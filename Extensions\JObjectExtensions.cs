using Newtonsoft.Json.Linq;

namespace IDC.Utilities.Extensions;

/// <summary>
/// Provides extension methods for JObject to enhance its functionality.
/// </summary>
/// <remarks>
/// This class contains utility methods for working with JObjects from Newtonsoft.Json.Linq.
///
/// Features:
/// - Safe property access with type conversion
/// - Deep object merging
/// - Property updates and cloning
/// - Null-safe operations
///
/// Example usage:
/// <example>
/// <code>
/// var json = new JObject
/// {
///     ["user"] = new JObject
///     {
///         ["name"] = "John <PERSON>",
///         ["age"] = 30,
///         ["scores"] = new JArray { 1, 2, 3 }
///     }
/// };
///
/// // Safe property access
/// string name = json.PropGet&lt;string&gt;(path: "user.name", defaultValue: "Unknown");
///
/// // Deep merge
/// var other = new JObject { ["user"] = new JObject { ["email"] = "<EMAIL>" } };
/// var merged = json.PropMerge(other: other);
/// </code>
/// </example>
///
/// > [!NOTE]
/// > All methods are designed to be null-safe and handle edge cases gracefully
///
/// > [!TIP]
/// > Use dot notation paths (e.g., "user.address.city") for deep property access
///
/// > [!IMPORTANT]
/// > Property paths are case-sensitive
/// </remarks>
/// <seealso href="https://www.newtonsoft.com/json/help/html/T_Newtonsoft_Json_Linq_JObject.htm">Newtonsoft JObject Documentation</seealso>
public static class JObjectExtensions
{
    /// <summary>
    /// Gets a value from a JObject using a dot notation path with type conversion and default value handling.
    /// </summary>
    /// <typeparam name="T">The type to convert the value to.</typeparam>
    /// <param name="source">
    /// The source JObject to get the value from.
    /// <see href="https://www.newtonsoft.com/json/help/html/T_Newtonsoft_Json_Linq_JObject.htm">JObject Documentation</see>
    /// </param>
    /// <param name="path">The dot notation path to the value (e.g., "user.address.city").</param>
    /// <param name="defaultValue">
    /// The default value to return if the path doesn't exist or conversion fails.
    /// </param>
    /// <returns>
    /// The value at the specified path converted to type <typeparamref name="T"/>, or defaultValue if not found.
    /// </returns>
    /// <remarks>
    /// This method provides safe property access with automatic type conversion and null handling.
    ///
    /// Key Features:
    /// - Deep property access using dot notation
    /// - Automatic type conversion
    /// - Null-safe operations
    /// - Default value fallback
    /// - Dictionary type support
    /// - JObject/JArray direct casting
    ///
    /// Example Request:
    /// <code>
    /// {
    ///   "user": {
    ///     "profile": {
    ///       "name": "John Doe",
    ///       "age": 30,
    ///       "settings": {
    ///         "theme": "dark",
    ///         "notifications": true
    ///       }
    ///     }
    ///   }
    /// }
    /// </code>
    ///
    /// <example>
    /// <code>
    /// var json = JObject.Parse(jsonString);
    ///
    /// // Basic usage
    /// string theme = json.PropGet&lt;string&gt;(
    ///     path: "user.profile.settings.theme",
    ///     defaultValue: "light"
    /// );
    ///
    /// // Nested dictionary
    /// var settings = json.PropGet&lt;Dictionary&lt;string, object&gt;&gt;(
    ///     path: "user.profile.settings",
    ///     defaultValue: new Dictionary&lt;string, object&gt;()
    /// );
    ///
    /// // Nullable value type
    /// int? age = json.PropGet&lt;int?&gt;(
    ///     path: "user.profile.age",
    ///     defaultValue: null
    /// );
    /// </code>
    /// </example>
    ///
    /// > [!NOTE]
    /// > The method uses SelectToken internally for deep path traversal
    ///
    /// > [!TIP]
    /// > Always provide a meaningful default value to handle missing properties gracefully
    ///
    /// > [!IMPORTANT]
    /// > Property paths are case-sensitive and must use dot notation
    /// </remarks>
    /// <seealso cref="PropGet{T}(JObject, string, string, bool)"/>
    /// <seealso href="https://www.newtonsoft.com/json/help/html/SelectToken.htm">SelectToken Documentation</seealso>
    public static T PropGet<T>(this JObject source, string path, T? defaultValue)
    {
        if (source == null || string.IsNullOrEmpty(value: path))
            return defaultValue ?? default!;

        try
        {
            var token = source.SelectToken(path: path);
            if (token == null)
                return defaultValue ?? default!;

            // Special handling for Dictionary types
            if (
                typeof(T).IsGenericType
                && typeof(T).GetGenericTypeDefinition() == typeof(Dictionary<,>)
            )
            {
                if (token is JObject jObject)
                    return jObject.ToObject<T>() ?? defaultValue ?? default!;
            }

            // Handle JObject and JArray direct casting
            if (
                (token is JObject || token is JArray) && typeof(T).IsAssignableFrom(token.GetType())
            )
                return (T)(object)token;

            return token.ToObject<T>() ?? defaultValue ?? default!;
        }
        catch
        {
            return defaultValue ?? default!;
        }
    }

    /// <summary>
    /// Gets a value from a JObject using a dot notation path with optional error handling.
    /// </summary>
    /// <typeparam name="T">The type to convert the value to.</typeparam>
    /// <param name="source">
    /// The source JObject to get the value from.
    /// <see href="https://www.newtonsoft.com/json/help/html/T_Newtonsoft_Json_Linq_JObject.htm">JObject Documentation</see>
    /// </param>
    /// <param name="path">The dot notation path to the value (e.g., "user.address.city").</param>
    /// <param name="onNullMessage">Custom error message when path is not found. Use {0} as path placeholder.</param>
    /// <param name="throwOnNull">When true, throws KeyNotFoundException if path not found.</param>
    /// <returns>
    /// The value at the specified path converted to type <typeparamref name="T"/>, or null if not found.
    /// </returns>
    /// <exception cref="KeyNotFoundException">
    /// Thrown when path is not found and throwOnNull is true, or when onNullMessage is provided.
    /// </exception>
    /// <remarks>
    /// This method provides safe property access with error handling capabilities and type conversion support.
    ///
    /// Key Features:
    /// - Deep property access using dot notation
    /// - Custom error messages
    /// - Optional exception throwing
    /// - Automatic type conversion
    /// - Dictionary type support
    /// - JObject/JArray direct casting
    ///
    /// Example Request:
    /// <code>
    /// {
    ///   "user": {
    ///     "profile": {
    ///       "name": "John Doe",
    ///       "age": 30,
    ///       "settings": {
    ///         "theme": "dark",
    ///         "notifications": true
    ///       }
    ///     }
    ///   }
    /// }
    /// </code>
    ///
    /// <example>
    /// <code>
    /// var json = JObject.Parse(jsonString);
    ///
    /// // Basic usage with error handling
    /// try
    /// {
    ///     string theme = json.PropGet&lt;string&gt;(
    ///         path: "user.profile.settings.theme",
    ///         onNullMessage: "Theme setting not found at {0}",
    ///         throwOnNull: true
    ///     );
    /// }
    /// catch (KeyNotFoundException ex)
    /// {
    ///     // Handle missing property
    /// }
    ///
    /// // Nullable value without throwing
    /// int? age = json.PropGet&lt;int?&gt;(
    ///     path: "user.profile.age",
    ///     onNullMessage: null,
    ///     throwOnNull: false
    /// );
    ///
    /// // Dictionary with custom message
    /// var settings = json.PropGet&lt;Dictionary&lt;string, object&gt;&gt;(
    ///     path: "user.profile.settings",
    ///     onNullMessage: "Settings not configured at {0}"
    /// );
    /// </code>
    /// </example>
    ///
    /// > [!NOTE]
    /// > Returns default(T) when path not found and throwOnNull is false
    ///
    /// > [!TIP]
    /// > Use null for onNullMessage to suppress error messages
    ///
    /// > [!IMPORTANT]
    /// > Property paths are case-sensitive and must use dot notation
    ///
    /// > [!CAUTION]
    /// > Ensure proper exception handling when throwOnNull is true
    /// </remarks>
    /// <seealso cref="PropGet{T}(JObject, string, T)"/>
    /// <seealso href="https://www.newtonsoft.com/json/help/html/SelectToken.htm">SelectToken Documentation</seealso>
    public static T? PropGet<T>(
        this JObject source,
        string path,
        string onNullMessage = "JSON property not found: {0}",
        bool throwOnNull = false
    )
    {
        if (source == null || string.IsNullOrEmpty(value: path))
            return default;

        try
        {
            var token = source.SelectToken(path: path);
            if (token == null && throwOnNull && !string.IsNullOrEmpty(value: onNullMessage))
                throw new KeyNotFoundException(
                    message: string.Format(
                        format: onNullMessage ?? "JSON property not found: {0}",
                        arg0: path
                    )
                );

            if (token == null)
                return default;

            // Special handling for Dictionary types
            if (
                typeof(T).IsGenericType
                && typeof(T).GetGenericTypeDefinition() == typeof(Dictionary<,>)
            )
            {
                if (token is JObject jObject)
                    return jObject.ToObject<T>();
            }

            // Handle JObject and JArray direct casting
            if (
                (token is JObject || token is JArray) && typeof(T).IsAssignableFrom(token.GetType())
            )
                return (T)(object)token;

            return token.ToObject<T>();
        }
        catch (KeyNotFoundException)
        {
            throw;
        }
        catch
        {
            return default;
        }
    }

    /// <summary>
    /// Merges two JObjects recursively with support for array merging and deep cloning.
    /// </summary>
    /// <param name="source">
    /// The source JObject to merge into.
    /// <see href="https://www.newtonsoft.com/json/help/html/T_Newtonsoft_Json_Linq_JObject.htm">JObject Documentation</see>
    /// </param>
    /// <param name="other">
    /// The other JObject to merge from.
    /// <see href="https://www.newtonsoft.com/json/help/html/T_Newtonsoft_Json_Linq_JObject.htm">JObject Documentation</see>
    /// </param>
    /// <param name="mergeArrays">When true, arrays are merged using union operation. When false, arrays are replaced.</param>
    /// <returns>
    /// A new <see cref="JObject"/> containing the merged result with deep cloning of all values.
    /// </returns>
    /// <remarks>
    /// This method performs a deep merge of two JObjects with configurable array handling.
    ///
    /// Key Features:
    /// - Deep recursive merging
    /// - Configurable array merging strategy (union or replace)
    /// - Deep cloning of all values
    /// - Null-safe operations
    /// - Preserves source object structure
    ///
    /// Example Request:
    /// <code>
    /// // Source object
    /// {
    ///   "user": {
    ///     "name": "John",
    ///     "scores": [1, 2],
    ///     "settings": {
    ///       "theme": "light"
    ///     }
    ///   }
    /// }
    ///
    /// // Other object
    /// {
    ///   "user": {
    ///     "age": 30,
    ///     "scores": [3, 4],
    ///     "settings": {
    ///       "notifications": true
    ///     }
    ///   }
    /// }
    /// </code>
    ///
    /// <example>
    /// <code>
    /// // Basic merge with array replacement
    /// var result1 = source.PropMerge(
    ///     other: other,
    ///     mergeArrays: false
    /// );
    /// /* Result1:
    /// {
    ///   "user": {
    ///     "name": "John",
    ///     "age": 30,
    ///     "scores": [3, 4],
    ///     "settings": {
    ///       "theme": "light",
    ///       "notifications": true
    ///     }
    ///   }
    /// }
    /// */
    ///
    /// // Merge with array union
    /// var result2 = source.PropMerge(
    ///     other: other,
    ///     mergeArrays: true
    /// );
    /// /* Result2:
    /// {
    ///   "user": {
    ///     "name": "John",
    ///     "age": 30,
    ///     "scores": [1, 2, 3, 4],
    ///     "settings": {
    ///       "theme": "light",
    ///       "notifications": true
    ///     }
    ///   }
    /// }
    /// */
    /// </code>
    /// </example>
    ///
    /// > [!NOTE]
    /// > All values are deep cloned to prevent reference sharing between objects
    ///
    /// > [!TIP]
    /// > Use mergeArrays=true when you want to combine array values instead of replacing them
    ///
    /// > [!IMPORTANT]
    /// > Properties from 'other' will override properties from 'source' if they exist at the same path
    ///
    /// > [!WARNING]
    /// > Array merging only works for top-level array elements, nested arrays in objects are not merged
    /// </remarks>
    /// <seealso href="https://www.newtonsoft.com/json/help/html/M_Newtonsoft_Json_Linq_JToken_DeepClone.htm">JToken.DeepClone Method</seealso>
    /// <seealso href="https://www.newtonsoft.com/json/help/html/M_Newtonsoft_Json_Linq_JArray_Union.htm">JArray.Union Method</seealso>
    /// <seealso href="https://www.newtonsoft.com/json/help/html/DeepClone.htm">DeepClone Documentation</seealso>
    public static JObject PropMerge(this JObject source, JObject other, bool mergeArrays = false)
    {
        var result = new JObject();
        source
            ?.Properties()
            .ToList()
            .ForEach(action: prop =>
                result.Add(propertyName: prop.Name, value: prop.Value?.DeepClone())
            );

        if (other == null)
            return result;

        foreach (var kvp in other)
        {
            if (!result.ContainsKey(propertyName: kvp.Key))
            {
                result.Add(propertyName: kvp.Key, value: kvp.Value?.DeepClone());
                continue;
            }

            if (result[kvp.Key] is JObject sourceObj && kvp.Value is JObject otherObj)
                result[kvp.Key] = sourceObj.PropMerge(other: otherObj, mergeArrays: mergeArrays);
            else if (
                mergeArrays
                && result[kvp.Key] is JArray sourceArray
                && kvp.Value is JArray otherArray
            )
                result[kvp.Key] = new JArray(content: sourceArray.Union(second: otherArray));
            else
                result[kvp.Key] = kvp.Value?.DeepClone();
        }

        return result;
    }

    /// <summary>
    /// Removes properties from a JObject based on a predicate function with recursive capability.
    /// </summary>
    /// <param name="source">The source JObject to remove properties from.</param>
    /// <param name="predicate">A function that determines whether a property should be removed.</param>
    /// <param name="recursive">Whether to recursively remove properties from nested objects and arrays.</param>
    /// <returns>A new JObject with properties removed according to the predicate.</returns>
    /// <remarks>
    /// This method provides deep property removal with predicate-based filtering and recursive capabilities.
    ///
    /// Key Features:
    /// - Predicate-based property filtering
    /// - Optional recursive removal
    /// - Deep cloning of retained properties
    /// - Null-safe operations
    /// - Preserves object structure
    /// - Handles nested arrays and objects
    ///
    /// Example Request:
    /// <code>
    /// {
    ///   "user": {
    ///     "name": "John Doe",
    ///     "age": 30,
    ///     "sensitive": "private-data",
    ///     "scores": [
    ///       { "subject": "math", "sensitive": true, "value": 95 },
    ///       { "subject": "english", "sensitive": false, "value": 88 }
    ///     ],
    ///     "settings": {
    ///       "theme": "dark",
    ///       "sensitive": true,
    ///       "token": "secret-token"
    ///     }
    ///   }
    /// }
    /// </code>
    ///
    /// <example>
    /// <code>
    /// // Remove all properties named "sensitive" or "token"
    /// var result1 = json.PropRemove(
    ///     predicate: kvp => kvp.Key is "sensitive" or "token",
    ///     recursive: true
    /// );
    ///
    /// // Remove properties with null values
    /// var result2 = json.PropRemove(
    ///     predicate: kvp => kvp.Value?.Type == JTokenType.Null,
    ///     recursive: true
    /// );
    ///
    /// // Remove properties by value condition
    /// var result3 = json.PropRemove(
    ///     predicate: kvp => kvp.Value?.Type == JTokenType.Boolean &amp;&amp;
    ///                       kvp.Value.Value&lt;bool&gt;() == true,
    ///     recursive: true
    /// );
    /// </code>
    /// </example>
    ///
    /// <para>
    /// <b>Notes:</b>
    /// <list type="bullet">
    /// <item><description>Returns an empty JObject if source is null</description></item>
    /// <item><description>Use recursive=true to ensure complete removal across all nested structures</description></item>
    /// <item><description>The predicate receives KeyValuePair&lt;string, JToken?&gt; for maximum flexibility</description></item>
    /// <item><description>Recursive removal affects all nested objects and array elements</description></item>
    /// <item><description>Original object structure is preserved even if all properties are removed</description></item>
    /// </list>
    /// </para>
    /// </remarks>
    /// <seealso href="https://www.newtonsoft.com/json/help/html/M_Newtonsoft_Json_Linq_JToken_DeepClone.htm">JToken.DeepClone Method</seealso>
    /// <seealso href="https://learn.microsoft.com/en-us/dotnet/api/system.collections.generic.keyvaluepair-2">KeyValuePair Documentation</seealso>
    /// <seealso href="https://www.newtonsoft.com/json/help/html/T_Newtonsoft_Json_Linq_JTokenType.htm">JTokenType Documentation</seealso>
    public static JObject PropRemove(
        this JObject source,
        Func<KeyValuePair<string, JToken?>, bool> predicate,
        bool recursive = true
    )
    {
        if (source == null)
            return [];

        var result = new JObject();
        foreach (var kvp in source)
        {
            if (predicate(arg: kvp))
                continue;

            if (recursive && kvp.Value is JObject childObject)
                result.Add(
                    propertyName: kvp.Key,
                    value: childObject.PropRemove(predicate: predicate, recursive: true)
                );
            else if (recursive && kvp.Value is JArray array)
                result.Add(
                    propertyName: kvp.Key,
                    value: new JArray(
                        content: array.Select(selector: item =>
                            item is JObject obj
                                ? obj.PropRemove(predicate: predicate, recursive: true)
                                : item.DeepClone()
                        )
                    )
                );
            else
                result.Add(propertyName: kvp.Key, value: kvp.Value?.DeepClone());
        }

        return result;
    }

    /// <summary>
    /// Updates multiple properties in a JObject using a dictionary of path-value pairs with dot notation support.
    /// </summary>
    /// <param name="source">
    /// The source JObject to update.
    /// <see href="https://www.newtonsoft.com/json/help/html/T_Newtonsoft_Json_Linq_JObject.htm">JObject Documentation</see>
    /// </param>
    /// <param name="updates">
    /// Dictionary containing path-value pairs where path uses dot notation.
    /// <see href="https://learn.microsoft.com/dotnet/api/system.collections.generic.dictionary-2">Dictionary Documentation</see>
    /// </param>
    /// <returns>
    /// A new <see cref="JObject"/> containing the updated properties with preserved structure.
    /// </returns>
    /// <remarks>
    /// Provides deep property updates using dot notation paths with automatic object creation for missing paths.
    ///
    /// Key Features:
    /// - Deep path navigation using dot notation
    /// - Automatic creation of missing intermediate objects
    /// - Deep cloning of existing values
    /// - Null-safe operations
    /// - Preserves existing structure
    /// - Supports any serializable value type
    ///
    /// Example Request:
    /// <code>
    /// {
    ///   "user": {
    ///     "profile": {
    ///       "name": "John Doe",
    ///       "contact": {
    ///         "email": "<EMAIL>"
    ///       }
    ///     },
    ///     "settings": {
    ///       "theme": "light",
    ///       "notifications": {
    ///         "email": true
    ///       }
    ///     }
    ///   }
    /// }
    /// </code>
    ///
    /// <example>
    /// <code>
    /// var updates = new Dictionary&lt;string, object?&gt;
    /// {
    ///     ["user.profile.name"] = "Jane Doe",
    ///     ["user.profile.contact.phone"] = "+1234567890",
    ///     ["user.settings.notifications.push"] = true,
    ///     ["user.preferences.language"] = "en-US"
    /// };
    ///
    /// var result = sourceJson.PropUpdate(updates: updates);
    /// /* Result:
    /// {
    ///   "user": {
    ///     "profile": {
    ///       "name": "Jane Doe",
    ///       "contact": {
    ///         "email": "<EMAIL>",
    ///         "phone": "+1234567890"
    ///       }
    ///     },
    ///     "settings": {
    ///       "theme": "light",
    ///       "notifications": {
    ///         "email": true,
    ///         "push": true
    ///       }
    ///     },
    ///     "preferences": {
    ///       "language": "en-US"
    ///     }
    ///   }
    /// }
    /// */
    /// </code>
    /// </example>
    ///
    /// > [!NOTE]
    /// > Returns an empty JObject if source is null
    ///
    /// > [!TIP]
    /// > Use dot notation for deeply nested updates: "parent.child.grandchild"
    ///
    /// > [!IMPORTANT]
    /// > Missing intermediate objects are automatically created
    ///
    /// > [!CAUTION]
    /// > Existing values at update paths will be completely replaced
    ///
    /// > [!WARNING]
    /// > Array indices are not supported in dot notation paths
    /// </remarks>
    /// <seealso href="https://www.newtonsoft.com/json/help/html/SerializingJSON.htm">JSON Serialization</seealso>
    public static JObject PropUpdate(this JObject source, Dictionary<string, object?> updates)
    {
        if (source == null)
            return [];

        var result = source.DeepClone() as JObject ?? [];

        foreach (var update in updates)
        {
            var paths = update.Key.Split(separator: '.');
            var current = result;

            for (int i = 0; i < paths.Length - 1; i++)
            {
                if (current[paths[i]] is not JObject)
                    current[paths[i]] = new JObject();

                current = (JObject)current[paths[i]]!;
            }

            current[paths[^1]] = update.Value != null ? JToken.FromObject(o: update.Value) : null;
        }

        return result;
    }

    /// <summary>
    /// Upserts (updates or inserts) a value in a JObject using a dot notation path.
    /// </summary>
    /// <param name="source">
    /// The source JObject to update.
    /// <see href="https://www.newtonsoft.com/json/help/html/T_Newtonsoft_Json_Linq_JObject.htm">JObject Documentation</see>
    /// </param>
    /// <param name="path">
    /// The dot notation path to the value (e.g., "user.address.city").
    /// <see href="https://www.newtonsoft.com/json/help/html/T_System_String.htm">String Documentation</see>
    /// </param>
    /// <param name="value">
    /// The value to upsert. Can be any serializable object or null.
    /// <see href="https://learn.microsoft.com/dotnet/csharp/programming-guide/types/boxing-and-unboxing">Object Type Documentation</see>
    /// </param>
    /// <returns>
    /// A new <see cref="JObject"/> with the upserted value at the specified path.
    /// </returns>
    /// <exception cref="ArgumentNullException">
    /// Thrown when path is null or empty.
    /// <see href="https://learn.microsoft.com/dotnet/api/system.argumentnullexception">ArgumentNullException Documentation</see>
    /// </exception>
    /// <remarks>
    /// Provides atomic upsert operations for a single property using dot notation path.
    ///
    /// Key Features:
    /// - Single property upsert with dot notation
    /// - Automatic creation of missing intermediate objects
    /// - Deep cloning of existing structure
    /// - Null-safe operations
    /// - Preserves existing structure
    /// - Supports any serializable value type
    ///
    /// Example Request:
    /// <code>
    /// {
    ///   "user": {
    ///     "profile": {
    ///       "name": "Jane Smith"
    ///     }
    ///   }
    /// }
    /// </code>
    ///
    /// <example>
    /// <code>
    /// var source = new JObject
    /// {
    ///     ["user"] = new JObject
    ///     {
    ///         ["profile"] = new JObject
    ///         {
    ///             ["name"] = "Jane Smith"
    ///         }
    ///     }
    /// };
    ///
    /// // Update existing property
    /// var result1 = source.PropUpsert(
    ///     path: "user.profile.name",
    ///     value: "John Doe"
    /// );
    ///
    /// // Create new nested property
    /// var result2 = source.PropUpsert(
    ///     path: "user.settings.theme",
    ///     value: "dark"
    /// );
    ///
    /// // Set complex object
    /// var result3 = source.PropUpsert(
    ///     path: "user.contact",
    ///     value: new
    ///     {
    ///         email = "<EMAIL>",
    ///         phone = "+1234567890"
    ///     }
    /// );
    ///
    /// /* Result for result3:
    /// {
    ///   "user": {
    ///     "profile": {
    ///       "name": "John Doe"
    ///     },
    ///     "settings": {
    ///       "theme": "dark"
    ///     },
    ///     "contact": {
    ///       "email": "<EMAIL>",
    ///       "phone": "+1234567890"
    ///     }
    ///   }
    /// }
    /// */
    /// </code>
    /// </example>
    ///
    /// > [!NOTE]
    /// > Creates a new JObject instance for thread-safety
    ///
    /// > [!TIP]
    /// > Use null value to explicitly set property to null
    ///
    /// > [!IMPORTANT]
    /// > Path segments are processed sequentially from left to right
    ///
    /// > [!CAUTION]
    /// > Existing value at target path will be completely replaced
    ///
    /// > [!WARNING]
    /// > Array operations are not supported in path notation
    /// </remarks>
    /// <seealso cref="PropUpsert(JObject, Dictionary{string, object})"/>
    /// <seealso href="https://www.newtonsoft.com/json/help/html/M_Newtonsoft_Json_Linq_JToken_DeepClone.htm">JToken.DeepClone Method</seealso>
    /// <seealso href="https://www.newtonsoft.com/json/help/html/SerializingJSON.htm">JSON Serialization Guide</seealso>
    public static JObject PropUpsert(this JObject source, string path, object? value)
    {
        if (string.IsNullOrEmpty(value: path))
            throw new ArgumentNullException(
                paramName: nameof(path),
                message: "Path cannot be null or empty"
            );

        var result = source?.DeepClone() as JObject ?? [];
        var paths = path.Split(separator: '.');
        var current = result;

        for (int i = 0; i < paths.Length - 1; i++)
        {
            if (current[paths[i]] is not JObject)
                current[paths[i]] = new JObject();

            current = (JObject)current[paths[i]]!;
        }

        var lastPath = paths[^1];
        current[lastPath] = value != null ? JToken.FromObject(o: value) : null;

        return result;
    }

    /// <summary>
    /// Performs batch upsert operations on a JObject using a dictionary of path-value pairs.
    /// </summary>
    /// <param name="source">
    /// The source JObject to update.
    /// <see href="https://www.newtonsoft.com/json/help/html/T_Newtonsoft_Json_Linq_JObject.htm">JObject Documentation</see>
    /// </param>
    /// <param name="updates">
    /// Dictionary containing path-value pairs for batch updates.
    /// <see href="https://learn.microsoft.com/dotnet/api/system.collections.generic.dictionary-2">Dictionary Documentation</see>
    /// </param>
    /// <returns>
    /// A new <see cref="JObject"/> containing all updated properties.
    /// </returns>
    /// <exception cref="ArgumentNullException">
    /// Thrown when updates dictionary is null.
    /// <see href="https://learn.microsoft.com/dotnet/api/system.argumentnullexception">ArgumentNullException Documentation</see>
    /// </exception>
    /// <remarks>
    /// Provides atomic batch update operations using dot notation paths for multiple properties simultaneously.
    ///
    /// Key Features:
    /// - Batch processing of multiple updates
    /// - Atomic operation (all updates succeed or none)
    /// - Deep cloning of source object
    /// - Preserves existing structure
    /// - Thread-safe operation
    /// - Supports nullable values
    ///
    /// Example Request:
    /// <code>
    /// {
    ///   "user": {
    ///     "profile": {
    ///       "name": "John Smith",
    ///       "contact": {
    ///         "email": "<EMAIL>"
    ///       }
    ///     },
    ///     "settings": {
    ///       "theme": "light",
    ///       "notifications": {
    ///         "email": true
    ///       }
    ///     }
    ///   }
    /// }
    /// </code>
    ///
    /// <example>
    /// <code>
    /// var sourceJson = new JObject
    /// {
    ///     ["user"] = new JObject
    ///     {
    ///         ["profile"] = new JObject
    ///         {
    ///             ["name"] = "John Smith",
    ///             ["contact"] = new JObject
    ///             {
    ///                 ["email"] = "<EMAIL>"
    ///             }
    ///         },
    ///         ["settings"] = new JObject
    ///         {
    ///             ["theme"] = "light",
    ///             ["notifications"] = new JObject
    ///             {
    ///                 ["email"] = true
    ///             }
    ///         }
    ///     }
    /// };
    ///
    /// var updates = new Dictionary&lt;string, object?&gt;
    /// {
    ///     ["user.profile.name"] = "Jane Doe",
    ///     ["user.profile.contact.phone"] = "+1234567890",
    ///     ["user.settings.notifications.push"] = true,
    ///     ["user.preferences.language"] = "en-US"
    /// };
    ///
    /// var result = sourceJson.PropUpdate(updates: updates);
    /// /* Result:
    /// {
    ///   "user": {
    ///     "profile": {
    ///       "name": "Jane Doe",
    ///       "contact": {
    ///         "email": "<EMAIL>",
    ///         "phone": "+1234567890"
    ///       }
    ///     },
    ///     "settings": {
    ///       "theme": "light",
    ///       "notifications": {
    ///         "email": true,
    ///         "push": true
    ///       }
    ///     },
    ///     "preferences": {
    ///       "language": "en-US"
    ///     }
    ///   }
    /// }
    /// */
    /// </code>
    /// </example>
    ///
    /// > [!NOTE]
    /// > Returns an empty JObject if source is null
    ///
    /// > [!TIP]
    /// > Use dot notation for deeply nested updates: "parent.child.grandchild"
    ///
    /// > [!IMPORTANT]
    /// > Missing intermediate objects are automatically created
    ///
    /// > [!CAUTION]
    /// > Existing values at update paths will be completely replaced
    ///
    /// > [!WARNING]
    /// > Array indices are not supported in dot notation paths
    /// </remarks>
    /// <seealso href="https://www.newtonsoft.com/json/help/html/M_Newtonsoft_Json_Linq_JToken_DeepClone.htm">JToken.DeepClone Method</seealso>
    /// <seealso cref="PropUpsert"/>
    /// <seealso href="https://www.newtonsoft.com/json/help/html/SerializingJSON.htm">JSON Serialization Guide</seealso>
    public static JObject PropUpsert(this JObject source, Dictionary<string, object?> updates)
    {
        if (updates == null)
            throw new ArgumentNullException(
                paramName: nameof(updates),
                message: "Updates dictionary cannot be null"
            );

        var result = source?.DeepClone() as JObject ?? [];

        foreach (var update in updates)
            result = result.PropUpsert(path: update.Key, value: update.Value);

        return result;
    }

    /// <summary>
    /// Clones a property from one path to another within a JObject using dot notation.
    /// </summary>
    /// <param name="source">
    /// The source JObject to clone from.
    /// <see href="https://www.newtonsoft.com/json/help/html/T_Newtonsoft_Json_Linq_JObject.htm">JObject Documentation</see>
    /// </param>
    /// <param name="sourcePath">
    /// The dot notation path of the property to clone (e.g., "user.profile.name").
    /// <see href="https://www.newtonsoft.com/json/help/html/T_System_String.htm">String Documentation</see>
    /// </param>
    /// <param name="destinationPath">
    /// The dot notation path where the cloned property will be placed.
    /// <see href="https://www.newtonsoft.com/json/help/html/T_System_String.htm">String Documentation</see>
    /// </param>
    /// <returns>
    /// A new <see cref="JObject"/> with the cloned property at the specified destination path.
    /// <see href="https://www.newtonsoft.com/json/help/html/T_Newtonsoft_Json_Linq_JObject.htm">JObject Documentation</see>
    /// </returns>
    /// <exception cref="ArgumentNullException">
    /// Thrown when sourcePath or destinationPath is null or empty.
    /// <see href="https://learn.microsoft.com/dotnet/api/system.argumentnullexception">ArgumentNullException Documentation</see>
    /// </exception>
    /// <remarks>
    /// Provides deep cloning functionality for JObject properties with path-based navigation.
    ///
    /// Key Features:
    /// - Deep property cloning
    /// - Dot notation path support
    /// - Automatic creation of missing paths
    /// - Preserves original structure
    /// - Thread-safe operation
    /// - Null-safe handling
    ///
    /// Example Request:
    /// <code>
    /// {
    ///   "source": {
    ///     "user": {
    ///       "profile": {
    ///         "name": "John Doe",
    ///         "settings": {
    ///           "theme": "dark",
    ///           "notifications": true
    ///         }
    ///       }
    ///     }
    ///   }
    /// }
    /// </code>
    ///
    /// <example>
    /// <code>
    /// var json = new JObject
    /// {
    ///     ["user"] = new JObject
    ///     {
    ///         ["profile"] = new JObject
    ///         {
    ///             ["name"] = "John Doe",
    ///             ["settings"] = new JObject
    ///             {
    ///                 ["theme"] = "dark",
    ///                 ["notifications"] = true
    ///             }
    ///         }
    ///     }
    /// };
    ///
    /// // Clone entire profile
    /// var result1 = json.PropClone(
    ///     sourcePath: "user.profile",
    ///     destinationPath: "backup.userProfile"
    /// );
    ///
    /// // Clone specific setting
    /// var result2 = json.PropClone(
    ///     sourcePath: "user.profile.settings.theme",
    ///     destinationPath: "preferences.appearance.theme"
    /// );
    ///
    /// // Clone to existing path (overwrites destination)
    /// var result3 = json.PropClone(
    ///     sourcePath: "user.profile.name",
    ///     destinationPath: "user.displayName"
    /// );
    ///
    /// /* Result for result1:
    /// {
    ///   "user": {
    ///     "profile": {
    ///       "name": "John Doe",
    ///       "settings": {
    ///         "theme": "dark",
    ///         "notifications": true
    ///       }
    ///     }
    ///   },
    ///   "backup": {
    ///     "userProfile": {
    ///       "name": "John Doe",
    ///       "settings": {
    ///         "theme": "dark",
    ///         "notifications": true
    ///       }
    ///     }
    ///   }
    /// }
    /// */
    /// </code>
    /// </example>
    ///
    /// > [!NOTE]
    /// > Returns original JObject if source path doesn't exist
    ///
    /// > [!TIP]
    /// > Use this method to create backups of complex nested objects
    ///
    /// > [!IMPORTANT]
    /// > Performs deep cloning of all nested objects
    ///
    /// > [!CAUTION]
    /// > Existing values at destination path will be overwritten
    ///
    /// > [!WARNING]
    /// > Array indices are not supported in path notation
    /// </remarks>
    /// <seealso href="https://www.newtonsoft.com/json/help/html/M_Newtonsoft_Json_Linq_JToken_DeepClone.htm">JToken.DeepClone Method</seealso>
    /// <seealso cref="PropUpsert"/>
    /// <seealso href="https://www.newtonsoft.com/json/help/html/SerializingJSON.htm">JSON Serialization Guide</seealso>
    public static JObject PropClone(this JObject source, string sourcePath, string destinationPath)
    {
        if (string.IsNullOrEmpty(value: sourcePath))
            throw new ArgumentNullException(
                paramName: nameof(sourcePath),
                message: "Source path cannot be null or empty"
            );

        if (string.IsNullOrEmpty(value: destinationPath))
            throw new ArgumentNullException(
                paramName: nameof(destinationPath),
                message: "Destination path cannot be null or empty"
            );

        var result = source?.DeepClone() as JObject ?? [];

        // Get the value from source path
        var sourceValue = result.PropGet<JToken>(path: sourcePath);
        if (sourceValue == null)
            return result;

        // Clone the value to destination path
        result = result.PropUpsert(path: destinationPath, value: sourceValue.DeepClone());

        return result;
    }
}

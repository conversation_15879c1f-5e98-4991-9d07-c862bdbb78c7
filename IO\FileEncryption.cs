using System.Text;
using IDC.Utilities.Extensions;

namespace IDC.Utilities.IO;

/// <summary>
/// Provides file encryption and decryption functionality using various algorithms including AES, DES, RSA, and Base64.
/// </summary>
/// <remarks>
/// This class handles file-based encryption operations with support for different encryption methods.
/// Includes logging capabilities for error tracking and debugging purposes.
///
/// Example usage:
/// <code>
/// var encryption = new FileEncryption(destFileLocation: "encrypted.txt");
/// await encryption.AESFileEncryption(
///     sourceFileLocation: "source.txt",
///     key: "MySecretKey1234",
///     iv: "InitVector123456"
/// );
/// </code>
/// </remarks>
/// <param name="destFileLocation">The destination file path where the encrypted/decrypted content will be saved.</param>
/// <param name="systemLogging">Optional logging instance for error tracking. If not provided, errors will be thrown without logging.</param>
/// <seealso href="https://learn.microsoft.com/en-us/dotnet/api/system.security.cryptography">Cryptography Documentation</seealso>
public class FileEncryption(SystemLogging? systemLogging = default)
{
    #region UTILS
    private const int CONST_FIX_BYTE_LENGTH = 0x10;
    private const char CONST_PAD_CHAR = '#';

    /// <summary>
    /// Ensures a string is exactly 16 characters long by truncating or padding with '#' characters.
    /// </summary>
    /// <remarks>
    /// This method is used to prepare keys and initialization vectors for encryption algorithms that require fixed-length inputs.
    /// If the input string is longer than 16 characters, it will be truncated. If shorter, it will be padded with '#' characters.
    ///
    /// Example usage:
    /// <code>
    /// string? result = Make16Chars(data: "Hello"); // Returns "Hello###########"
    /// string? result = Make16Chars(data: "ThisIsAVeryLongString"); // Returns "ThisIsAVeryLong"
    /// </code>
    /// </remarks>
    /// <param name="data">The input string to be processed. Can be null.</param>
    /// <returns>A 16-character string, or null if the input is null.</returns>
    private static string? Make16Chars(string? data) =>
        data?[..CONST_FIX_BYTE_LENGTH].PadRight(
            totalWidth: CONST_FIX_BYTE_LENGTH,
            paddingChar: CONST_PAD_CHAR
        );

    /// <summary>
    /// Converts a string into a byte array with fixed length of 16 bytes.
    /// </summary>
    /// <remarks>
    /// This method processes a string into a byte array suitable for cryptographic operations.
    /// The string is first normalized to 16 characters, then split into individual characters,
    /// and finally converted to bytes.
    ///
    /// > [!IMPORTANT]
    /// > The method assumes each character can be converted to a single byte. Extended ASCII
    /// > or Unicode characters may not convert correctly.
    ///
    /// Example usage:
    /// <code>
    /// byte[]? bytes = ConvertToBytes(data: "MyKey123"); // Returns byte array of length 16
    /// </code>
    /// </remarks>
    /// <param name="data">The input string to convert. Can be null.</param>
    /// <returns>A byte array of length 16, or null if the input is null.</returns>
    private static byte[]? ConvertToBytes(string? data) =>
        data
            ?[..CONST_FIX_BYTE_LENGTH].PadRight(
                totalWidth: CONST_FIX_BYTE_LENGTH,
                paddingChar: CONST_PAD_CHAR
            )
            .Split(separator: "")
            .Select(selector: x => x.CastToByte() ?? new byte())
            .ToArray();
    #endregion UTILS

    #region AES
    /// <summary>
    /// Performs AES encryption on a file with enhanced performance using buffered streaming.
    /// </summary>
    /// <remarks>
    /// This method implements AES (Advanced Encryption Standard) encryption for files using a buffered streaming approach
    /// to efficiently handle large files. It processes the file in chunks to minimize memory usage.
    ///
    /// > [!IMPORTANT]
    /// > The key will be automatically padded or truncated to 16 characters using the '#' character.
    /// > The same key must be used for decryption.
    ///
    /// > [!NOTE]
    /// > The buffer size affects performance. Larger buffers may improve speed but use more memory.
    /// > The default size of 81920 bytes (80KB) is optimized for most scenarios.
    ///
    /// > [!CAUTION]
    /// > Ensure proper key management and storage. The encrypted file can only be decrypted with the same key and IV.
    ///
    /// Example usage:
    /// <code>
    /// await PerformAESFileEncryption(
    ///     sourceFileLocation: "C:/Data/source.txt",
    ///     destFileLocation: "C:/Data/encrypted.bin",
    ///     key: "MySecretKey1234",
    ///     iv: "InitVector123456",
    ///     bufferSize: 81920
    /// );
    /// </code>
    /// </remarks>
    /// <param name="sourceFileLocation">The full path to the source file to be encrypted.</param>
    /// <param name="destFileLocation">The full path where the encrypted file will be saved.</param>
    /// <param name="key">The encryption key (will be adjusted to 16 characters).</param>
    /// <param name="iv">Optional initialization vector (will be adjusted to 16 characters). If not provided, defaults to null.</param>
    /// <param name="bufferSize">The size of the buffer in bytes. Defaults to 81920 (80KB).</param>
    /// <param name="cancellationToken">Optional cancellation token to cancel the operation.</param>
    /// <returns>A <see href="https://learn.microsoft.com/en-us/dotnet/api/system.threading.tasks.task">Task</see> representing the asynchronous encryption operation.</returns>
    /// <exception cref="IOException">Thrown when file access errors occur.</exception>
    /// <exception cref="System.Security.Cryptography.CryptographicException">Thrown when encryption fails.</exception>
    /// <exception cref="ArgumentNullException">Thrown when required parameters are null.</exception>
    /// <seealso href="https://learn.microsoft.com/en-us/dotnet/api/system.security.cryptography.aes">AES Encryption</seealso>
    public async Task PerformAESFileEncryption(
        string sourceFileLocation,
        string destFileLocation,
        string key,
        string? iv = default,
        int bufferSize = 81920,
        CancellationToken cancellationToken = default
    )
    {
        try
        {
            using var sourceStream = new FileStream(
                path: sourceFileLocation,
                mode: FileMode.Open,
                access: FileAccess.Read,
                share: FileShare.Read,
                bufferSize: bufferSize
            );
            using var destinationStream = new FileStream(
                path: destFileLocation,
                mode: FileMode.Create,
                access: FileAccess.Write,
                share: FileShare.None,
                bufferSize: bufferSize
            );

            int bytesRead;

            while (
                (
                    bytesRead = await sourceStream.ReadAsync(
                        buffer: new byte[bufferSize],
                        cancellationToken: cancellationToken
                    )
                ) > 0
            )
                await destinationStream.WriteAsync(
                    buffer: Convert.FromBase64String(
                        Encoding
                            .UTF8.GetString(
                                bytes: [.. (new byte[bufferSize]).Take(count: bytesRead)]
                            )
                            .AsAESEncrypt(key: Make16Chars(data: key)!, iv: Make16Chars(data: iv))!
                    ),
                    cancellationToken: cancellationToken
                );
        }
        catch (Exception ex)
        {
            systemLogging?.LogError(exception: ex);
            throw;
        }
    }

    public async Task PerformAESFileDecryption(
        string sourceFileLocation,
        string destFileLocation,
        string key,
        string? iv = default,
        int bufferSize = 81920,
        CancellationToken cancellationToken = default
    )
    {
        try
        {
            using var sourceStream = new FileStream(
                path: sourceFileLocation,
                mode: FileMode.Open,
                access: FileAccess.Read,
                share: FileShare.Read,
                bufferSize: bufferSize
            );
            using var destinationStream = new FileStream(
                path: destFileLocation,
                mode: FileMode.Create,
                access: FileAccess.Write,
                share: FileShare.None,
                bufferSize: bufferSize
            );

            int bytesRead;

            while (
                (
                    bytesRead = await sourceStream.ReadAsync(
                        buffer: new byte[bufferSize],
                        cancellationToken: cancellationToken
                    )
                ) > 0
            )
                await destinationStream.WriteAsync(
                    buffer: Convert.FromBase64String(
                        Encoding
                            .UTF8.GetString(
                                bytes: [.. (new byte[bufferSize]).Take(count: bytesRead)]
                            )
                            .AsAESDecrypt(key: Make16Chars(data: key)!, iv: Make16Chars(data: iv))!
                    ),
                    cancellationToken: cancellationToken
                );
        }
        catch (Exception ex)
        {
            systemLogging?.LogError(exception: ex);
            throw;
        }
    }

    public async Task PerformAESStringEncryption(
        string content,
        string destFileLocation,
        string key,
        string? iv = default,
        CancellationToken cancellationToken = default
    )
    {
        try
        {
            using var destinationStream = new FileStream(
                path: destFileLocation,
                mode: FileMode.Create,
                access: FileAccess.Write,
                share: FileShare.None
            );

            await destinationStream.WriteAsync(
                buffer: Convert.FromBase64String(
                    content.AsAESEncrypt(key: Make16Chars(data: key)!, iv: Make16Chars(data: iv))!
                ),
                cancellationToken: cancellationToken
            );
        }
        catch (Exception ex)
        {
            systemLogging?.LogError(exception: ex);
            throw;
        }
    }

    public async Task PerformAESStringDecryption(
        string content,
        string destFileLocation,
        string key,
        string? iv = default,
        CancellationToken cancellationToken = default
    )
    {
        try
        {
            using var destinationStream = new FileStream(
                path: destFileLocation,
                mode: FileMode.Create,
                access: FileAccess.Write,
                share: FileShare.None
            );

            await destinationStream.WriteAsync(
                buffer: Convert.FromBase64String(
                    content.AsAESDecrypt(key: Make16Chars(data: key)!, iv: Make16Chars(data: iv))!
                ),
                cancellationToken: cancellationToken
            );
        }
        catch (Exception ex)
        {
            systemLogging?.LogError(exception: ex);
            throw;
        }
    }
    #endregion AES

    #region DES
    public async Task PerformDESFileEncryption(
        string sourceFileLocation,
        string destFileLocation,
        string key,
        string? iv = default,
        int bufferSize = 81920,
        CancellationToken cancellationToken = default
    )
    {
        try
        {
            using var sourceStream = new FileStream(
                path: sourceFileLocation,
                mode: FileMode.Open,
                access: FileAccess.Read,
                share: FileShare.Read,
                bufferSize: bufferSize
            );
            using var destinationStream = new FileStream(
                path: destFileLocation,
                mode: FileMode.Create,
                access: FileAccess.Write,
                share: FileShare.None,
                bufferSize: bufferSize
            );

            int bytesRead;
            while (
                (
                    bytesRead = await sourceStream.ReadAsync(
                        buffer: new byte[bufferSize],
                        cancellationToken: cancellationToken
                    )
                ) > 0
            )
                await destinationStream.WriteAsync(
                    buffer: Convert.FromBase64String(
                        Encoding
                            .UTF8.GetString(
                                bytes: [.. (new byte[bufferSize]).Take(count: bytesRead)]
                            )
                            .AsDESEncrypt(
                                key: ConvertToBytes(data: Make16Chars(data: key))!,
                                iv: ConvertToBytes(data: Make16Chars(data: iv))
                            )!
                    ),
                    cancellationToken: cancellationToken
                );
        }
        catch (Exception ex)
        {
            systemLogging?.LogError(exception: ex);
            throw;
        }
    }

    public async Task PerformDESFileDecryption(
        string sourceFileLocation,
        string destFileLocation,
        string key,
        string? iv = default,
        int bufferSize = 81920,
        CancellationToken cancellationToken = default
    )
    {
        try
        {
            using var sourceStream = new FileStream(
                path: sourceFileLocation,
                mode: FileMode.Open,
                access: FileAccess.Read,
                share: FileShare.Read,
                bufferSize: bufferSize
            );
            using var destinationStream = new FileStream(
                path: destFileLocation,
                mode: FileMode.Create,
                access: FileAccess.Write,
                share: FileShare.None,
                bufferSize: bufferSize
            );

            int bytesRead;
            while (
                (
                    bytesRead = await sourceStream.ReadAsync(
                        buffer: new byte[bufferSize],
                        cancellationToken: cancellationToken
                    )
                ) > 0
            )
                await destinationStream.WriteAsync(
                    buffer: Convert.FromBase64String(
                        Encoding
                            .UTF8.GetString(
                                bytes: [.. (new byte[bufferSize]).Take(count: bytesRead)]
                            )
                            .AsDESDecrypt(
                                key: ConvertToBytes(data: Make16Chars(data: key))!,
                                iv: ConvertToBytes(data: Make16Chars(data: iv))
                            )!
                    ),
                    cancellationToken: cancellationToken
                );
        }
        catch (Exception ex)
        {
            systemLogging?.LogError(exception: ex);
            throw;
        }
    }

    public async Task PerformDESStringEncryption(
        string content,
        string destFileLocation,
        string key,
        string? iv = default,
        CancellationToken cancellationToken = default
    )
    {
        try
        {
            using var destinationStream = new FileStream(
                path: destFileLocation,
                mode: FileMode.Create,
                access: FileAccess.Write,
                share: FileShare.None
            );

            await destinationStream.WriteAsync(
                buffer: Convert.FromBase64String(
                    content.AsDESEncrypt(
                        key: ConvertToBytes(data: Make16Chars(data: key))!,
                        iv: ConvertToBytes(data: Make16Chars(data: iv))
                    )!
                ),
                cancellationToken: cancellationToken
            );
        }
        catch (Exception ex)
        {
            systemLogging?.LogError(exception: ex);
            throw;
        }
    }

    public async Task PerformDESStringDecryption(
        string content,
        string destFileLocation,
        string key,
        string? iv = default,
        CancellationToken cancellationToken = default
    )
    {
        try
        {
            using var destinationStream = new FileStream(
                path: destFileLocation,
                mode: FileMode.Create,
                access: FileAccess.Write,
                share: FileShare.None
            );

            await destinationStream.WriteAsync(
                buffer: Convert.FromBase64String(
                    content.AsDESDecrypt(
                        key: ConvertToBytes(data: Make16Chars(data: key))!,
                        iv: ConvertToBytes(data: Make16Chars(data: iv))
                    )!
                ),
                cancellationToken: cancellationToken
            );
        }
        catch (Exception ex)
        {
            systemLogging?.LogError(exception: ex);
            throw;
        }
    }
    #endregion DES

    #region RSA
    public async Task PerformRSAFileEncryption(
        string sourceFileLocation,
        string destFileLocation,
        string publicKey,
        int bufferSize = 81920,
        CancellationToken cancellationToken = default
    )
    {
        try
        {
            using var sourceStream = new FileStream(
                path: sourceFileLocation,
                mode: FileMode.Open,
                access: FileAccess.Read,
                share: FileShare.Read,
                bufferSize: bufferSize
            );
            using var destinationStream = new FileStream(
                path: destFileLocation,
                mode: FileMode.Create,
                access: FileAccess.Write,
                share: FileShare.None,
                bufferSize: bufferSize
            );

            int bytesRead;
            while (
                (
                    bytesRead = await sourceStream.ReadAsync(
                        buffer: new byte[bufferSize],
                        cancellationToken: cancellationToken
                    )
                ) > 0
            )
                await destinationStream.WriteAsync(
                    buffer: Convert.FromBase64String(
                        Encoding
                            .UTF8.GetString(
                                bytes: [.. (new byte[bufferSize]).Take(count: bytesRead)]
                            )
                            .AsRSAEncrypt(publicKey: publicKey)!
                    ),
                    cancellationToken: cancellationToken
                );
        }
        catch (Exception ex)
        {
            systemLogging?.LogError(exception: ex);
            throw;
        }
    }

    public async Task PerformRSAFileDecryption(
        string sourceFileLocation,
        string destFileLocation,
        string privateKey,
        int bufferSize = 81920,
        CancellationToken cancellationToken = default
    )
    {
        try
        {
            using var sourceStream = new FileStream(
                path: sourceFileLocation,
                mode: FileMode.Open,
                access: FileAccess.Read,
                share: FileShare.Read,
                bufferSize: bufferSize
            );
            using var destinationStream = new FileStream(
                path: destFileLocation,
                mode: FileMode.Create,
                access: FileAccess.Write,
                share: FileShare.None,
                bufferSize: bufferSize
            );

            int bytesRead;
            while (
                (
                    bytesRead = await sourceStream.ReadAsync(
                        buffer: new byte[bufferSize],
                        cancellationToken: cancellationToken
                    )
                ) > 0
            )
                await destinationStream.WriteAsync(
                    buffer: Convert.FromBase64String(
                        Encoding
                            .UTF8.GetString(
                                bytes: [.. (new byte[bufferSize]).Take(count: bytesRead)]
                            )
                            .AsRSADecrypt(privateKey: privateKey)!
                    ),
                    cancellationToken: cancellationToken
                );
        }
        catch (Exception ex)
        {
            systemLogging?.LogError(exception: ex);
            throw;
        }
    }
    #endregion RSA

    #region Base64
    public async Task PerformBase64FileEncoding(
        string sourceFileLocation,
        string destFileLocation,
        int bufferSize = 81920,
        CancellationToken cancellationToken = default
    )
    {
        try
        {
            using var sourceStream = new FileStream(
                path: sourceFileLocation,
                mode: FileMode.Open,
                access: FileAccess.Read,
                share: FileShare.Read,
                bufferSize: bufferSize
            );
            using var destinationStream = new FileStream(
                path: destFileLocation,
                mode: FileMode.Create,
                access: FileAccess.Write,
                share: FileShare.None,
                bufferSize: bufferSize
            );

            int bytesRead;
            while (
                (
                    bytesRead = await sourceStream.ReadAsync(
                        buffer: new byte[bufferSize],
                        cancellationToken: cancellationToken
                    )
                ) > 0
            )
                await destinationStream.WriteAsync(
                    buffer: Encoding.UTF8.GetBytes(
                        s: Convert.ToBase64String(
                            inArray: [.. (new byte[bufferSize]).Take(count: bytesRead)]
                        )
                    ),
                    cancellationToken: cancellationToken
                );
        }
        catch (Exception ex)
        {
            systemLogging?.LogError(exception: ex);
            throw;
        }
    }

    public async Task PerformBase64FileDecoding(
        string sourceFileLocation,
        string destFileLocation,
        int bufferSize = 81920,
        CancellationToken cancellationToken = default
    )
    {
        try
        {
            using var sourceStream = new FileStream(
                path: sourceFileLocation,
                mode: FileMode.Open,
                access: FileAccess.Read,
                share: FileShare.Read,
                bufferSize: bufferSize
            );
            using var destinationStream = new FileStream(
                path: destFileLocation,
                mode: FileMode.Create,
                access: FileAccess.Write,
                share: FileShare.None,
                bufferSize: bufferSize
            );

            int bytesRead;
            while (
                (
                    bytesRead = await sourceStream.ReadAsync(
                        buffer: new byte[bufferSize],
                        cancellationToken: cancellationToken
                    )
                ) > 0
            )
                await destinationStream.WriteAsync(
                    buffer: Convert.FromBase64String(
                        Encoding.UTF8.GetString(
                            bytes: [.. (new byte[bufferSize]).Take(count: bytesRead)]
                        )
                    ),
                    cancellationToken: cancellationToken
                );
        }
        catch (Exception ex)
        {
            systemLogging?.LogError(exception: ex);
            throw;
        }
    }
    #endregion Base64
}

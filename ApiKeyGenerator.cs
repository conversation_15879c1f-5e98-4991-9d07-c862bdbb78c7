// NOTE:
// FILE INI MASIH DALAM TAHAP RISET, JANGAN DIGUNAKAN!!!!


using System.Security.Cryptography;
using System.Text;

namespace IDC.Utilities;

/// <summary>
/// Provides functionality for generating and validating API keys using cryptographic hashing.
/// </summary>
/// <remarks>
/// A utility class that generates secure API keys for various scenarios including user-specific, temporary,
/// client-specific, and environment-specific keys.
///
/// Features:
/// - SHA256-based key generation
/// - Multiple key generation patterns
/// - Key validation capabilities
/// - Thread-safe operations
///
/// Example usage:
/// <example>
/// <code>
/// // Generate user-specific key
/// var userKey = ApiKeyGenerator.Generate(
///     userId: "user123",
///     appId: "app456",
///     expiryDate: DateTime.Now.AddDays(30),
///     salt: "custom-salt"
/// );
///
/// // Validate the key
/// bool isValid = ApiKeyGenerator.ValidateApiKey(
///     apiKey: userKey,
///     registeredKeys: new[] { "key1", "key2" }
/// );
/// </code>
/// </example>
///
/// > [!IMPORTANT]
/// > Always store and transmit API keys securely
///
/// > [!WARNING]
/// > Never expose the salt value in public code or configurations
///
/// > [!TIP]
/// > Use environment-specific salts for different deployment environments
/// </remarks>
/// <seealso href="https://learn.microsoft.com/en-us/dotnet/api/system.security.cryptography.sha256">SHA256 Class</seealso>
public sealed class ApiKeyGenerator
{
    /// <summary>
    /// Generates a user-specific API key using SHA256 hashing.
    /// </summary>
    /// <remarks>
    /// Creates a unique API key by combining user information, application ID, expiry date and a salt value.
    /// The generated key is Base64 encoded and cryptographically secure.
    ///
    /// Example usage:
    /// <example>
    /// <code>
    /// var apiKey = ApiKeyGenerator.Generate(
    ///     userId: "USER123",
    ///     appId: "MOBILE_APP",
    ///     expiryDate: DateTime.Now.AddDays(30),
    ///     salt: "production-salt-value"
    /// );
    /// // Returns: "NFHUZqt0zmL6siZ7/ynQ8nljJtsQrT3h0+nQZHhIQhk="
    /// </code>
    /// </example>
    ///
    /// > [!IMPORTANT]
    /// > The salt value should be kept secure and consistent within the same environment
    ///
    /// > [!NOTE]
    /// > The expiry date is formatted as 'yyyyMMdd' in the key generation process
    /// </remarks>
    /// <param name="userId">The unique identifier of the user requesting the API key</param>
    /// <param name="appId">The application identifier for which the key is being generated</param>
    /// <param name="expiryDate">The date when this API key should expire</param>
    /// <param name="salt">A secret value to enhance security of the generated key</param>
    /// <returns>A Base64 encoded string representing the generated API key</returns>
    /// <seealso href="https://learn.microsoft.com/en-us/dotnet/api/system.convert.tobase64string">Base64String</seealso>
    /// <seealso href="https://learn.microsoft.com/en-us/dotnet/api/system.security.cryptography.sha256">SHA256</seealso>
    public static string Generate(
        string userId,
        string appId,
        DateTime expiryDate,
        string salt = "your-salt-here"
    ) =>
        Convert.ToBase64String(
            SHA256.HashData(
                Encoding.UTF8.GetBytes($"{userId}-{appId}-{expiryDate:yyyyMMdd}-{salt}")
            )
        );

    /// <summary>
    /// Generates a temporary API key with specified validity period.
    /// </summary>
    /// <remarks>
    /// Creates a time-limited API key by combining purpose, expiry timestamp, and salt value.
    /// The generated key is Base64 encoded and uses SHA256 hashing for security.
    ///
    /// Example usage:
    /// <example>
    /// <code>
    /// // Generate 24-hour temporary key for file download
    /// var tempKey = ApiKeyGenerator.GenerateTemporary(
    ///     validity: TimeSpan.FromHours(24),
    ///     purpose: "file-download",
    ///     salt: "temp-key-salt"
    /// );
    /// // Returns: "xK2jM9pL5nR8qW3vY7tB4mD6fH9kN1sA="
    ///
    /// // Generate 30-minute temporary key for password reset
    /// var resetKey = ApiKeyGenerator.GenerateTemporary(
    ///     validity: TimeSpan.FromMinutes(30),
    ///     purpose: "password-reset",
    ///     salt: "reset-salt-value"
    /// );
    /// </code>
    /// </example>
    ///
    /// > [!IMPORTANT]
    /// > Temporary keys should be stored with their expiration time for validation
    ///
    /// > [!TIP]
    /// > Use descriptive purpose strings to easily identify key usage
    ///
    /// > [!NOTE]
    /// > The expiry timestamp is formatted as 'yyyyMMddHHmmss' in the key generation process
    /// </remarks>
    /// <param name="validity">The duration for which the key should remain valid</param>
    /// <param name="purpose">The intended purpose or use case for this temporary key</param>
    /// <param name="salt">A secret value to enhance security of the generated key</param>
    /// <returns>A Base64 encoded string representing the temporary API key</returns>
    /// <seealso href="https://learn.microsoft.com/en-us/dotnet/api/system.timespan">TimeSpan Structure</seealso>
    /// <seealso href="https://learn.microsoft.com/en-us/dotnet/api/system.convert.tobase64string">Base64String</seealso>
    /// <seealso href="https://learn.microsoft.com/en-us/dotnet/api/system.security.cryptography.sha256">SHA256</seealso>
    public static string GenerateTemporary(
        TimeSpan validity,
        string purpose,
        string salt = "your-salt-here"
    ) =>
        Convert.ToBase64String(
            SHA256.HashData(
                Encoding.UTF8.GetBytes(
                    $"{purpose}-{DateTime.UtcNow.Add(validity):yyyyMMddHHmmss}-{salt}"
                )
            )
        );

    /// <summary>
    /// Generates a client-specific API key with associated permissions.
    /// </summary>
    /// <remarks>
    /// Creates a unique API key by combining client credentials, permissions, and salt value.
    /// The generated key is Base64 encoded and uses SHA256 hashing for security.
    ///
    /// Example usage:
    /// <example>
    /// <code>
    /// // Generate API key for a client with specific permissions
    /// var clientKey = ApiKeyGenerator.GenerateForClient(
    ///     clientId: "client_123",
    ///     clientSecret: "secret_456",
    ///     permissions: ["read", "write", "delete"],
    ///     salt: "client-salt-value"
    /// );
    /// // Returns: "mK8pL4nR7qW2vY6tB3mD5fH8kN0sAxJ1jM8="
    ///
    /// // Generate API key for a read-only client
    /// var readOnlyKey = ApiKeyGenerator.GenerateForClient(
    ///     clientId: "readonly_client",
    ///     clientSecret: "readonly_secret",
    ///     permissions: ["read"],
    ///     salt: "readonly-salt"
    /// );
    /// </code>
    /// </example>
    ///
    /// > [!IMPORTANT]
    /// > Store client credentials securely and never expose them in public repositories
    ///
    /// > [!TIP]
    /// > Use granular permissions to implement principle of least privilege
    ///
    /// > [!NOTE]
    /// > The generated key combines all parameters in the format: clientId-clientSecret-permissions-salt
    ///
    /// > [!WARNING]
    /// > Rotate client secrets periodically to maintain security
    /// </remarks>
    /// <param name="clientId">The unique identifier for the client application</param>
    /// <param name="clientSecret">The secret key associated with the client</param>
    /// <param name="permissions">Array of permission strings defining client access levels</param>
    /// <param name="salt">A secret value to enhance security of the generated key</param>
    /// <returns>A Base64 encoded string representing the client-specific API key</returns>
    /// <seealso href="https://learn.microsoft.com/en-us/dotnet/api/system.convert.tobase64string">Base64String</seealso>
    /// <seealso href="https://learn.microsoft.com/en-us/dotnet/api/system.security.cryptography.sha256">SHA256</seealso>
    public static string GenerateForClient(
        string clientId,
        string clientSecret,
        string[] permissions,
        string salt = "your-salt-here"
    ) =>
        Convert.ToBase64String(
            SHA256.HashData(
                Encoding.UTF8.GetBytes(
                    $"{clientId}-{clientSecret}-{string.Join(",", permissions)}-{salt}"
                )
            )
        );

    /// <summary>
    /// Generates an environment-specific API key for service authentication.
    /// </summary>
    /// <remarks>
    /// Creates a unique API key by combining environment name, service name, version, and salt value.
    /// The generated key is Base64 encoded and uses SHA256 hashing for security.
    ///
    /// Example usage:
    /// <example>
    /// <code>
    /// // Generate API key for production environment
    /// var prodKey = ApiKeyGenerator.GenerateForEnvironment(
    ///     environment: "production",
    ///     serviceName: "payment-service",
    ///     version: "v1.0.0",
    ///     salt: "prod-env-salt"
    /// );
    /// // Returns: "bK9pL5nR8qW3vY7tB4mD6fH9kN1sAxK2jM9="
    ///
    /// // Generate API key for staging environment
    /// var stagingKey = ApiKeyGenerator.GenerateForEnvironment(
    ///     environment: "staging",
    ///     serviceName: "auth-service",
    ///     version: "v2.1.0",
    ///     salt: "staging-env-salt"
    /// );
    /// </code>
    /// </example>
    ///
    /// > [!IMPORTANT]
    /// > Use different salt values for different environments to maintain security isolation
    ///
    /// > [!TIP]
    /// > Include major version changes in the version parameter to manage API versioning
    ///
    /// > [!NOTE]
    /// > The generated key combines all parameters in the format: environment-serviceName-version-salt
    /// </remarks>
    /// <param name="environment">The deployment environment (e.g., production, staging, development)</param>
    /// <param name="serviceName">The name of the service requiring authentication</param>
    /// <param name="version">The version identifier of the service</param>
    /// <param name="salt">A secret value to enhance security of the generated key</param>
    /// <returns>A Base64 encoded string representing the environment-specific API key</returns>
    /// <seealso href="https://learn.microsoft.com/en-us/dotnet/api/system.convert.tobase64string">Base64String</seealso>
    /// <seealso href="https://learn.microsoft.com/en-us/dotnet/api/system.security.cryptography.sha256">SHA256</seealso>
    public static string GenerateForEnvironment(
        string environment,
        string serviceName,
        string version,
        string salt = "your-salt-here"
    ) =>
        Convert.ToBase64String(
            SHA256.HashData(Encoding.UTF8.GetBytes($"{environment}-{serviceName}-{version}-{salt}"))
        );

    /// <summary>
    /// Validates an API key against a collection of registered keys.
    /// </summary>
    /// <remarks>
    /// Performs a simple validation check to determine if the provided API key exists in the collection of
    /// registered keys. This method is null-safe and handles empty collections gracefully.
    ///
    /// Example usage:
    /// <example>
    /// <code>
    /// // Example registered keys from configuration
    /// var registeredKeys = new[] {
    ///     "NFHUZqt0zmL6siZ7/ynQ8nljJtsQrT3h0+nQZHhIQhk=",
    ///     "IDxvX6aT3XTERRpuHpMNtpcQVUo2rZ3Smtm83UPVfi8="
    /// };
    ///
    /// // Validate an API key
    /// bool isValid = ApiKeyGenerator.ValidateApiKey(
    ///     apiKey: "NFHUZqt0zmL6siZ7/ynQ8nljJtsQrT3h0+nQZHhIQhk=",
    ///     registeredKeys: registeredKeys
    /// );
    ///
    /// // Handle validation result
    /// if (isValid) {
    ///     // Process authenticated request
    /// }
    /// </code>
    /// </example>
    ///
    /// > [!IMPORTANT]
    /// > This method performs a simple existence check. For more detailed validation, use the overload with
    /// > error message output
    ///
    /// > [!NOTE]
    /// > Returns false if registeredKeys is null or empty
    ///
    /// > [!TIP]
    /// > Use this method for quick validation in middleware or authentication handlers
    /// </remarks>
    /// <param name="apiKey">The API key to validate</param>
    /// <param name="registeredKeys">
    /// <see href="https://learn.microsoft.com/en-us/dotnet/api/system.collections.generic.ienumerable-1">
    /// IEnumerable</see> of valid API keys to check against
    /// </param>
    /// <returns>True if the API key exists in the registered keys collection, false otherwise</returns>
    /// <seealso href="https://learn.microsoft.com/en-us/dotnet/api/system.linq.enumerable.contains">
    /// Enumerable.Contains Method</seealso>
    public static bool ValidateApiKey(string apiKey, IEnumerable<string> registeredKeys) =>
        registeredKeys?.Contains(apiKey) ?? false;

    /// <summary>
    /// Validates an API key against a collection of registered keys and provides detailed error information.
    /// </summary>
    /// <remarks>
    /// Performs validation of the provided API key against a collection of registered keys and returns detailed
    /// error information if validation fails.
    ///
    /// Example usage:
    /// <example>
    /// <code>
    /// var registeredKeys = new[] {
    ///     "NFHUZqt0zmL6siZ7/ynQ8nljJtsQrT3h0+nQZHhIQhk=",
    ///     "xK2jM9pL5nR8qW3vY7tB4mD6fH9kN1sA="
    /// };
    ///
    /// if (ApiKeyGenerator.ValidateApiKey(
    ///     apiKey: "NFHUZqt0zmL6siZ7/ynQ8nljJtsQrT3h0+nQZHhIQhk=",
    ///     registeredKeys: registeredKeys,
    ///     errMessage: out string? error
    /// ))
    /// {
    ///     // API key is valid
    /// }
    /// else
    /// {
    ///     Console.WriteLine($"Validation failed: {error}");
    /// }
    /// </code>
    /// </example>
    ///
    /// > [!IMPORTANT]
    /// > Always maintain a secure storage for registered API keys
    ///
    /// > [!NOTE]
    /// > The method returns false and sets appropriate error message for null or empty registered keys collection
    ///
    /// > [!WARNING]
    /// > Handle the error message securely to prevent information leakage
    /// </remarks>
    /// <param name="apiKey">The API key to validate</param>
    /// <param name="registeredKeys">Collection of valid API keys to check against</param>
    /// <param name="errMessage">Output parameter containing error message if validation fails</param>
    /// <returns>True if the API key is valid, false otherwise</returns>
    /// <seealso href="https://learn.microsoft.com/en-us/dotnet/api/system.collections.generic.ienumerable-1">
    /// IEnumerable&lt;T&gt;</seealso>
    /// <exception cref="Exception">Thrown when an unexpected error occurs during validation</exception>
    public static bool ValidateApiKey(
        string apiKey,
        IEnumerable<string> registeredKeys,
        out string? errMessage
    )
    {
        errMessage = null;
        try
        {
            if (registeredKeys == null || !registeredKeys.Any())
            {
                errMessage = "No registered API keys found";
                return false;
            }

            if (!registeredKeys.Contains(apiKey))
            {
                errMessage = "Invalid API key";
                return false;
            }

            return true;
        }
        catch (Exception ex)
        {
            errMessage = ex.Message;
            return false;
        }
    }
}

using System.Globalization;
using System.Security.Cryptography;
using System.Text;
using System.Text.RegularExpressions;

namespace IDC.Utilities.Extensions;

/// <summary>
/// Provides extension methods for string manipulation and validation.
/// </summary>
/// <remarks>
/// A collection of utility methods that extend the functionality of strings in .NET applications.
/// These methods provide common string operations like case conversion, formatting, and validation.
///
/// Example usage:
/// <example>
/// <code>
/// string text = "Hello World";
/// string snakeCase = text.ToSnakeCase();      // "hello_world"
/// string truncated = text.Truncate(length: 5); // "Hello..."
/// string reversed = text.Reverse();            // "dlroW olleH"
/// </code>
/// </example>
///
/// > [!NOTE]
/// > All methods handle null inputs safely by returning empty string
///
/// > [!TIP]
/// > These extensions are particularly useful for text processing and formatting scenarios
/// </remarks>
/// <seealso href="https://learn.microsoft.com/en-us/dotnet/api/system.string">String Class</seealso>
/// <seealso href="https://learn.microsoft.com/en-us/dotnet/standard/base-types/string-manipulation">String Manipulation</seealso>
public static partial class StringExtensions
{
    /// <summary>
    /// Truncates a string to a specified length and adds an ellipsis if truncated.
    /// </summary>
    /// <remarks>
    /// This method safely handles null inputs by returning empty string. If the input string is shorter than
    /// or equal to maxLength, it returns the original string. Otherwise, it truncates to maxLength and appends
    /// the specified suffix.
    ///
    /// Example usage:
    /// <example>
    /// <code>
    /// string text = "This is a long text";
    /// string result = text.Truncate(maxLength: 10);           // returns "This is..."
    /// string custom = text.Truncate(maxLength: 7, suffix: "!"); // returns "This is!"
    /// string nullStr = null;
    /// string safe = nullStr.Truncate(maxLength: 5);           // returns ""
    /// </code>
    /// </example>
    ///
    /// > [!NOTE]
    /// > The suffix is only added if the string is actually truncated
    ///
    /// > [!TIP]
    /// > Consider the length of your suffix when choosing maxLength to ensure readable results
    /// </remarks>
    /// <param name="value">The string to truncate. Can be null.</param>
    /// <param name="maxLength">The maximum length of the resulting string before adding the suffix.</param>
    /// <param name="suffix">The string to append if truncation occurs. Defaults to "..."</param>
    /// <returns>
    /// A string that has been truncated to the specified length with the suffix appended if truncation occurred.
    /// Returns empty string if input is null.
    /// </returns>
    /// <seealso href="https://learn.microsoft.com/en-us/dotnet/api/system.string.substring">String.Substring Method</seealso>
    public static string Truncate(this string? value, int maxLength, string suffix = "...") =>
        string.IsNullOrEmpty(value: value) || value.Length <= maxLength
            ? value ?? string.Empty
            : value[..maxLength] + suffix;

    /// <summary>
    /// Removes all whitespace characters from a string, including spaces, tabs, and line breaks.
    /// </summary>
    /// <remarks>
    /// This method efficiently removes all Unicode whitespace characters from a string using LINQ.
    /// It handles null inputs safely by returning an empty string.
    ///
    /// The method uses char.IsWhiteSpace to identify whitespace characters according to Unicode standards.
    ///
    /// Example usage:
    /// <example>
    /// <code>
    /// string text = "Hello   World\n\t";
    /// string result = text.RemoveWhitespace();     // returns "HelloWorld"
    /// string multiline = "Hello\r\nWorld";
    /// string cleaned = multiline.RemoveWhitespace(); // returns "HelloWorld"
    /// string nullStr = null;
    /// string safe = nullStr.RemoveWhitespace();    // returns ""
    /// </code>
    /// </example>
    ///
    /// > [!NOTE]
    /// > This method preserves all non-whitespace characters in their original order
    ///
    /// > [!TIP]
    /// > Use this method when you need to clean up user input or normalize strings for comparison
    ///
    /// > [!IMPORTANT]
    /// > The method considers all Unicode whitespace characters, not just ASCII spaces
    /// </remarks>
    /// <param name="value">The string from which to remove whitespace. Can be null.</param>
    /// <returns>
    /// A new string with all whitespace characters removed.
    /// Returns empty string if input is null.
    /// </returns>
    /// <seealso href="https://learn.microsoft.com/en-us/dotnet/api/system.char.iswhitespace">Char.IsWhiteSpace Method</seealso>
    /// <seealso href="https://learn.microsoft.com/en-us/dotnet/api/system.string">String Class</seealso>
    public static string RemoveWhitespace(this string? value) =>
        string.IsNullOrEmpty(value: value)
            ? string.Empty
            : new string(value: [.. value.Where(predicate: static c => !char.IsWhiteSpace(c: c))]);

    /// <summary>
    /// Converts a string to snake_case format by inserting underscores between words and converting to lowercase.
    /// </summary>
    /// <remarks>
    /// This method transforms any string into snake_case format by identifying word boundaries and inserting underscores.
    /// It handles null inputs safely by returning an empty string. The method uses regex pattern matching to identify
    /// word boundaries and transform the text.
    ///
    /// Example usage:
    /// <example>
    /// <code>
    /// string pascalCase = "HelloWorld";
    /// string result1 = pascalCase.ToSnakeCase();    // returns "hello_world"
    ///
    /// string camelCase = "myVariableName";
    /// string result2 = camelCase.ToSnakeCase();     // returns "my_variable_name"
    ///
    /// string withNumbers = "User123Name";
    /// string result3 = withNumbers.ToSnakeCase();   // returns "user123_name"
    ///
    /// string nullString = null;
    /// string result4 = nullString.ToSnakeCase();    // returns ""
    /// </code>
    /// </example>
    ///
    /// > [!NOTE]
    /// > The method preserves numbers but adds underscores between number-letter transitions
    ///
    /// > [!TIP]
    /// > This format is commonly used in database column names and Ruby programming
    ///
    /// > [!IMPORTANT]
    /// > Consecutive capital letters are treated as separate words
    /// </remarks>
    /// <param name="value">The string to convert to snake_case. Can be null.</param>
    /// <returns>
    /// A string in snake_case format.
    /// Returns empty string if input is null.
    /// </returns>
    /// <seealso href="https://learn.microsoft.com/en-us/dotnet/api/system.text.regularexpressions.regex">Regex Class</seealso>
    /// <seealso href="https://en.wikipedia.org/wiki/Snake_case">Snake Case Convention</seealso>
    public static string ToSnakeCase(this string? value) =>
        string.IsNullOrEmpty(value: value)
            ? string.Empty
            : RegexPatternCollections
                .SnakeCase()
                .Replace(input: value, replacement: "$1_$2")
                .ToLower();

    /// <summary>
    /// Converts a string to kebab-case format by inserting hyphens between words and converting to lowercase.
    /// </summary>
    /// <remarks>
    /// This method transforms any string into kebab-case format by identifying word boundaries and inserting hyphens.
    /// It handles null inputs safely by returning an empty string. The method uses regex pattern matching to identify
    /// word boundaries and transform the text.
    ///
    /// Example usage:
    /// <example>
    /// <code>
    /// string pascalCase = "HelloWorld";
    /// string result1 = pascalCase.ToKebabCase();    // returns "hello-world"
    ///
    /// string camelCase = "myVariableName";
    /// string result2 = camelCase.ToKebabCase();     // returns "my-variable-name"
    ///
    /// string withNumbers = "User123Name";
    /// string result3 = withNumbers.ToKebabCase();   // returns "user123-name"
    ///
    /// string nullString = null;
    /// string result4 = nullString.ToKebabCase();    // returns ""
    /// </code>
    /// </example>
    ///
    /// > [!NOTE]
    /// > The method preserves numbers but adds hyphens between number-letter transitions
    ///
    /// > [!TIP]
    /// > This format is commonly used in URLs and CSS class names
    ///
    /// > [!IMPORTANT]
    /// > Consecutive capital letters are treated as separate words
    /// </remarks>
    /// <param name="value">The string to convert to kebab-case. Can be null.</param>
    /// <returns>
    /// A string in kebab-case format.
    /// Returns empty string if input is null.
    /// </returns>
    /// <seealso href="https://learn.microsoft.com/en-us/dotnet/api/system.text.regularexpressions.regex">Regex Class</seealso>
    /// <seealso href="https://en.wikipedia.org/wiki/Kebab_case">Kebab Case Convention</seealso>
    public static string ToKebabCase(this string? value) =>
        string.IsNullOrEmpty(value: value)
            ? string.Empty
            : RegexPatternCollections
                .KebabCase()
                .Replace(input: value, replacement: "$1-$2")
                .ToLower();

    /// <summary>
    /// Converts a string to Title Case format where the first character of each word is capitalized.
    /// </summary>
    /// <remarks>
    /// This method transforms any string into Title Case format using the current culture's text info.
    /// It handles null inputs safely by returning an empty string. The method first converts the string
    /// to lowercase to ensure consistent capitalization.
    ///
    /// Example usage:
    /// <example>
    /// <code>
    /// string lowercase = "hello world";
    /// string result1 = lowercase.ToTitleCase();    // returns "Hello World"
    ///
    /// string mixed = "hELLo wORLD";
    /// string result2 = mixed.ToTitleCase();        // returns "Hello World"
    ///
    /// string withNumbers = "hello world 123";
    /// string result3 = withNumbers.ToTitleCase();  // returns "Hello World 123"
    ///
    /// string nullString = null;
    /// string result4 = nullString.ToTitleCase();   // returns ""
    /// </code>
    /// </example>
    ///
    /// > [!NOTE]
    /// > The method uses the current thread's culture for proper capitalization rules
    ///
    /// > [!TIP]
    /// > This format is commonly used for titles, headings, and proper nouns
    ///
    /// > [!IMPORTANT]
    /// > The result may vary depending on the current culture settings
    /// </remarks>
    /// <param name="value">The string to convert to Title Case. Can be null.</param>
    /// <returns>
    /// A string in Title Case format.
    /// Returns empty string if input is null.
    /// </returns>
    /// <seealso href="https://learn.microsoft.com/en-us/dotnet/api/system.globalization.textinfo.totitlecase">TextInfo.ToTitleCase Method</seealso>
    /// <seealso href="https://learn.microsoft.com/en-us/dotnet/api/system.threading.thread.currentculture">CurrentCulture Property</seealso>
    public static string ToTitleCase(this string? value) =>
        string.IsNullOrEmpty(value: value)
            ? string.Empty
            : Thread.CurrentThread.CurrentCulture.TextInfo.ToTitleCase(str: value.ToLower());

    /// <summary>
    /// Reverses the characters in a string.
    /// </summary>
    /// <remarks>
    /// This method efficiently reverses all characters in a string using LINQ and array operations.
    /// It handles null inputs safely by returning an empty string. The method preserves Unicode
    /// surrogate pairs and combining characters.
    ///
    /// Example usage:
    /// <example>
    /// <code>
    /// string text = "Hello World!";
    /// string result1 = text.Reverse();        // returns "!dlroW olleH"
    ///
    /// string unicode = "Hello 👋 World";
    /// string result2 = unicode.Reverse();     // returns "dlroW 👋 olleH"
    ///
    /// string empty = "";
    /// string result3 = empty.Reverse();       // returns ""
    ///
    /// string nullString = null;
    /// string result4 = nullString.Reverse();  // returns ""
    /// </code>
    /// </example>
    ///
    /// > [!NOTE]
    /// > The method maintains the integrity of Unicode characters including emojis
    ///
    /// > [!TIP]
    /// > Useful for string manipulation tasks like palindrome checking
    ///
    /// > [!IMPORTANT]
    /// > For complex Unicode strings, consider using StringInfo class for proper grapheme cluster handling
    /// </remarks>
    /// <param name="value">The string to reverse. Can be null.</param>
    /// <returns>
    /// A new string with all characters in reverse order.
    /// Returns empty string if input is null.
    /// </returns>
    /// <seealso href="https://learn.microsoft.com/en-us/dotnet/api/system.string">String Class</seealso>
    /// <seealso href="https://learn.microsoft.com/en-us/dotnet/api/system.char">Char Struct</seealso>
    /// <seealso href="https://learn.microsoft.com/en-us/dotnet/api/system.globalization.stringinfo">StringInfo Class</seealso>
    public static string Reverse(this string? value) =>
        string.IsNullOrEmpty(value: value)
            ? string.Empty
            : new string(value: [.. value.ToCharArray().Reverse()]);

    /// <summary>
    /// Converts the first character of a string to uppercase.
    /// </summary>
    /// <remarks>
    /// This method transforms the first character of a string to uppercase while preserving the rest of the string.
    /// It handles null inputs safely by returning an empty string. The method uses char.ToUpper for proper
    /// culture-aware case conversion.
    ///
    /// Example usage:
    /// <example>
    /// <code>
    /// string lowercase = "hello world";
    /// string result1 = lowercase.FirstCharToUpper();    // returns "Hello world"
    ///
    /// string mixed = "tEST string";
    /// string result2 = mixed.FirstCharToUpper();        // returns "TEST string"
    ///
    /// string singleChar = "a";
    /// string result3 = singleChar.FirstCharToUpper();   // returns "A"
    ///
    /// string nullString = null;
    /// string result4 = nullString.FirstCharToUpper();   // returns ""
    /// </code>
    /// </example>
    ///
    /// > [!NOTE]
    /// > The method only affects the first character, leaving the rest of the string unchanged
    ///
    /// > [!TIP]
    /// > Useful for formatting names or beginning of sentences
    ///
    /// > [!IMPORTANT]
    /// > For empty strings or strings with length 1, appropriate handling is implemented
    /// </remarks>
    /// <param name="value">The string to process. Can be null.</param>
    /// <returns>
    /// A string with its first character in uppercase.
    /// Returns empty string if input is null or empty.
    /// </returns>
    /// <seealso href="https://learn.microsoft.com/en-us/dotnet/api/system.char.toupper">Char.ToUpper Method</seealso>
    /// <seealso href="https://learn.microsoft.com/en-us/dotnet/api/system.string.substring">String.Substring Method</seealso>
    public static string FirstCharToUpper(this string? value) =>
        string.IsNullOrEmpty(value: value) ? string.Empty : char.ToUpper(c: value[0]) + value[1..];

    /// <summary>
    /// Converts the first character of a string to lowercase.
    /// </summary>
    /// <remarks>
    /// This method transforms the first character of a string to lowercase while preserving the rest of the string.
    /// It handles null inputs safely by returning an empty string. The method uses char.ToLower for proper
    /// culture-aware case conversion.
    ///
    /// Example usage:
    /// <example>
    /// <code>
    /// string uppercase = "Hello World";
    /// string result1 = uppercase.FirstCharToLower();    // returns "hello World"
    ///
    /// string mixed = "TEST string";
    /// string result2 = mixed.FirstCharToLower();        // returns "tEST string"
    ///
    /// string singleChar = "A";
    /// string result3 = singleChar.FirstCharToLower();   // returns "a"
    ///
    /// string nullString = null;
    /// string result4 = nullString.FirstCharToLower();   // returns ""
    /// </code>
    /// </example>
    ///
    /// > [!NOTE]
    /// > The method only affects the first character, leaving the rest of the string unchanged
    ///
    /// > [!TIP]
    /// > Useful for formatting camelCase identifiers or variable names
    ///
    /// > [!IMPORTANT]
    /// > For empty strings or strings with length 1, appropriate handling is implemented
    /// </remarks>
    /// <param name="value">The string to process. Can be null.</param>
    /// <returns>
    /// A string with its first character in lowercase.
    /// Returns empty string if input is null or empty.
    /// </returns>
    /// <seealso href="https://learn.microsoft.com/en-us/dotnet/api/system.char.tolower">Char.ToLower Method</seealso>
    /// <seealso href="https://learn.microsoft.com/en-us/dotnet/api/system.string.substring">String.Substring Method</seealso>
    public static string FirstCharToLower(this string? value) =>
        string.IsNullOrEmpty(value: value) ? string.Empty : char.ToLower(c: value[0]) + value[1..];

    /// <summary>
    /// Removes diacritics (accent marks) from a string.
    /// </summary>
    /// <remarks>
    /// This method removes all diacritical marks from characters while preserving the base letter.
    /// It handles null inputs safely by returning an empty string. The method uses Unicode normalization
    /// to separate base characters from their combining diacritical marks.
    ///
    /// Example usage:
    /// <example>
    /// <code>
    /// string accented = "résumé";
    /// string result1 = accented.RemoveDiacritics();     // returns "resume"
    ///
    /// string mixed = "Crème Brûlée";
    /// string result2 = mixed.RemoveDiacritics();        // returns "Creme Brulee"
    ///
    /// string complex = "Voilà l'été";
    /// string result3 = complex.RemoveDiacritics();      // returns "Voila l'ete"
    ///
    /// string nullString = null;
    /// string result4 = nullString.RemoveDiacritics();   // returns ""
    /// </code>
    /// </example>
    ///
    /// > [!NOTE]
    /// > The method preserves case and non-diacritical punctuation
    ///
    /// > [!TIP]
    /// > Useful for search functionality or URL slug generation
    ///
    /// > [!IMPORTANT]
    /// > The method handles all Unicode diacritical marks, not just common Latin accents
    /// </remarks>
    /// <param name="value">The string from which to remove diacritics. Can be null.</param>
    /// <returns>
    /// A string with all diacritical marks removed.
    /// Returns empty string if input is null.
    /// </returns>
    /// <seealso href="https://learn.microsoft.com/en-us/dotnet/api/system.text.normalizationform">NormalizationForm Enumeration</seealso>
    /// <seealso href="https://learn.microsoft.com/en-us/dotnet/api/system.globalization.unicodecategory">UnicodeCategory Enumeration</seealso>
    public static string RemoveDiacritics(this string? value)
    {
        if (string.IsNullOrEmpty(value: value))
            return string.Empty;

        var normalizedString = value.Normalize(normalizationForm: NormalizationForm.FormD);
        var stringBuilder = new StringBuilder();

        foreach (var c in normalizedString)
            if (CharUnicodeInfo.GetUnicodeCategory(ch: c) != UnicodeCategory.NonSpacingMark)
                stringBuilder.Append(value: c);

        return stringBuilder.ToString().Normalize(normalizationForm: NormalizationForm.FormC);
    }

    /// <summary>
    /// Extracts numbers from a string.
    /// </summary>
    /// <remarks>
    /// This method filters out all non-numeric characters from a string, returning only the numeric digits.
    /// It handles null inputs safely by returning an empty string. The method preserves the order of
    /// numbers as they appear in the original string.
    ///
    /// Example usage:
    /// <example>
    /// <code>
    /// string mixed = "ABC123DEF456";
    /// string result1 = mixed.ExtractNumbers();          // returns "123456"
    ///
    /// string phone = "+****************";
    /// string result2 = phone.ExtractNumbers();          // returns "15551234567"
    ///
    /// string noNumbers = "Hello World!";
    /// string result3 = noNumbers.ExtractNumbers();      // returns ""
    ///
    /// string nullString = null;
    /// string result4 = nullString.ExtractNumbers();     // returns ""
    /// </code>
    /// </example>
    ///
    /// > [!NOTE]
    /// > The method only extracts 0-9 digits, ignoring any other numeric symbols
    ///
    /// > [!TIP]
    /// > Useful for cleaning phone numbers or extracting numeric data from formatted strings
    ///
    /// > [!IMPORTANT]
    /// > Decimal points and negative signs are not preserved
    /// </remarks>
    /// <param name="value">The string from which to extract numbers. Can be null.</param>
    /// <returns>
    /// A string containing only the numeric characters from the input.
    /// Returns empty string if input is null or contains no numbers.
    /// </returns>
    /// <seealso href="https://learn.microsoft.com/en-us/dotnet/api/system.char.isdigit">Char.IsDigit Method</seealso>
    /// <seealso href="https://learn.microsoft.com/en-us/dotnet/api/system.linq.enumerable.where">Enumerable.Where Method</seealso>
    public static string ExtractNumbers(this string? value) =>
        string.IsNullOrEmpty(value: value)
            ? string.Empty
            : new string(value: [.. value.Where(predicate: char.IsDigit)]);

    /// <summary>
    /// Extracts letters from a string.
    /// </summary>
    /// <remarks>
    /// This method filters out all non-letter characters from a string, returning only alphabetic characters.
    /// It handles null inputs safely by returning an empty string. The method preserves the case and order of
    /// letters as they appear in the original string.
    ///
    /// Example usage:
    /// <example>
    /// <code>
    /// string mixed = "ABC123DEF456";
    /// string result1 = mixed.ExtractLetters();          // returns "ABCDEF"
    ///
    /// string sentence = "Hello, World! 2023";
    /// string result2 = sentence.ExtractLetters();       // returns "HelloWorld"
    ///
    /// string noLetters = "12345!@#$%";
    /// string result3 = noLetters.ExtractLetters();      // returns ""
    ///
    /// string nullString = null;
    /// string result4 = nullString.ExtractLetters();     // returns ""
    /// </code>
    /// </example>
    ///
    /// > [!NOTE]
    /// > The method preserves both uppercase and lowercase letters
    ///
    /// > [!TIP]
    /// > Useful for extracting text from alphanumeric codes or cleaning string data
    ///
    /// > [!IMPORTANT]
    /// > Unicode letters from all alphabets are included
    /// </remarks>
    /// <param name="value">The string from which to extract letters. Can be null.</param>
    /// <returns>
    /// A string containing only the letter characters from the input.
    /// Returns empty string if input is null or contains no letters.
    /// </returns>
    /// <seealso href="https://learn.microsoft.com/en-us/dotnet/api/system.char.isletter">Char.IsLetter Method</seealso>
    /// <seealso href="https://learn.microsoft.com/en-us/dotnet/api/system.linq.enumerable.where">Enumerable.Where Method</seealso>
    public static string ExtractLetters(this string? value) =>
        string.IsNullOrEmpty(value: value)
            ? string.Empty
            : new string(value: [.. value.Where(predicate: char.IsLetter)]);

    /// <summary>
    /// Converts a string to Base64 encoding.
    /// </summary>
    /// <remarks>
    /// This method converts a string to its Base64 representation using UTF-8 encoding.
    /// It safely handles null inputs by returning an empty string. The output is compatible with
    /// standard Base64 encoding specifications.
    ///
    /// Example usage:
    /// <example>
    /// <code>
    /// string text = "Hello World";
    /// string result1 = text.ToBase64();                 // returns "SGVsbG8gV29ybGQ="
    ///
    /// string special = "Hello@123!";
    /// string result2 = special.ToBase64();              // returns "SGVsbG9AMTIzIQ=="
    ///
    /// string unicode = "こんにちは";
    /// string result3 = unicode.ToBase64();              // returns "44GT44KT44Gr44Gh44Gv"
    ///
    /// string nullString = null;
    /// string result4 = nullString.ToBase64();           // returns ""
    /// </code>
    /// </example>
    ///
    /// > [!NOTE]
    /// > The output is padded with '=' characters as per Base64 specification
    ///
    /// > [!TIP]
    /// > Useful for encoding data for transmission or storage where binary data isn't suitable
    ///
    /// > [!IMPORTANT]
    /// > The method uses UTF-8 encoding for string conversion
    /// </remarks>
    /// <param name="value">The string to convert to Base64. Can be null.</param>
    /// <returns>
    /// A Base64 encoded string.
    /// Returns empty string if input is null.
    /// </returns>
    /// <seealso href="https://learn.microsoft.com/en-us/dotnet/api/system.convert.tobase64string">Convert.ToBase64String Method</seealso>
    /// <seealso href="https://learn.microsoft.com/en-us/dotnet/api/system.text.encoding.utf8">UTF8 Encoding</seealso>
    public static string ToBase64(this string? value) =>
        string.IsNullOrEmpty(value: value)
            ? string.Empty
            : Convert.ToBase64String(inArray: Encoding.UTF8.GetBytes(s: value));

    /// <summary>
    /// Decodes a Base64 string back to its original string representation.
    /// </summary>
    /// <remarks>
    /// This method converts a Base64 encoded string back to its original string representation using UTF-8 encoding.
    /// It safely handles null inputs by returning an empty string. The method expects standard Base64 encoded input.
    ///
    /// Example usage:
    /// <example>
    /// <code>
    /// string base64 = "SGVsbG8gV29ybGQ=";
    /// string result1 = base64.FromBase64();             // returns "Hello World"
    ///
    /// string special = "SGVsbG9AMTIzIQ==";
    /// string result2 = special.FromBase64();            // returns "Hello@123!"
    ///
    /// string unicode = "44GT44KT44Gr44Gh44Gv";
    /// string result3 = unicode.FromBase64();            // returns "こんにちは"
    ///
    /// string nullString = null;
    /// string result4 = nullString.FromBase64();         // returns ""
    /// </code>
    /// </example>
    ///
    /// > [!NOTE]
    /// > The method expects properly padded Base64 input
    ///
    /// > [!TIP]
    /// > Useful for decoding Base64 encoded data in transmission or storage scenarios
    ///
    /// > [!CAUTION]
    /// > Invalid Base64 input will throw a FormatException
    ///
    /// > [!IMPORTANT]
    /// > The method uses UTF-8 encoding for string conversion
    /// </remarks>
    /// <param name="value">The Base64 string to decode. Can be null.</param>
    /// <returns>
    /// The original string decoded from Base64.
    /// Returns empty string if input is null.
    /// </returns>
    /// <exception cref="System.FormatException">
    /// Thrown when the input string is not a valid Base64 encoded string.
    /// </exception>
    /// <seealso href="https://learn.microsoft.com/en-us/dotnet/api/system.convert.frombase64string">Convert.FromBase64String Method</seealso>
    /// <seealso href="https://learn.microsoft.com/en-us/dotnet/api/system.text.encoding.utf8">UTF8 Encoding</seealso>
    public static string FromBase64(this string? value) =>
        string.IsNullOrEmpty(value: value)
            ? string.Empty
            : Encoding.UTF8.GetString(bytes: Convert.FromBase64String(s: value));

    /// <summary>
    /// Converts a string to its MD5 hash representation.
    /// </summary>
    /// <remarks>
    /// This method generates a 128-bit (16-byte) hash value represented as a 32-character hexadecimal string.
    /// It safely handles null inputs by returning an empty string. The output is always lowercase.
    ///
    /// Example usage:
    /// <example>
    /// <code>
    /// string text = "Hello World";
    /// string result1 = text.ToMD5();                    // returns "b10a8db164e0754105b7a99be72e3fe5"
    ///
    /// string password = "mySecurePassword123";
    /// string result2 = password.ToMD5();                // returns "6dcd4ce23d88e2ee9568ba546c007c63"
    ///
    /// string empty = "";
    /// string result3 = empty.ToMD5();                   // returns ""
    ///
    /// string nullString = null;
    /// string result4 = nullString.ToMD5();              // returns ""
    /// </code>
    /// </example>
    ///
    /// > [!WARNING]
    /// > MD5 is cryptographically broken and should not be used for secure hashing or password storage
    ///
    /// > [!IMPORTANT]
    /// > Use SHA256 or stronger algorithms for security-critical applications
    ///
    /// > [!TIP]
    /// > Suitable for checksums and non-security hash requirements
    /// </remarks>
    /// <param name="value">The string to convert to MD5 hash. Can be null.</param>
    /// <returns>
    /// A 32-character lowercase hexadecimal string representing the MD5 hash.
    /// Returns empty string if input is null or empty.
    /// </returns>
    /// <seealso href="https://learn.microsoft.com/en-us/dotnet/api/system.security.cryptography.md5">MD5 Class</seealso>
    /// <seealso href="https://learn.microsoft.com/en-us/dotnet/api/system.convert.tohexstring">Convert.ToHexString Method</seealso>
    public static string ToMD5(this string? value)
    {
        if (string.IsNullOrEmpty(value: value))
            return string.Empty;
        return Convert
            .ToHexString(inArray: MD5.HashData(source: Encoding.UTF8.GetBytes(s: value)))
            .ToLower();
    }

    /// <summary>
    /// Converts a string to its SHA256 hash representation.
    /// </summary>
    /// <remarks>
    /// This method generates a 256-bit (32-byte) hash value represented as a 64-character hexadecimal string.
    /// It safely handles null inputs by returning an empty string. The output is always lowercase.
    ///
    /// Example usage:
    /// <example>
    /// <code>
    /// string text = "Hello World";
    /// string result1 = text.ToSHA256();                 // returns "a591a6d40bf420404a011733cfb7b190d62c65bf0bcda32b57b277d9ad9f146e"
    ///
    /// string password = "mySecurePassword123";
    /// string result2 = password.ToSHA256();             // returns "7d793037a0760186574b0282f2f435e7"
    ///
    /// string empty = "";
    /// string result3 = empty.ToSHA256();                // returns ""
    ///
    /// string nullString = null;
    /// string result4 = nullString.ToSHA256();           // returns ""
    /// </code>
    /// </example>
    ///
    /// > [!NOTE]
    /// > SHA256 is a cryptographically secure hash function
    ///
    /// > [!TIP]
    /// > Recommended for security-critical applications and password hashing
    ///
    /// > [!IMPORTANT]
    /// > For password storage, consider using specialized password hashing functions like Argon2 or PBKDF2
    /// </remarks>
    /// <param name="value">The string to convert to SHA256 hash. Can be null.</param>
    /// <returns>
    /// A 64-character lowercase hexadecimal string representing the SHA256 hash.
    /// Returns empty string if input is null or empty.
    /// </returns>
    /// <seealso href="https://learn.microsoft.com/en-us/dotnet/api/system.security.cryptography.sha256">SHA256 Class</seealso>
    /// <seealso href="https://learn.microsoft.com/en-us/dotnet/api/system.convert.tohexstring">Convert.ToHexString Method</seealso>
    public static string ToSHA256(this string? value)
    {
        if (string.IsNullOrEmpty(value))
            return string.Empty;
        return Convert
            .ToHexString(inArray: SHA256.HashData(source: Encoding.UTF8.GetBytes(s: value)))
            .ToLower();
    }

    /// <summary>
    /// Masks a portion of the string with a specified character.
    /// </summary>
    /// <remarks>
    /// This method replaces a specified portion of a string with mask characters while preserving the original
    /// length. It safely handles null inputs and validates index boundaries to prevent exceptions.
    ///
    /// Example usage:
    /// <example>
    /// <code>
    /// string creditCard = "1234567890123456";
    /// string result1 = creditCard.Mask(startIndex: 6, length: 6);           // returns "123456******3456"
    ///
    /// string email = "<EMAIL>";
    /// string result2 = email.Mask(startIndex: 2, length: 4, maskChar: '#'); // returns "us####@example.com"
    ///
    /// string phone = "1234567890";
    /// string result3 = phone.Mask(startIndex: 3, length: 4);                // returns "123****890"
    ///
    /// string nullString = null;
    /// string result4 = nullString.Mask(startIndex: 0, length: 5);           // returns ""
    ///
    /// // Invalid indices return original string
    /// string text = "Hello";
    /// string result5 = text.Mask(startIndex: 10, length: 2);               // returns "Hello"
    /// </code>
    /// </example>
    ///
    /// > [!NOTE]
    /// > The method preserves characters outside the masked region
    ///
    /// > [!TIP]
    /// > Useful for hiding sensitive information like credit card numbers or personal data
    ///
    /// > [!IMPORTANT]
    /// > Returns original string if start index or length are invalid
    /// </remarks>
    /// <param name="value">The string to mask. Can be null.</param>
    /// <param name="startIndex">Zero-based starting position of the masking operation.</param>
    /// <param name="length">Number of characters to mask.</param>
    /// <param name="maskChar">Character to use for masking. Defaults to '*'.</param>
    /// <returns>
    /// A string with the specified portion masked.
    /// Returns empty string if input is null.
    /// Returns original string if indices are invalid.
    /// </returns>
    /// <seealso href="https://learn.microsoft.com/en-us/dotnet/api/system.string.tochararray">String.ToCharArray Method</seealso>
    /// <seealso href="https://learn.microsoft.com/en-us/dotnet/api/system.string">String Class</seealso>
    public static string Mask(this string? value, int startIndex, int length, char maskChar = '*')
    {
        if (string.IsNullOrEmpty(value: value))
            return string.Empty;

        if (startIndex < 0 || length < 0 || startIndex + length > value.Length)
            return value;

        var chars = value.ToCharArray();
        for (int i = startIndex; i < startIndex + length; i++)
            chars[i] = maskChar;

        return new string(value: chars);
    }

    /// <summary>
    /// Checks if string contains only numeric characters.
    /// </summary>
    /// <remarks>
    /// This method validates whether a string consists entirely of numeric digits (0-9).
    /// It safely handles null inputs by returning false. Empty strings also return false.
    ///
    /// Example usage:
    /// <example>
    /// <code>
    /// string number = "12345";
    /// bool result1 = number.IsNumeric();                // returns true
    ///
    /// string mixed = "123abc";
    /// bool result2 = mixed.IsNumeric();                 // returns false
    ///
    /// string decimal = "123.45";
    /// bool result3 = decimal.IsNumeric();               // returns false
    ///
    /// string empty = "";
    /// bool result4 = empty.IsNumeric();                 // returns false
    ///
    /// string nullString = null;
    /// bool result5 = nullString.IsNumeric();            // returns false
    /// </code>
    /// </example>
    ///
    /// > [!NOTE]
    /// > Only considers 0-9 as numeric characters
    ///
    /// > [!TIP]
    /// > For decimal validation, consider using decimal.TryParse instead
    ///
    /// > [!IMPORTANT]
    /// > Special characters like decimal points or negative signs are not considered numeric
    /// </remarks>
    /// <param name="value">The string to check. Can be null.</param>
    /// <returns>
    /// True if string contains only numeric digits.
    /// False if string is null, empty, or contains any non-digit characters.
    /// </returns>
    /// <seealso href="https://learn.microsoft.com/en-us/dotnet/api/system.char.isdigit">Char.IsDigit Method</seealso>
    public static bool IsNumeric(this string? value) =>
        !string.IsNullOrEmpty(value: value) && value.All(predicate: char.IsDigit);

    /// <summary>
    /// Checks if string contains only alphabetic characters.
    /// </summary>
    /// <remarks>
    /// This method validates whether a string consists entirely of letters (A-Z, a-z).
    /// It safely handles null inputs by returning false. Empty strings also return false.
    /// Supports Unicode letters from any culture.
    ///
    /// Example usage:
    /// <example>
    /// <code>
    /// string text = "Hello";
    /// bool result1 = text.IsAlphabetic();               // returns true
    ///
    /// string mixed = "Hello123";
    /// bool result2 = mixed.IsAlphabetic();              // returns false
    ///
    /// string unicode = "こんにちは";
    /// bool result3 = unicode.IsAlphabetic();            // returns true
    ///
    /// string special = "Hello!";
    /// bool result4 = special.IsAlphabetic();            // returns false
    ///
    /// string nullString = null;
    /// bool result5 = nullString.IsAlphabetic();         // returns false
    /// </code>
    /// </example>
    ///
    /// > [!NOTE]
    /// > Supports Unicode letters from all cultures
    ///
    /// > [!TIP]
    /// > Use IsAlphanumeric() if numbers are also acceptable
    ///
    /// > [!IMPORTANT]
    /// > Spaces and special characters are not considered alphabetic
    /// </remarks>
    /// <param name="value">The string to check. Can be null.</param>
    /// <returns>
    /// True if string contains only letters.
    /// False if string is null, empty, or contains any non-letter characters.
    /// </returns>
    /// <seealso href="https://learn.microsoft.com/en-us/dotnet/api/system.char.isletter">Char.IsLetter Method</seealso>
    public static bool IsAlphabetic(this string? value) =>
        !string.IsNullOrEmpty(value: value) && value.All(predicate: char.IsLetter);

    /// <summary>
    /// Checks if string contains only alphanumeric characters.
    /// </summary>
    /// <remarks>
    /// This method validates whether a string consists entirely of letters (A-Z, a-z) and numbers (0-9).
    /// It safely handles null inputs by returning false. Empty strings also return false.
    /// Supports Unicode letters from any culture.
    ///
    /// Example usage:
    /// <example>
    /// <code>
    /// string text = "Hello123";
    /// bool result1 = text.IsAlphanumeric();            // returns true
    ///
    /// string special = "Hello123!";
    /// bool result2 = special.IsAlphanumeric();         // returns false
    ///
    /// string unicode = "こんにちは123";
    /// bool result3 = unicode.IsAlphanumeric();         // returns true
    ///
    /// string space = "Hello 123";
    /// bool result4 = space.IsAlphanumeric();          // returns false
    ///
    /// string nullString = null;
    /// bool result5 = nullString.IsAlphanumeric();     // returns false
    /// </code>
    /// </example>
    ///
    /// > [!NOTE]
    /// > Supports Unicode letters from all cultures along with numeric digits
    ///
    /// > [!TIP]
    /// > Use IsAlphabetic() if only letters are acceptable
    ///
    /// > [!IMPORTANT]
    /// > Spaces, special characters, and punctuation marks are not considered alphanumeric
    /// </remarks>
    /// <param name="value">The string to check. Can be null.</param>
    /// <returns>
    /// True if string contains only letters and numbers.
    /// False if string is null, empty, or contains any non-alphanumeric characters.
    /// </returns>
    /// <seealso href="https://learn.microsoft.com/en-us/dotnet/api/system.char.isletterordigit">Char.IsLetterOrDigit Method</seealso>
    public static bool IsAlphanumeric(this string? value) =>
        !string.IsNullOrEmpty(value: value) && value.All(predicate: char.IsLetterOrDigit);

    /// <summary>
    /// Repeats a string a specified number of times.
    /// </summary>
    /// <remarks>
    /// This method creates a new string by concatenating the input string multiple times.
    /// It safely handles null inputs by returning an empty string.
    ///
    /// Example usage:
    /// <example>
    /// <code>
    /// string text = "Ha";
    /// string result1 = text.Repeat(count: 3);          // returns "HaHaHa"
    ///
    /// string symbol = "*";
    /// string result2 = symbol.Repeat(count: 5);        // returns "*****"
    ///
    /// string space = " ";
    /// string result3 = space.Repeat(count: 4);         // returns "    "
    ///
    /// string empty = "";
    /// string result4 = empty.Repeat(count: 3);         // returns ""
    ///
    /// string nullString = null;
    /// string result5 = nullString.Repeat(count: 2);    // returns ""
    /// </code>
    /// </example>
    ///
    /// > [!NOTE]
    /// > Memory usage increases linearly with the count parameter
    ///
    /// > [!TIP]
    /// > Useful for creating padding or repetitive string patterns
    ///
    /// > [!IMPORTANT]
    /// > Large count values may impact performance and memory usage
    /// </remarks>
    /// <param name="value">The string to repeat. Can be null.</param>
    /// <param name="count">Number of times to repeat the string. Must be non-negative.</param>
    /// <returns>
    /// A new string containing the input string repeated the specified number of times.
    /// Returns empty string if input is null or empty.
    /// </returns>
    /// <seealso href="https://learn.microsoft.com/en-us/dotnet/api/system.linq.enumerable.repeat">Enumerable.Repeat Method</seealso>
    /// <seealso href="https://learn.microsoft.com/en-us/dotnet/api/system.string.concat">String.Concat Method</seealso>
    public static string Repeat(this string? value, int count) =>
        string.IsNullOrEmpty(value: value)
            ? string.Empty
            : string.Concat(values: Enumerable.Repeat(element: value, count: count));

    /// <summary>
    /// Checks if string matches a wildcard pattern.
    /// </summary>
    /// <remarks>
    /// This method validates whether a string matches a specified wildcard pattern using * and ? wildcards.
    /// It safely handles null inputs by returning false. The pattern is converted to a regular expression internally.
    ///
    /// Example usage:
    /// <example>
    /// <code>
    /// string fileName = "document.txt";
    /// bool result1 = fileName.MatchesWildcard(pattern: "*.txt");        // returns true
    ///
    /// string code = "test123.cs";
    /// bool result2 = code.MatchesWildcard(pattern: "test???.cs");      // returns true
    ///
    /// string text = "Hello World";
    /// bool result3 = text.MatchesWildcard(pattern: "Hello*");          // returns true
    ///
    /// string email = "<EMAIL>";
    /// bool result4 = email.MatchesWildcard(pattern: "*@*.com");        // returns true
    ///
    /// string nullString = null;
    /// bool result5 = nullString.MatchesWildcard(pattern: "*");         // returns false
    /// </code>
    /// </example>
    ///
    /// > [!NOTE]
    /// > * matches zero or more characters
    /// > ? matches exactly one character
    ///
    /// > [!TIP]
    /// > Useful for file pattern matching and simple string pattern validation
    ///
    /// > [!IMPORTANT]
    /// > Pattern matching is case-sensitive
    /// </remarks>
    /// <param name="value">The string to check. Can be null.</param>
    /// <param name="pattern">
    /// The wildcard pattern to match against.
    /// Supports * (matches zero or more characters) and ? (matches one character).
    /// </param>
    /// <returns>
    /// True if string matches the wildcard pattern.
    /// False if string is null or doesn't match the pattern.
    /// </returns>
    /// <seealso href="https://learn.microsoft.com/en-us/dotnet/api/system.text.regularexpressions.regex">Regex Class</seealso>
    /// <seealso href="https://learn.microsoft.com/en-us/dotnet/standard/base-types/regular-expression-language-quick-reference">Regular Expression Language</seealso>
    public static bool MatchesWildcard(this string? value, string pattern)
    {
        if (string.IsNullOrEmpty(value: value))
            return false;

        return Regex.IsMatch(
            input: value,
            pattern: $"^{Regex.Escape(str: pattern).Replace(oldValue: "\\*", newValue: ".*").Replace(oldValue: "\\?", newValue: ".")}$"
        );
    }

    /// <summary>
    /// Converts string to proper case (first letter of each word capitalized).
    /// </summary>
    /// <remarks>
    /// This method capitalizes the first letter of each word in a string while converting the remaining letters
    /// to lowercase. It safely handles null inputs by returning an empty string.
    ///
    /// Example usage:
    /// <example>
    /// <code>
    /// string text = "hello world";
    /// string result1 = text.ToProperCase();              // returns "Hello World"
    ///
    /// string mixed = "JOHN DOE";
    /// string result2 = mixed.ToProperCase();             // returns "John Doe"
    ///
    /// string multiple = "the quick brown fox";
    /// string result3 = multiple.ToProperCase();          // returns "The Quick Brown Fox"
    ///
    /// string single = "programming";
    /// string result4 = single.ToProperCase();            // returns "Programming"
    ///
    /// string nullString = null;
    /// string result5 = nullString.ToProperCase();        // returns ""
    /// </code>
    /// </example>
    ///
    /// > [!NOTE]
    /// > Words are separated by spaces
    ///
    /// > [!TIP]
    /// > Useful for formatting names and titles
    ///
    /// > [!IMPORTANT]
    /// > Preserves existing word spacing
    /// </remarks>
    /// <param name="value">The string to convert. Can be null.</param>
    /// <returns>
    /// A string with the first letter of each word capitalized.
    /// Returns empty string if input is null.
    /// </returns>
    /// <seealso href="https://learn.microsoft.com/en-us/dotnet/api/system.char.toupper">Char.ToUpper Method</seealso>
    /// <seealso href="https://learn.microsoft.com/en-us/dotnet/api/system.string.tolower">String.ToLower Method</seealso>
    public static string ToProperCase(this string? value) =>
        string.IsNullOrEmpty(value: value)
            ? string.Empty
            : string.Join(
                separator: " ",
                values: value
                    .Split(separator: ' ')
                    .Select(selector: static word =>
                        word.Length > 0 ? char.ToUpper(c: word[0]) + word[1..].ToLower() : word
                    )
            );

    /// <summary>
    /// Converts string to camelCase format by removing spaces and capitalizing subsequent words.
    /// </summary>
    /// <remarks>
    /// This method transforms a string into camelCase format by removing spaces, underscores, and hyphens,
    /// then capitalizing the first letter of each word except the first one. Safely handles null inputs
    /// by returning an empty string.
    ///
    /// Example usage:
    /// <example>
    /// <code>
    /// string text = "Hello World";
    /// string result1 = text.ToCamelCase();              // returns "helloWorld"
    ///
    /// string withUnderscore = "first_second_third";
    /// string result2 = withUnderscore.ToCamelCase();    // returns "firstSecondThird"
    ///
    /// string withHyphen = "api-response-data";
    /// string result3 = withHyphen.ToCamelCase();        // returns "apiResponseData"
    ///
    /// string mixed = "JSON API Response";
    /// string result4 = mixed.ToCamelCase();             // returns "jsonApiResponse"
    ///
    /// string nullString = null;
    /// string result5 = nullString.ToCamelCase();        // returns ""
    /// </code>
    /// </example>
    ///
    /// > [!NOTE]
    /// > Handles multiple word separators: spaces, underscores, and hyphens
    ///
    /// > [!TIP]
    /// > Useful for converting strings to match JavaScript naming conventions
    ///
    /// > [!IMPORTANT]
    /// > All word separators are removed in the final output
    /// </remarks>
    /// <param name="value">The string to convert. Can be null.</param>
    /// <returns>
    /// A string in camelCase format.
    /// Returns empty string if input is null or empty.
    /// </returns>
    /// <seealso href="https://learn.microsoft.com/en-us/dotnet/api/system.char.tolower">Char.ToLower Method</seealso>
    /// <seealso href="https://learn.microsoft.com/en-us/dotnet/api/system.char.toupper">Char.ToUpper Method</seealso>
    public static string ToCamelCase(this string? value)
    {
        if (string.IsNullOrEmpty(value: value))
            return string.Empty;

        var words = value.Split(
            separator: [' ', '_', '-'],
            options: StringSplitOptions.RemoveEmptyEntries
        );

        var result = words[0].ToLower();
        for (int i = 1; i < words.Length; i++)
            result += char.ToUpper(c: words[i][0]) + words[i][1..].ToLower();
        return result;
    }

    /// <summary>
    /// Converts string to PascalCase format by capitalizing the first letter of each word.
    /// </summary>
    /// <remarks>
    /// This method transforms a string into PascalCase format by removing spaces, underscores, and hyphens,
    /// then capitalizing the first letter of each word. Safely handles null inputs by returning an empty string.
    ///
    /// Example usage:
    /// <example>
    /// <code>
    /// string text = "hello world";
    /// string result1 = text.ToPascalCase();              // returns "HelloWorld"
    ///
    /// string withUnderscore = "user_first_name";
    /// string result2 = withUnderscore.ToPascalCase();    // returns "UserFirstName"
    ///
    /// string withHyphen = "api-response-type";
    /// string result3 = withHyphen.ToPascalCase();        // returns "ApiResponseType"
    ///
    /// string mixed = "JSON data format";
    /// string result4 = mixed.ToPascalCase();             // returns "JsonDataFormat"
    ///
    /// string nullString = null;
    /// string result5 = nullString.ToPascalCase();        // returns ""
    /// </code>
    /// </example>
    ///
    /// > [!NOTE]
    /// > Handles multiple word separators: spaces, underscores, and hyphens
    ///
    /// > [!TIP]
    /// > Useful for converting strings to match C# class and property naming conventions
    ///
    /// > [!IMPORTANT]
    /// > All word separators are removed in the final output
    /// </remarks>
    /// <param name="value">The string to convert. Can be null.</param>
    /// <returns>
    /// A string in PascalCase format.
    /// Returns empty string if input is null or empty.
    /// </returns>
    /// <seealso href="https://learn.microsoft.com/en-us/dotnet/api/system.char.toupper">Char.ToUpper Method</seealso>
    /// <seealso href="https://learn.microsoft.com/en-us/dotnet/standard/design-guidelines/capitalization-conventions">Capitalization Conventions</seealso>
    public static string ToPascalCase(this string? value)
    {
        if (string.IsNullOrEmpty(value: value))
            return string.Empty;

        return string.Join(
            separator: "",
            values: value
                .Split(separator: [' ', '_', '-'], options: StringSplitOptions.RemoveEmptyEntries)
                .Select(selector: static word => char.ToUpper(c: word[0]) + word[1..].ToLower())
        );
    }

    /// <summary>
    /// Converts PascalCase or camelCase string to snake_case format.
    /// </summary>
    /// <remarks>
    /// This method transforms a PascalCase or camelCase string into snake_case format by inserting underscores
    /// before capital letters and converting all characters to lowercase. Safely handles null inputs by returning
    /// an empty string.
    ///
    /// Example usage:
    /// <example>
    /// <code>
    /// string pascalCase = "InstallmentDetails";
    /// string result1 = pascalCase.ToSnakeCaseFromPascal();    // returns "installment_details"
    ///
    /// string camelCase = "installmentDetails";
    /// string result2 = camelCase.ToSnakeCaseFromPascal();     // returns "installment_details"
    ///
    /// string acronym = "JSONData";
    /// string result3 = acronym.ToSnakeCaseFromPascal();       // returns "json_data"
    ///
    /// string nullString = null;
    /// string result4 = nullString.ToSnakeCaseFromPascal();    // returns ""
    /// </code>
    /// </example>
    ///
    /// > [!NOTE]
    /// > Works with both PascalCase and camelCase inputs
    ///
    /// > [!TIP]
    /// > Useful for converting C# property names to database column names
    ///
    /// > [!IMPORTANT]
    /// > Consecutive capital letters are treated as separate words
    /// </remarks>
    /// <param name="value">The string to convert. Can be null.</param>
    /// <returns>
    /// A string in snake_case format.
    /// Returns empty string if input is null or empty.
    /// </returns>
    /// <seealso href="https://learn.microsoft.com/en-us/dotnet/api/system.text.regularexpressions.regex">Regex Class</seealso>
    public static string ToSnakeCaseFromPascal(this string? value)
    {
        if (string.IsNullOrEmpty(value: value))
            return string.Empty;

        return string.Concat(
                value.Select(
                    selector: (x, i) => i > 0 && char.IsUpper(c: x) ? $"_{x}" : x.ToString()
                )
            )
            .ToLower();
    }
}

namespace IDC.Utilities.Comm.Http;

public partial class HttpClientUtility
{
    private static Lazy<HttpClientUtility>? _instance;
    private readonly HttpClient _httpClient;
    private bool _disposed;
    private readonly SystemLogging? _systemLogging;

    /// <summary>
    /// Gets the singleton instance of the HttpClientUtility.
    /// </summary>
    /// <value>The singleton instance.</value>
    public static HttpClientUtility Instance
    {
        get
        {
            if (_instance is null)
                throw new InvalidOperationException(message: "HttpClientUtility not initialized.");

            return _instance.Value;
        }
    }
}

namespace IDC.Utilities.Models.API;

/// <summary>
/// Represents a standardized API response format with generic data payload.
/// </summary>
/// <typeparam name="T">The type of data payload that can be null.</typeparam>
/// <remarks>
/// Provides a consistent structure for API responses with status, message, and strongly-typed data handling.
/// Inherits base functionality from <see cref="APIResponse"/> and adds generic data support.
///
/// Response format patterns:
/// <list type="bullet">
/// <item><description>Single entity responses</description></item>
/// <item><description>Collection responses</description></item>
/// <item><description>Paginated data responses</description></item>
/// <item><description>Null data handling</description></item>
/// </list>
///
/// Example response formats:
/// <code>
/// // Single Entity Response
/// {
///   "status": "Success",
///   "message": "User retrieved successfully",
///   "data": {
///     "id": "123",
///     "username": "johndoe",
///     "email": "<EMAIL>"
///   }
/// }
///
/// // Collection Response
/// {
///   "status": "Success",
///   "message": "Users retrieved successfully",
///   "data": [
///     {
///       "id": "123",
///       "username": "johndoe"
///     },
///     {
///       "id": "124",
///       "username": "janedoe"
///     }
///   ]
/// }
///
/// // Null Data Response
/// {
///   "status": "Success",
///   "message": "No data found",
///   "data": null
/// }
/// </code>
///
/// > [!NOTE]
/// > All properties support null values for proper null handling
///
/// > [!TIP]
/// > Use appropriate data types for better type safety
///
/// > [!IMPORTANT]
/// > Always validate data before setting it in the response
///
/// > [!CAUTION]
/// > Ensure sensitive data is properly filtered before sending
/// </remarks>
/// <example>
/// <code>
/// // Single entity response
/// var userResponse = new APIResponseData&lt;UserModel&gt;()
///     .ChangeStatus(status: "Success")
///     .ChangeMessage(message: "User retrieved successfully")
///     .ChangeData(data: new UserModel
///     {
///         Id = "123",
///         Username = "johndoe",
///         Email = "<EMAIL>"
///     });
///
/// // Collection response
/// var usersResponse = new APIResponseData&lt;IEnumerable&lt;UserModel&gt;&gt;()
///     .ChangeStatus(status: "Success")
///     .ChangeMessage(message: "Users retrieved successfully")
///     .ChangeData(data: new[]
///     {
///         new UserModel { Id = "123", Username = "johndoe" },
///         new UserModel { Id = "124", Username = "janedoe" }
///     });
///
/// // Null data handling
/// var emptyResponse = new APIResponseData&lt;UserModel&gt;()
///     .ChangeStatus(status: "Success")
///     .ChangeMessage(message: "No user found")
///     .ChangeData(data: null);
/// </code>
/// </example>
/// <seealso cref="APIResponse"/>
/// <seealso href="https://learn.microsoft.com/en-us/dotnet/csharp/programming-guide/generics/">
/// Generics in C#
/// </seealso>
/// <seealso href="https://learn.microsoft.com/en-us/aspnet/core/web-api/action-return-types">
/// Controller action return types in ASP.NET Core web API
/// </seealso>
public class APIResponseData<T> : APIResponse
{
    /// <summary>
    /// Gets or sets the data payload of the API response.
    /// </summary>
    /// <value>
    /// A nullable instance of type <typeparamref name="T"/> representing the response data.
    /// The value can be null for responses with no data or when data is not found.
    /// </value>
    /// <remarks>
    /// This property serves as the main data container for the API response. It supports any type that can be
    /// serialized to JSON, including complex objects, collections, and primitive types.
    ///
    /// Common usage patterns:
    /// <list type="bullet">
    /// <item><description>Single entity responses (e.g., UserModel)</description></item>
    /// <item><description>Collection responses (e.g., IEnumerable&lt;UserModel&gt;)</description></item>
    /// <item><description>Primitive type responses (e.g., string, int)</description></item>
    /// <item><description>Complex nested object responses</description></item>
    /// </list>
    ///
    /// Example response structures:
    /// <code>
    /// // Single Entity
    /// {
    ///   "status": "Success",
    ///   "message": "User retrieved",
    ///   "data": {
    ///     "id": "123",
    ///     "name": "John Doe",
    ///     "email": "<EMAIL>"
    ///   }
    /// }
    ///
    /// // Collection
    /// {
    ///   "status": "Success",
    ///   "message": "Users retrieved",
    ///   "data": [
    ///     { "id": "123", "name": "John" },
    ///     { "id": "124", "name": "Jane" }
    ///   ]
    /// }
    ///
    /// // Null Data
    /// {
    ///   "status": "Success",
    ///   "message": "No data found",
    ///   "data": null
    /// }
    /// </code>
    ///
    /// > [!NOTE]
    /// > The property is internally settable to maintain encapsulation
    ///
    /// > [!TIP]
    /// > Use the ChangeData method for fluent API support
    ///
    /// > [!IMPORTANT]
    /// > Ensure data is properly sanitized before setting
    ///
    /// > [!CAUTION]
    /// > Avoid storing sensitive information in this property
    /// </remarks>
    /// <example>
    /// <code>
    /// // Single entity
    /// var response = new APIResponseData&lt;UserModel&gt;();
    /// response.Data = new UserModel
    /// {
    ///     Id = "123",
    ///     Name = "John Doe",
    ///     Email = "<EMAIL>"
    /// };
    ///
    /// // Collection
    /// var collectionResponse = new APIResponseData&lt;IEnumerable&lt;UserModel&gt;&gt;();
    /// collectionResponse.Data = new List&lt;UserModel&gt;
    /// {
    ///     new() { Id = "123", Name = "John" },
    ///     new() { Id = "124", Name = "Jane" }
    /// };
    ///
    /// // Null data
    /// var emptyResponse = new APIResponseData&lt;UserModel&gt;();
    /// emptyResponse.Data = null;
    /// </code>
    /// </example>
    /// <seealso cref="ChangeData"/>
    /// <seealso href="https://learn.microsoft.com/en-us/dotnet/csharp/programming-guide/generics/">
    /// C# Generics Documentation
    /// </seealso>
    /// <seealso href="https://learn.microsoft.com/en-us/dotnet/standard/serialization/system-text-json-how-to">
    /// JSON Serialization in .NET
    /// </seealso>
    public T? Data { get; internal set; }

    /// <summary>
    /// Changes the data payload of the API response while maintaining a fluent interface pattern.
    /// </summary>
    /// <param name="data">The new data payload to set, which can be null.</param>
    /// <returns>The current instance of <see cref="APIResponseData{T}"/> for method chaining.</returns>
    /// <remarks>
    /// Provides a type-safe way to update the response data while supporting method chaining.
    /// Supports various data types including single entities, collections, and primitive types.
    ///
    /// Common usage scenarios:
    /// <list type="bullet">
    /// <item><description>Setting single entity data (e.g., UserModel)</description></item>
    /// <item><description>Setting collection data (e.g., List&lt;UserModel&gt;)</description></item>
    /// <item><description>Setting primitive type data (e.g., string, int)</description></item>
    /// <item><description>Setting null data for empty responses</description></item>
    /// </list>
    ///
    /// > [!NOTE]
    /// > This method supports null data for handling empty or not found scenarios
    ///
    /// > [!TIP]
    /// > Use with other chain methods like ChangeStatus and ChangeMessage for complete response building
    ///
    /// > [!IMPORTANT]
    /// > Validate and sanitize data before setting to ensure response integrity
    ///
    /// > [!CAUTION]
    /// > Ensure sensitive data is properly filtered before including in the response
    ///
    /// > [!WARNING]
    /// > Large data sets should be paginated to prevent performance issues
    /// </remarks>
    /// <example>
    /// <code>
    /// // Single entity example
    /// var response = new APIResponseData&lt;UserModel&gt;()
    ///     .ChangeStatus(status: "Success")
    ///     .ChangeMessage(message: "User retrieved successfully")
    ///     .ChangeData(data: new UserModel
    ///     {
    ///         Id = "123",
    ///         Name = "John Doe",
    ///         Email = "<EMAIL>"
    ///     });
    ///
    /// // Collection example
    /// var collectionResponse = new APIResponseData&lt;IEnumerable&lt;UserModel&gt;&gt;()
    ///     .ChangeData(data: new List&lt;UserModel&gt;
    ///     {
    ///         new() { Id = "123", Name = "John" },
    ///         new() { Id = "124", Name = "Jane" }
    ///     });
    ///
    /// // Null data example
    /// var emptyResponse = new APIResponseData&lt;UserModel&gt;()
    ///     .ChangeData(data: null);
    /// </code>
    /// </example>
    /// <seealso cref="Data"/>
    /// <seealso cref="APIResponse"/>
    /// <seealso href="https://learn.microsoft.com/en-us/dotnet/csharp/programming-guide/classes-and-structs/methods#method-return-values">
    /// Method Return Values in C#
    /// </seealso>
    /// <seealso href="https://learn.microsoft.com/en-us/dotnet/standard/serialization/system-text-json-how-to">
    /// JSON Serialization Guidelines
    /// </seealso>
    public virtual APIResponseData<T> ChangeData(T? data)
    {
        Data = data;
        return this;
    }

    /// <summary>
    /// Changes the status of the API response.
    /// </summary>
    /// <param name="status">The new status value to set. Common values include "Success", "Error", "Warning",
    /// or "Info".</param>
    /// <returns>The current <see cref="APIResponseData{T}"/> instance for method chaining.</returns>
    /// <remarks>
    /// This method overrides the base class implementation to maintain the fluent interface pattern while providing
    /// type-safe status updates for API responses.
    ///
    /// > [!NOTE]
    /// > Status values are case-sensitive and should follow consistent casing conventions
    ///
    /// > [!TIP]
    /// > Combine with ChangeMessage for more detailed response information
    ///
    /// > [!IMPORTANT]
    /// > Status should reflect the actual outcome of the operation
    ///
    /// > [!WARNING]
    /// > Status should not contain sensitive information
    /// </remarks>
    /// <example>
    /// <code>
    /// // Basic status update
    /// var response = new APIResponseData&lt;UserModel&gt;()
    ///     .ChangeStatus(status: "Success");
    ///
    /// // Combined with other methods
    /// var fullResponse = new APIResponseData&lt;UserModel&gt;()
    ///     .ChangeStatus(status: "Success")
    ///     .ChangeMessage(message: "User created successfully")
    ///     .ChangeData(data: new UserModel
    ///     {
    ///         Id = "123",
    ///         Name = "John Doe"
    ///     });
    ///
    /// // Error status
    /// var errorResponse = new APIResponseData&lt;UserModel&gt;()
    ///     .ChangeStatus(status: "Error")
    ///     .ChangeMessage(message: "User not found");
    /// </code>
    /// </example>
    /// <seealso cref="APIResponse.ChangeStatus(string?)"/>
    /// <seealso cref="ChangeMessage"/>
    /// <seealso cref="ChangeData"/>
    /// <seealso href="https://learn.microsoft.com/en-us/dotnet/csharp/programming-guide/classes-and-structs/methods#virtual-and-override-methods">
    /// Virtual and override methods in C#
    /// </seealso>
    /// <seealso href="https://learn.microsoft.com/en-us/aspnet/core/web-api/handle-errors">
    /// Handle errors in ASP.NET Core web APIs
    /// </seealso>
    public override APIResponseData<T> ChangeStatus(string? status)
    {
        base.ChangeStatus(status: status);
        return this;
    }

    /// <summary>
    /// Changes the status of the API response using a Language instance and key.
    /// </summary>
    /// <param name="language">
    /// The <see cref="Language"/> instance to use for translation. Must be properly initialized with resource files.
    /// </param>
    /// <param name="key">The language key to lookup for the status message. Must exist in resource files.</param>
    /// <returns>The current <see cref="APIResponseData{T}"/> instance for method chaining.</returns>
    /// <exception cref="ArgumentNullException">Thrown when language or key parameters are null.</exception>
    /// <exception cref="KeyNotFoundException">Thrown when the specified key is not found in resource files.</exception>
    /// <exception cref="InvalidOperationException">Thrown when language instance is not properly initialized.</exception>
    /// <remarks>
    /// This method provides localization support for API response statuses using resource files. It maintains
    /// the fluent interface pattern while ensuring consistent status messages across different languages.
    ///
    /// Example resource file structure:
    /// <code>
    /// {
    ///   "api.status.success": "Success",
    ///   "api.status.error": "Error",
    ///   "api.status.not_found": "Not Found",
    ///   "api.status.validation_error": "Validation Error"
    /// }
    /// </code>
    ///
    /// > [!NOTE]
    /// > Status values are automatically translated based on the current culture of the Language instance
    ///
    /// > [!TIP]
    /// > Use consistent key hierarchies across all resource files for better maintainability
    ///
    /// > [!IMPORTANT]
    /// > Ensure all required translations exist in resource files before deployment
    ///
    /// > [!CAUTION]
    /// > Avoid using dynamic or concatenated keys as they may break localization
    ///
    /// > [!WARNING]
    /// > Missing translations will fall back to the default language
    /// </remarks>
    /// <example>
    /// <code>
    /// // Initialize language instance
    /// var languageInstance = new Language();
    ///
    /// // Basic status translation
    /// var response = new APIResponseData&lt;UserModel&gt;()
    ///     .ChangeStatus(
    ///         language: languageInstance,
    ///         key: "api.status.success"
    ///     );
    ///
    /// // Combined with other methods
    /// var fullResponse = new APIResponseData&lt;UserModel&gt;()
    ///     .ChangeStatus(
    ///         language: languageInstance,
    ///         key: "api.status.success"
    ///     )
    ///     .ChangeMessage(
    ///         language: languageInstance,
    ///         key: "api.messages.user_created"
    ///     )
    ///     .ChangeData(data: new UserModel
    ///     {
    ///         Id = "123",
    ///         Name = "John Doe"
    ///     });
    ///
    /// // Error status with localization
    /// var errorResponse = new APIResponseData&lt;UserModel&gt;()
    ///     .ChangeStatus(
    ///         language: languageInstance,
    ///         key: "api.status.not_found"
    ///     );
    /// </code>
    /// </example>
    /// <seealso cref="APIResponse.ChangeStatus(Language, string)"/>
    /// <seealso cref="Language"/>
    /// <seealso href="https://learn.microsoft.com/en-us/dotnet/standard/globalization-localization/">
    /// Globalization and localization in .NET
    /// </seealso>
    /// <seealso href="https://learn.microsoft.com/en-us/dotnet/core/extensions/localization">
    /// Localization in .NET Core
    /// </seealso>
    public override APIResponseData<T> ChangeStatus(Language language, string key)
    {
        base.ChangeStatus(language: language, key: key);
        return this;
    }

    /// <summary>
    /// Changes the message of the API response.
    /// </summary>
    /// <param name="message">The new message to set. Can be null for removing existing messages.</param>
    /// <returns>The current <see cref="APIResponseData{T}"/> instance for method chaining.</returns>
    /// <remarks>
    /// This method overrides the base class implementation to maintain the fluent interface pattern while providing
    /// type-safe message updates for API responses.
    ///
    /// Message guidelines:
    /// <list type="bullet">
    /// <item><description>Keep messages clear and concise</description></item>
    /// <item><description>Include relevant operation details</description></item>
    /// <item><description>Avoid technical jargon in user-facing messages</description></item>
    /// <item><description>Support multiple languages through localization</description></item>
    /// </list>
    ///
    /// Example response structure:
    /// <code>
    /// {
    ///   "status": "Success",
    ///   "message": "User data updated successfully",
    ///   "data": {
    ///     "id": "123",
    ///     "name": "John Doe",
    ///     "email": "<EMAIL>"
    ///   }
    /// }
    /// </code>
    ///
    /// > [!NOTE]
    /// > Messages should be meaningful and provide clear feedback about the operation result
    ///
    /// > [!TIP]
    /// > Use consistent message formats across your application
    ///
    /// > [!IMPORTANT]
    /// > Ensure messages don't contain sensitive information
    ///
    /// > [!CAUTION]
    /// > Long messages may impact response size and readability
    ///
    /// > [!WARNING]
    /// > Avoid including stack traces or technical details in production messages
    /// </remarks>
    /// <example>
    /// <code>
    /// // Basic message update
    /// var response = new APIResponseData&lt;UserModel&gt;()
    ///     .ChangeMessage(message: "User profile updated successfully");
    ///
    /// // Combined with status and data
    /// var fullResponse = new APIResponseData&lt;UserModel&gt;()
    ///     .ChangeStatus(status: "Success")
    ///     .ChangeMessage(message: "User created successfully")
    ///     .ChangeData(data: new UserModel
    ///     {
    ///         Id = "123",
    ///         Name = "John Doe",
    ///         Email = "<EMAIL>"
    ///     });
    ///
    /// // Error message
    /// var errorResponse = new APIResponseData&lt;UserModel&gt;()
    ///     .ChangeStatus(status: "Error")
    ///     .ChangeMessage(message: "Invalid user data provided");
    ///
    /// // Clearing message
    /// var clearMessage = new APIResponseData&lt;UserModel&gt;()
    ///     .ChangeMessage(message: null);
    /// </code>
    /// </example>
    /// <seealso cref="APIResponse.ChangeMessage(string?)"/>
    /// <seealso cref="ChangeStatus"/>
    /// <seealso cref="ChangeData"/>
    /// <seealso href="https://learn.microsoft.com/en-us/aspnet/core/web-api/handle-errors">
    /// Handle errors in ASP.NET Core web APIs
    /// </seealso>
    /// <seealso href="https://learn.microsoft.com/en-us/dotnet/standard/serialization/system-text-json-how-to">
    /// JSON Serialization Guidelines
    /// </seealso>
    public override APIResponseData<T> ChangeMessage(string? message)
    {
        base.ChangeMessage(message: message);
        return this;
    }

    /// <summary>
    /// Changes the message of the API response using a Language instance and key.
    /// </summary>
    /// <param name="language">
    /// The <see cref="Language"/> instance to use for translation. Must be properly initialized with resource files.
    /// <see href="https://learn.microsoft.com/en-us/dotnet/api/system.resources.resourcemanager">ResourceManager Class</see>
    /// </param>
    /// <param name="key">
    /// The language key to lookup for the message. Must exist in resource files.
    /// <see href="https://learn.microsoft.com/en-us/dotnet/core/extensions/localization">Localization in .NET</see>
    /// </param>
    /// <returns>The current <see cref="APIResponseData{T}"/> instance for method chaining.</returns>
    /// <exception cref="ArgumentNullException">Thrown when language or key parameters are null.</exception>
    /// <exception cref="KeyNotFoundException">Thrown when the specified key is not found in resource files.</exception>
    /// <exception cref="InvalidOperationException">Thrown when language instance is not properly initialized.</exception>
    /// <remarks>
    /// This method provides localization support for API response messages using resource files. It maintains
    /// the fluent interface pattern while ensuring consistent message translations across different languages.
    ///
    /// Resource key naming conventions:
    /// <list type="bullet">
    /// <item><description>api.messages.success - For successful operations</description></item>
    /// <item><description>api.messages.error - For error states</description></item>
    /// <item><description>api.messages.validation - For validation messages</description></item>
    /// <item><description>api.messages.info - For informational messages</description></item>
    /// </list>
    ///
    /// > [!NOTE]
    /// > Messages are automatically translated based on the current culture of the Language instance
    ///
    /// > [!TIP]
    /// > Use message templates with placeholders for dynamic content
    ///
    /// > [!IMPORTANT]
    /// > Ensure all required translations exist in resource files before deployment
    ///
    /// > [!CAUTION]
    /// > Avoid using dynamic or concatenated keys as they may break localization
    ///
    /// > [!WARNING]
    /// > Missing translations will fall back to the default language
    /// </remarks>
    /// <example>
    /// <code>
    /// // Initialize language instance
    /// var languageInstance = new Language();
    ///
    /// // Basic message translation
    /// var response = new APIResponseData&lt;UserModel&gt;()
    ///     .ChangeMessage(
    ///         language: languageInstance,
    ///         key: "api.messages.user.created"
    ///     );
    ///
    /// // Combined with other methods
    /// var fullResponse = new APIResponseData&lt;UserModel&gt;()
    ///     .ChangeStatus(
    ///         language: languageInstance,
    ///         key: "api.status.success"
    ///     )
    ///     .ChangeMessage(
    ///         language: languageInstance,
    ///         key: "api.messages.user.updated"
    ///     )
    ///     .ChangeData(data: new UserModel
    ///     {
    ///         Id = "123",
    ///         Name = "John Doe",
    ///         Email = "<EMAIL>"
    ///     });
    ///
    /// // Error message with localization
    /// var errorResponse = new APIResponseData&lt;UserModel&gt;()
    ///     .ChangeMessage(
    ///         language: languageInstance,
    ///         key: "api.messages.user.not_found"
    ///     );
    /// </code>
    /// </example>
    /// <seealso cref="APIResponse.ChangeMessage(Language, string)"/>
    /// <seealso cref="Language"/>
    /// <seealso href="https://learn.microsoft.com/en-us/dotnet/standard/globalization-localization/">
    /// Globalization and localization in .NET
    /// </seealso>
    /// <seealso href="https://learn.microsoft.com/en-us/aspnet/core/fundamentals/localization">
    /// Localization middleware in ASP.NET Core
    /// </seealso>
    public override APIResponseData<T> ChangeMessage(Language language, string key)
    {
        base.ChangeMessage(language: language, key: key);
        return this;
    }

    /// <summary>
    /// Changes the message of the API response using an exception.
    /// </summary>
    /// <param name="exception">
    /// The exception to extract the message from.
    /// <see href="https://learn.microsoft.com/en-us/dotnet/api/system.exception">Exception Class</see>
    /// </param>
    /// <param name="includeStackTrace">
    /// Whether to include the stack trace in the message. Default is false.
    /// <see href="https://learn.microsoft.com/en-us/dotnet/api/system.exception.stacktrace">StackTrace Property</see>
    /// </param>
    /// <returns>
    /// The current <see cref="APIResponseData{T}"/> instance for method chaining.
    /// <see href="https://learn.microsoft.com/en-us/dotnet/csharp/programming-guide/classes-and-structs/methods#return-values">
    /// Method Return Values</see>
    /// </returns>
    /// <remarks>
    /// This method overrides the base class implementation to maintain the fluent interface pattern.
    /// It sets the message to the exception details and optionally includes the stack trace.
    ///
    /// Exception message format:
    /// <code>
    /// {
    ///   "status": "Error",
    ///   "message": "An error occurred while processing the request: Invalid operation",
    ///   "stackTrace": "at MyNamespace.MyClass.MyMethod() in MyFile.cs:line 123\n...",
    ///   "data": null
    /// }
    /// </code>
    ///
    /// > [!NOTE]
    /// > The message is set using the base class method, ensuring consistency across all API responses
    ///
    /// > [!TIP]
    /// > Use this method when you want to set the API response message based on an exception
    ///
    /// > [!IMPORTANT]
    /// > Always log the full exception details separately for debugging purposes
    ///
    /// > [!CAUTION]
    /// > Including the stack trace in production environments may expose sensitive information
    ///
    /// > [!WARNING]
    /// > Stack traces should only be included in development or testing environments
    /// </remarks>
    /// <example>
    /// <code>
    /// try
    /// {
    ///     // Some operation that might throw an exception
    ///     throw new InvalidOperationException("An error occurred");
    /// }
    /// catch (Exception ex)
    /// {
    ///     // Basic exception handling
    ///     var response = new APIResponseData&lt;UserModel&gt;()
    ///         .ChangeMessage(
    ///             exception: ex,
    ///             includeStackTrace: false
    ///         );
    ///
    ///     // With status and stack trace
    ///     var detailedResponse = new APIResponseData&lt;UserModel&gt;()
    ///         .ChangeStatus(status: "Error")
    ///         .ChangeMessage(
    ///             exception: ex,
    ///             includeStackTrace: true
    ///         );
    ///
    ///     // Combined with data
    ///     var fullResponse = new APIResponseData&lt;UserModel&gt;()
    ///         .ChangeStatus(status: "PartialSuccess")
    ///         .ChangeMessage(
    ///             exception: ex,
    ///             includeStackTrace: false
    ///         )
    ///         .ChangeData(data: new UserModel
    ///         {
    ///             Id = "123",
    ///             Name = "John Doe"
    ///         });
    /// }
    /// </code>
    /// </example>
    /// <seealso cref="APIResponse.ChangeMessage(Exception, bool)"/>
    /// <seealso cref="Exception"/>
    /// <seealso href="https://learn.microsoft.com/en-us/dotnet/csharp/fundamentals/exceptions/">
    /// Exceptions and Exception Handling
    /// </seealso>
    /// <seealso href="https://learn.microsoft.com/en-us/aspnet/core/web-api/handle-errors">
    /// Handle errors in ASP.NET Core web APIs
    /// </seealso>
    public override APIResponseData<T> ChangeMessage(
        Exception exception,
        bool includeStackTrace = false
    )
    {
        base.ChangeMessage(exception: exception, includeStackTrace: includeStackTrace);
        return this;
    }

    /// <summary>
    /// Changes the message of the API response using an exception and logs it.
    /// </summary>
    /// <param name="exception">
    /// The exception to extract the message from.
    /// <see href="https://learn.microsoft.com/en-us/dotnet/api/system.exception">Exception Class</see>
    /// </param>
    /// <param name="logging">
    /// The <see cref="SystemLogging"/> instance to use for logging.
    /// <see href="https://learn.microsoft.com/en-us/dotnet/core/extensions/logging">Logging in .NET</see>
    /// </param>
    /// <param name="includeStackTrace">
    /// Whether to include the stack trace in the message. Default is false.
    /// <see href="https://learn.microsoft.com/en-us/dotnet/api/system.exception.stacktrace">StackTrace Property</see>
    /// </param>
    /// <returns>
    /// The current <see cref="APIResponseData{T}"/> instance for method chaining.
    /// <see href="https://learn.microsoft.com/en-us/dotnet/csharp/programming-guide/classes-and-structs/methods#return-values">
    /// Method Return Values</see>
    /// </returns>
    /// <exception cref="ArgumentNullException">
    /// Thrown when exception or logging parameters are null.
    /// </exception>
    /// <exception cref="InvalidOperationException">
    /// Thrown when logging instance is not properly configured.
    /// </exception>
    /// <remarks>
    /// This method combines exception handling with logging capabilities while maintaining the fluent interface pattern.
    /// It automatically logs the exception details and sets the API response message accordingly.
    ///
    /// Logging format:
    /// <code>
    /// {
    ///   "timestamp": "2024-03-20T10:30:45Z",
    ///   "level": "Error",
    ///   "exception": {
    ///     "message": "An error occurred while processing the request",
    ///     "type": "System.InvalidOperationException",
    ///     "stackTrace": "at MyNamespace.MyClass.MyMethod() in MyFile.cs:line 123"
    ///   },
    ///   "additionalData": {
    ///     "endpoint": "/api/users",
    ///     "method": "POST",
    ///     "userId": "user123"
    ///   }
    /// }
    /// </code>
    ///
    /// > [!NOTE]
    /// > Exception details are automatically structured for consistent logging format
    ///
    /// > [!TIP]
    /// > Use structured logging with correlation IDs for better traceability
    ///
    /// > [!IMPORTANT]
    /// > Configure appropriate log levels and sinks before deployment
    ///
    /// > [!CAUTION]
    /// > Ensure sensitive data is properly redacted in logs
    ///
    /// > [!WARNING]
    /// > High-volume exceptions may impact logging performance
    /// </remarks>
    /// <example>
    /// <code>
    /// try
    /// {
    ///     // Some operation that might throw an exception
    ///     throw new InvalidOperationException("An error occurred");
    /// }
    /// catch (Exception ex)
    /// {
    ///     var logging = new SystemLogging();
    ///
    ///     // Basic exception handling with logging
    ///     var response = new APIResponseData&lt;UserModel&gt;()
    ///         .ChangeMessage(
    ///             exception: ex,
    ///             logging: logging,
    ///             includeStackTrace: false
    ///         );
    ///
    ///     // With custom status and data
    ///     var fullResponse = new APIResponseData&lt;UserModel&gt;()
    ///         .ChangeStatus(status: "Error")
    ///         .ChangeMessage(
    ///             exception: ex,
    ///             logging: logging,
    ///             includeStackTrace: true
    ///         )
    ///         .ChangeData(data: new UserModel
    ///         {
    ///             Id = "123",
    ///             Name = "John Doe",
    ///             LastError = ex.Message
    ///         });
    /// }
    /// </code>
    /// </example>
    /// <seealso cref="APIResponse.ChangeMessage(Exception, SystemLogging, bool)"/>
    /// <seealso cref="SystemLogging"/>
    /// <seealso cref="Exception"/>
    /// <seealso href="https://learn.microsoft.com/en-us/dotnet/core/extensions/logging">
    /// Logging in .NET
    /// </seealso>
    /// <seealso href="https://learn.microsoft.com/en-us/aspnet/core/fundamentals/logging/">
    /// Logging in ASP.NET Core
    /// </seealso>
    public override APIResponseData<T> ChangeMessage(
        Exception exception,
        SystemLogging logging,
        bool includeStackTrace = false
    )
    {
        base.ChangeMessage(
            exception: exception,
            logging: logging,
            includeStackTrace: includeStackTrace
        );
        return this;
    }
}

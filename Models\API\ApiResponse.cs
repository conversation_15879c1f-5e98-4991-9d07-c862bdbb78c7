using IDC.Utilities.Extensions;

namespace IDC.Utilities.Models.API;

/// <summary>
/// Represents a standardized API response structure with status and message information.
/// </summary>
/// <remarks>
/// This class provides a consistent response format for API operations, including success and error scenarios.
/// It supports method chaining, localization, exception handling, and system logging integration.
///
/// Example response formats:
/// <code>
/// // Success Response
/// {
///   "status": "Success",
///   "message": "User profile updated successfully"
/// }
///
/// // Error Response
/// {
///   "status": "Error",
///   "message": "Database operation failed: Connection timeout"
/// }
///
/// // Validation Response
/// {
///   "status": "Failed",
///   "message": "Invalid input: Email address is required"
/// }
/// </code>
///
/// > [!NOTE]
/// > All response methods support method chaining for fluent API usage
///
/// > [!TIP]
/// > Use appropriate status values: "Success", "Failed", or "Error"
///
/// > [!IMPORTANT]
/// > Always handle sensitive information appropriately in error messages
///
/// > [!CAUTION]
/// > Stack traces should only be included in development environments
/// </remarks>
/// <example>
/// <code>
/// // Basic usage
/// var response = new APIResponse()
///     .ChangeStatus(status: "Success")
///     .ChangeMessage(message: "Operation completed successfully");
///
/// // With localization
/// response.ChangeMessage(
///     language: languageService,
///     key: "api.messages.success"
/// );
///
/// // Error handling
/// try
/// {
///     throw new DatabaseException("Connection failed");
/// }
/// catch (Exception ex)
/// {
///     response
///         .ChangeStatus(status: "Error")
///         .ChangeMessage(
///             exception: ex,
///             logging: loggingService,
///             includeStackTrace: false
///         );
/// }
/// </code>
/// </example>
/// <seealso cref="Status"/>
/// <seealso cref="Message"/>
/// <seealso href="https://learn.microsoft.com/en-us/aspnet/core/web-api/handle-errors">
/// Error handling in ASP.NET Core web APIs
/// </seealso>
/// <seealso href="https://learn.microsoft.com/en-us/aspnet/core/fundamentals/localization">
/// Globalization and localization in ASP.NET Core
/// </seealso>
public class APIResponse
{
    /// <summary>
    /// Gets or sets the status of the API response.
    /// </summary>
    /// <value>The status of the API response.</value>
    /// <remarks>
    /// The default value is "Success". This property can be set using the <see cref="ChangeStatus(string)"/>
    /// method or its overloads.
    ///
    /// Example usage:
    /// <example>
    /// <code>
    /// var response = new APIResponse();
    ///
    /// // Success case
    /// response.Status = "Success";
    ///
    /// // Failed case (business logic)
    /// response.Status = "Failed";
    ///
    /// // Error case (system error)
    /// response.Status = "Error";
    /// </code>
    /// </example>
    ///
    /// > [!NOTE]
    /// > Status is automatically set to "Success" by default when creating a new instance
    ///
    /// > [!TIP]
    /// > Use the <see cref="ChangeStatus(Language, string)"/> method for localized status messages
    ///
    /// > [!IMPORTANT]
    /// > Status should reflect the actual outcome of the operation for proper error handling
    /// </remarks>
    /// <seealso cref="ChangeStatus(string)"/>
    /// <seealso cref="ChangeStatus(Language, string)"/>
    /// <seealso href="https://learn.microsoft.com/en-us/aspnet/core/web-api/handle-errors">Error handling in ASP.NET Core web APIs</seealso>
    public string? Status { get; internal set; } = "Success";

    /// <summary>
    /// Gets or sets the message of the API response.
    /// </summary>
    /// <value>The message describing the result of the API operation.</value>
    /// <remarks>
    /// The default value is "API processing has been completed". This property can be set using various
    /// <see cref="ChangeMessage"/> method overloads to support different scenarios:
    ///
    /// <list type="bullet">
    /// <item><description>Direct string messages</description></item>
    /// <item><description>Localized messages via Language service</description></item>
    /// <item><description>Exception details with optional stack traces</description></item>
    /// <item><description>Logged messages with system logging integration</description></item>
    /// </list>
    ///
    /// Example response formats:
    /// <code>
    /// {
    ///   "message": "Operation completed successfully",
    ///   "status": "Success"
    /// }
    ///
    /// {
    ///   "message": "Invalid input parameters: id cannot be null",
    ///   "status": "Failed"
    /// }
    ///
    /// {
    ///   "message": "Database connection error: Connection timeout",
    ///   "status": "Error"
    /// }
    /// </code>
    ///
    /// > [!NOTE]
    /// > Messages should be clear, concise, and appropriate for the response status
    ///
    /// > [!TIP]
    /// > Use localized messages for international applications
    ///
    /// > [!IMPORTANT]
    /// > Avoid exposing sensitive information in error messages
    /// </remarks>
    /// <example>
    /// <code>
    /// var response = new APIResponse()
    ///     .ChangeMessage(message: "User profile updated successfully");
    ///
    /// // Localized message
    /// response.ChangeMessage(
    ///     language: languageService,
    ///     key: "api.messages.profile_updated"
    /// );
    ///
    /// // Exception handling
    /// try
    /// {
    ///     throw new DbException("Connection failed");
    /// }
    /// catch (Exception ex)
    /// {
    ///     response.ChangeMessage(
    ///         exception: ex,
    ///         logging: loggingService,
    ///         includeStackTrace: false
    ///     );
    /// }
    /// </code>
    /// </example>
    /// <seealso cref="ChangeMessage(string)"/>
    /// <seealso cref="ChangeMessage(Language, string)"/>
    /// <seealso cref="ChangeMessage(Exception, bool)"/>
    /// <seealso cref="ChangeMessage(Exception, SystemLogging, bool)"/>
    /// <seealso href="https://learn.microsoft.com/en-us/aspnet/core/fundamentals/error-handling">Error Handling in ASP.NET Core</seealso>
    public string? Message { get; internal set; } = "API processing has been completed.";

    /// <summary>
    /// Changes the status of the API response using a string value.
    /// </summary>
    /// <param name="status">
    /// The new status to set. Common values include: "Success", "Failed", "Error".
    /// </param>
    /// <returns>
    /// The current <see cref="APIResponse"/> instance for method chaining.
    /// </returns>
    /// <exception cref="ArgumentNullException">
    /// Thrown when <paramref name="status"/> is null.
    /// </exception>
    /// <remarks>
    /// This method allows changing the status using a string value. The status should reflect the outcome
    /// of the API operation:
    ///
    /// <list type="bullet">
    /// <item><description>"Success" - Operation completed successfully</description></item>
    /// <item><description>"Failed" - Operation failed due to business logic</description></item>
    /// <item><description>"Error" - Operation failed due to system/technical error</description></item>
    /// </list>
    ///
    /// Example response format:
    /// <code>
    /// {
    ///   "status": "Failed",
    ///   "message": "Invalid input parameters"
    /// }
    /// </code>
    ///
    /// > [!NOTE]
    /// > The status value is case-sensitive
    ///
    /// > [!TIP]
    /// > Use consistent status values across your API for better error handling
    ///
    /// > [!IMPORTANT]
    /// > Status should accurately reflect the nature of the operation result
    /// </remarks>
    /// <example>
    /// <code>
    /// var response = new APIResponse();
    ///
    /// // Success scenario
    /// response.ChangeStatus(status: "Success");
    ///
    /// // Business logic failure
    /// response.ChangeStatus(status: "Failed");
    ///
    /// // System error
    /// response.ChangeStatus(status: "Error");
    /// </code>
    /// </example>
    /// <seealso cref="Status"/>
    /// <seealso cref="ChangeStatus(Language, string)"/>
    /// <seealso href="https://learn.microsoft.com/en-us/aspnet/core/web-api/handle-errors">
    /// Error handling in ASP.NET Core web APIs
    /// </seealso>
    public virtual APIResponse ChangeStatus(string? status)
    {
        Status = status ?? throw new ArgumentNullException(paramName: nameof(status));
        return this;
    }

    /// <summary>
    /// Changes the status of the API response using a Language instance.
    /// </summary>
    /// <param name="language">
    /// The <see cref="Language"/> instance to get the status from. Used for localization support.
    /// </param>
    /// <param name="key">
    /// The key to lookup the status message in the language resource files.
    /// </param>
    /// <returns>
    /// The current <see cref="APIResponse"/> instance for method chaining.
    /// </returns>
    /// <exception cref="ArgumentNullException">
    /// Thrown when <paramref name="language"/> or <paramref name="key"/> is null.
    /// </exception>
    /// <remarks>
    /// This method enables localization of status messages using the Language service. It's particularly
    /// useful for applications that need to support multiple languages.
    ///
    /// Common usage patterns:
    /// <list type="bullet">
    /// <item><description>Success messages: "api.status.success"</description></item>
    /// <item><description>Validation errors: "api.status.validation_failed"</description></item>
    /// <item><description>System errors: "api.status.system_error"</description></item>
    /// </list>
    ///
    /// Example response format:
    /// <code>
    /// {
    ///   "status": "Operasi Gagal",  // Localized status in Indonesian
    ///   "message": "Data tidak ditemukan"
    /// }
    /// </code>
    ///
    /// > [!NOTE]
    /// > The key must exist in the language resource files
    ///
    /// > [!TIP]
    /// > Use consistent key naming conventions across your application
    ///
    /// > [!IMPORTANT]
    /// > Ensure all required translations are available in the language resource files
    /// </remarks>
    /// <example>
    /// <code>
    /// var response = new APIResponse();
    ///
    /// // Success case with Indonesian language
    /// response.ChangeStatus(
    ///     language: languageService,
    ///     key: "api.status.success"
    /// );
    ///
    /// // Error case with English language
    /// response.ChangeStatus(
    ///     language: englishLanguageService,
    ///     key: "api.status.not_found"
    /// );
    /// </code>
    /// </example>
    /// <seealso cref="Language"/>
    /// <seealso cref="ChangeStatus(string)"/>
    /// <seealso href="https://learn.microsoft.com/en-us/aspnet/core/fundamentals/localization">
    /// Localization in ASP.NET Core
    /// </seealso>
    public virtual APIResponse ChangeStatus(Language language, string key)
    {
        Status = language.GetMessage(path: key);
        return this;
    }

    /// <summary>
    /// Changes the message of the API response using a string value.
    /// </summary>
    /// <param name="message">
    /// The new message to set. Should be descriptive and user-friendly.
    /// </param>
    /// <returns>
    /// The current <see cref="APIResponse"/> instance for method chaining.
    /// </returns>
    /// <exception cref="ArgumentNullException">
    /// Thrown when <paramref name="message"/> is null.
    /// </exception>
    /// <remarks>
    /// This method allows setting a custom message for the API response. Messages should be clear,
    /// informative, and appropriate for the end user.
    ///
    /// Common message patterns:
    /// <list type="bullet">
    /// <item><description>Success: "Operation completed successfully"</description></item>
    /// <item><description>Validation: "Invalid input: {specific reason}"</description></item>
    /// <item><description>Not Found: "Resource {id} not found"</description></item>
    /// <item><description>Error: "An unexpected error occurred"</description></item>
    /// </list>
    ///
    /// Example response format:
    /// <code>
    /// {
    ///   "status": "Success",
    ///   "message": "User profile updated successfully"
    /// }
    /// </code>
    ///
    /// > [!NOTE]
    /// > Messages should be concise but informative
    ///
    /// > [!TIP]
    /// > Include relevant identifiers or specific details when appropriate
    ///
    /// > [!IMPORTANT]
    /// > Never include sensitive information in messages
    ///
    /// > [!CAUTION]
    /// > Avoid technical details in user-facing messages
    /// </remarks>
    /// <example>
    /// <code>
    /// var response = new APIResponse();
    ///
    /// // Success message
    /// response.ChangeMessage(
    ///     message: "User profile updated successfully"
    /// );
    ///
    /// // Validation error
    /// response.ChangeMessage(
    ///     message: "Invalid email format: user@example is not a valid email address"
    /// );
    ///
    /// // Not found error
    /// response.ChangeMessage(
    ///     message: "User with ID 12345 was not found"
    /// );
    /// </code>
    /// </example>
    /// <seealso cref="Message"/>
    /// <seealso cref="ChangeMessage(Language, string)"/>
    /// <seealso cref="ChangeMessage(Exception, bool)"/>
    /// <seealso href="https://learn.microsoft.com/en-us/aspnet/core/fundamentals/error-handling">
    /// Error Handling in ASP.NET Core
    /// </seealso>
    public virtual APIResponse ChangeMessage(string? message)
    {
        Message = message ?? throw new ArgumentNullException(paramName: nameof(message));
        return this;
    }

    /// <summary>
    /// Changes the message of the API response using a Language instance for localization support.
    /// </summary>
    /// <param name="language">
    /// The <see cref="Language"/> instance used for message localization.
    /// <see href="https://learn.microsoft.com/en-us/aspnet/core/fundamentals/localization">Learn more about ASP.NET Core Localization</see>
    /// </param>
    /// <param name="key">
    /// The resource key to lookup the localized message.
    /// </param>
    /// <returns>
    /// The current <see cref="APIResponse"/> instance for method chaining.
    /// </returns>
    /// <exception cref="ArgumentNullException">
    /// Thrown when either <paramref name="language"/> or <paramref name="key"/> is null.
    /// </exception>
    /// <exception cref="KeyNotFoundException">
    /// Thrown when the specified <paramref name="key"/> is not found in the language resources.
    /// </exception>
    /// <remarks>
    /// This method facilitates internationalization by allowing messages to be retrieved from language
    /// resource files. It's essential for building multilingual APIs.
    ///
    /// Common key patterns:
    /// <list type="bullet">
    /// <item><description>Success messages: "api.messages.success"</description></item>
    /// <item><description>Validation errors: "api.messages.validation.{field}"</description></item>
    /// <item><description>Not found errors: "api.messages.notfound.{resource}"</description></item>
    /// <item><description>System errors: "api.messages.system.error"</description></item>
    /// </list>
    ///
    /// Example response formats:
    /// <code>
    /// // English Response
    /// {
    ///   "status": "Success",
    ///   "message": "User profile updated successfully"
    /// }
    ///
    /// // Indonesian Response
    /// {
    ///   "status": "Berhasil",
    ///   "message": "Profil pengguna berhasil diperbarui"
    /// }
    /// </code>
    ///
    /// > [!NOTE]
    /// > Ensure all keys exist in the language resource files before using them
    ///
    /// > [!TIP]
    /// > Organize keys hierarchically for better maintenance
    ///
    /// > [!IMPORTANT]
    /// > Keep translations consistent across all supported languages
    ///
    /// > [!WARNING]
    /// > Missing translations will throw KeyNotFoundException
    /// </remarks>
    /// <example>
    /// <code>
    /// var response = new APIResponse();
    ///
    /// // Basic usage
    /// response.ChangeMessage(
    ///     language: languageService,
    ///     key: "api.messages.user.profile.updated"
    /// );
    ///
    /// // With different languages
    /// response.ChangeMessage(
    ///     language: indonesianLanguage,
    ///     key: "api.messages.validation.email"
    /// );
    ///
    /// // Error messages
    /// response.ChangeMessage(
    ///     language: englishLanguage,
    ///     key: "api.messages.error.notfound"
    /// );
    /// </code>
    /// </example>
    /// <seealso cref="Message"/>
    /// <seealso cref="ChangeMessage(string)"/>
    /// <seealso cref="Language"/>
    /// <seealso href="https://learn.microsoft.com/en-us/aspnet/core/fundamentals/localization">
    /// ASP.NET Core Localization
    /// </seealso>
    public virtual APIResponse ChangeMessage(Language language, string key)
    {
        Message = language.GetMessage(path: key);
        return this;
    }

    /// <summary>
    /// Changes the message of the API response using an exception.
    /// </summary>
    /// <param name="exception">
    /// The <see cref="Exception"/> instance to extract the message from.
    /// <see href="https://learn.microsoft.com/en-us/dotnet/api/system.exception">Learn more about Exception Class</see>
    /// </param>
    /// <param name="includeStackTrace">
    /// Optional boolean parameter to include stack trace information. Defaults to false.
    /// </param>
    /// <returns>
    /// The current <see cref="APIResponse"/> instance for method chaining.
    /// </returns>
    /// <exception cref="ArgumentNullException">
    /// Thrown when <paramref name="exception"/> is null.
    /// </exception>
    /// <remarks>
    /// This method sets the message to the exception details and outputs it to the console. It's particularly
    /// useful for debugging and logging purposes in development environments.
    ///
    /// Message format patterns:
    /// <list type="bullet">
    /// <item><description>Basic: Exception message only</description></item>
    /// <item><description>Detailed: Exception message with inner exception details</description></item>
    /// <item><description>Full: Exception message with stack trace (when includeStackTrace is true)</description></item>
    /// </list>
    ///
    /// Example response format:
    /// <code>
    /// {
    ///   "status": "Error",
    ///   "message": "System.InvalidOperationException: Operation failed. ---> System.NullReferenceException:
    ///               Object reference not set to an instance of an object."
    /// }
    /// </code>
    ///
    /// > [!NOTE]
    /// > Stack traces are only included when explicitly requested
    ///
    /// > [!IMPORTANT]
    /// > Consider security implications when including stack traces in production
    ///
    /// > [!WARNING]
    /// > Stack traces may contain sensitive information
    ///
    /// > [!CAUTION]
    /// > Use stack traces only in development or secure environments
    /// </remarks>
    /// <example>
    /// <code>
    /// var response = new APIResponse();
    ///
    /// try
    /// {
    ///     throw new InvalidOperationException("Database connection failed");
    /// }
    /// catch (Exception ex)
    /// {
    ///     // Basic usage - message only
    ///     response.ChangeMessage(
    ///         exception: ex,
    ///         includeStackTrace: false
    ///     );
    ///
    ///     // With stack trace for debugging
    ///     response.ChangeMessage(
    ///         exception: ex,
    ///         includeStackTrace: true
    ///     );
    /// }
    /// </code>
    /// </example>
    /// <seealso cref="Message"/>
    /// <seealso cref="ChangeMessage(Exception, SystemLogging, bool)"/>
    /// <seealso href="https://learn.microsoft.com/en-us/aspnet/core/fundamentals/error-handling">
    /// Error Handling in ASP.NET Core
    /// </seealso>
    public virtual APIResponse ChangeMessage(Exception exception, bool includeStackTrace = false)
    {
        Message = exception.GetExceptionDetails(includeStackTrace: includeStackTrace);
        Console.WriteLine(value: Message);

        return this;
    }

    /// <summary>
    /// Changes the message of the API response using an exception and logs it using a logging service.
    /// </summary>
    /// <param name="exception">
    /// The <see cref="Exception"/> instance to extract the message from.
    /// <see href="https://learn.microsoft.com/en-us/dotnet/api/system.exception">Learn more about Exception Class</see>
    /// </param>
    /// <param name="logging">
    /// The <see cref="SystemLogging"/> instance for error logging.
    /// <see href="https://learn.microsoft.com/en-us/aspnet/core/fundamentals/logging">Learn more about ASP.NET Core Logging</see>
    /// </param>
    /// <param name="includeStackTrace">
    /// Optional boolean parameter to include stack trace information in the response message. Defaults to false.
    /// </param>
    /// <returns>
    /// The current <see cref="APIResponse"/> instance for method chaining.
    /// </returns>
    /// <exception cref="ArgumentNullException">
    /// Thrown when either <paramref name="exception"/> or <paramref name="logging"/> is null.
    /// </exception>
    /// <remarks>
    /// This method combines exception handling with system logging, providing both user feedback and system monitoring.
    /// The exception details are always logged with full stack trace, while the response message can be configured
    /// to include or exclude the stack trace.
    ///
    /// Logging patterns:
    /// <list type="bullet">
    /// <item><description>Error logs always include full stack trace</description></item>
    /// <item><description>Response message can be configured for detail level</description></item>
    /// <item><description>Structured logging with exception context</description></item>
    /// </list>
    ///
    /// Example response format:
    /// <code>
    /// {
    ///   "status": "Error",
    ///   "message": "Database operation failed: Connection timeout after 30 seconds"
    /// }
    /// </code>
    ///
    /// Example log entry:
    /// <code>
    /// [ERROR] Database operation failed
    /// Exception: System.TimeoutException: Connection timeout after 30 seconds
    ///    at DatabaseService.Connect() in DatabaseService.cs:line 45
    ///    at UserController.GetProfile() in UserController.cs:line 28
    /// </code>
    ///
    /// > [!NOTE]
    /// > Logs will always contain full exception details regardless of includeStackTrace setting
    ///
    /// > [!TIP]
    /// > Use structured logging for better error analysis
    ///
    /// > [!IMPORTANT]
    /// > Configure appropriate log levels in production
    ///
    /// > [!CAUTION]
    /// > Ensure sensitive data is properly redacted in logs
    /// </remarks>
    /// <example>
    /// <code>
    /// var response = new APIResponse();
    /// var logger = new SystemLogging();
    ///
    /// try
    /// {
    ///     throw new DatabaseException("Failed to connect to database");
    /// }
    /// catch (Exception ex)
    /// {
    ///     // Basic usage with logging
    ///     response.ChangeMessage(
    ///         exception: ex,
    ///         logging: logger,
    ///         includeStackTrace: false
    ///     );
    ///
    ///     // With stack trace for detailed debugging
    ///     response.ChangeMessage(
    ///         exception: ex,
    ///         logging: logger,
    ///         includeStackTrace: true
    ///     );
    /// }
    /// </code>
    /// </example>
    /// <seealso cref="Message"/>
    /// <seealso cref="SystemLogging"/>
    /// <seealso cref="ChangeMessage(Exception, bool)"/>
    /// <seealso href="https://learn.microsoft.com/en-us/aspnet/core/fundamentals/logging">
    /// Logging in .NET Core and ASP.NET Core
    /// </seealso>
    public virtual APIResponse ChangeMessage(
        Exception exception,
        SystemLogging logging,
        bool includeStackTrace = false
    )
    {
        Message = exception.GetExceptionDetails(includeStackTrace: includeStackTrace);
        logging.LogError(message: exception.GetExceptionDetails(includeStackTrace: true));

        return this;
    }
}

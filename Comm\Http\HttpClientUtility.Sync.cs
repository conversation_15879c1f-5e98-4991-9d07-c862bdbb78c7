using IDC.Utilities.Models.API;
using Newtonsoft.Json;

namespace IDC.Utilities.Comm.Http;

/// <summary>
/// Provides synchronous methods for the HttpClientUtility class.
/// </summary>
/// <remarks>
/// This class extends the HttpClientUtility with synchronous versions of all methods.
/// It provides the same functionality but with blocking calls for scenarios where
/// asynchronous programming is not suitable or required.
///
/// Features:
/// - Synchronous versions of all HTTP methods
/// - Same error handling and timeout configuration as the async version
/// - Same serialization/deserialization capabilities
///
/// Example usage:
/// <code>
/// // Basic GET request
/// var client = HttpClientUtility.Instance;
/// var user = client.Get&lt;UserModel&gt;("https://api.example.com/users/1");
///
/// // POST request with data
/// var newUser = new UserModel { Name = "<PERSON>", Email = "<EMAIL>" };
/// var createdUser = client.Post&lt;UserModel, UserModel&gt;(
///     "https://api.example.com/users",
///     newUser
/// );
/// </code>
///
/// > [!WARNING]
/// > Synchronous HTTP calls can lead to thread pool starvation in high-load scenarios.
/// > Use the asynchronous methods when possible, especially in ASP.NET applications.
///
/// > [!NOTE]
/// > These methods internally use the asynchronous methods with .Result, which can
/// > cause deadlocks in certain contexts. Use with caution.
/// </remarks>
public static class HttpClientUtilitySync
{
    /// <summary>
    /// Sends a GET request to the specified URI and returns the deserialized response.
    /// </summary>
    /// <typeparam name="TResponse">The type to deserialize the response to.</typeparam>
    /// <param name="client">The HttpClientUtility instance.</param>
    /// <param name="uri">The URI to send the request to.</param>
    /// <param name="headers">Optional headers to include in the request.</param>
    /// <param name="timeoutSeconds">Optional timeout in seconds (overrides default timeout).</param>
    /// <param name="ensureStatusCodeSuccess">Optional flag to ensure the status code is success.</param>
    /// <returns>The deserialized response object.</returns>
    /// <exception cref="HttpRequestException">Thrown when the request fails.</exception>
    /// <exception cref="TimeoutException">Thrown when the request times out.</exception>
    /// <exception cref="JsonException">Thrown when the response cannot be deserialized.</exception>
    /// <remarks>
    /// Sends a GET request to the specified URI and deserializes the response to the specified type.
    /// This is a synchronous version of the GetAsync method.
    ///
    /// Example usage:
    /// <code>
    /// // Get a user by ID
    /// var user = HttpClientUtility.Instance.Get&lt;UserModel&gt;(
    ///     "https://api.example.com/users/123",
    ///     new Dictionary&lt;string, string&gt; { { "Authorization", "Bearer token123" } }
    /// );
    ///
    /// // Get a list of users
    /// var users = HttpClientUtility.Instance.Get&lt;List&lt;UserModel&gt;&gt;(
    ///     "https://api.example.com/users"
    /// );
    /// </code>
    /// </remarks>
    public static TResponse? Get<TResponse>(
        this HttpClientUtility client,
        string uri,
        Dictionary<string, string>? headers = null,
        int? timeoutSeconds = null,
        bool ensureStatusCodeSuccess = true
    ) =>
        client
            .GetAsync<TResponse>(
                uri: uri,
                headers: headers,
                timeoutSeconds: timeoutSeconds,
                ensureStatusCodeSuccess: ensureStatusCodeSuccess
            )
            .GetAwaiter()
            .GetResult();

    /// <summary>
    /// Sends a POST request with the specified content to the URI and returns the deserialized response.
    /// </summary>
    /// <typeparam name="TRequest">The type of the request content.</typeparam>
    /// <typeparam name="TResponse">The type to deserialize the response to.</typeparam>
    /// <param name="client">The HttpClientUtility instance.</param>
    /// <param name="uri">The URI to send the request to.</param>
    /// <param name="content">The content to send in the request body.</param>
    /// <param name="headers">Optional headers to include in the request.</param>
    /// <param name="timeoutSeconds">Optional timeout in seconds (overrides default timeout).</param>
    /// <param name="ensureStatusCodeSuccess">Optional flag to ensure the status code is success.</param>
    /// <returns>The deserialized response object.</returns>
    /// <exception cref="HttpRequestException">Thrown when the request fails.</exception>
    /// <exception cref="TimeoutException">Thrown when the request times out.</exception>
    /// <exception cref="JsonException">Thrown when the response cannot be deserialized.</exception>
    /// <remarks>
    /// Sends a POST request with the specified content to the URI and deserializes the response.
    /// This is a synchronous version of the PostAsync method.
    ///
    /// Example usage:
    /// <code>
    /// // Create a new user
    /// var newUser = new UserModel { Name = "John Doe", Email = "<EMAIL>" };
    /// var createdUser = HttpClientUtility.Instance.Post&lt;UserModel, UserModel&gt;(
    ///     "https://api.example.com/users",
    ///     newUser
    /// );
    /// </code>
    /// </remarks>
    public static TResponse? Post<TRequest, TResponse>(
        this HttpClientUtility client,
        string uri,
        TRequest content,
        Dictionary<string, string>? headers = null,
        int? timeoutSeconds = null,
        bool ensureStatusCodeSuccess = true
    ) =>
        client
            .PostAsync<TRequest, TResponse>(
                uri: uri,
                content: content,
                headers: headers,
                timeoutSeconds: timeoutSeconds,
                ensureStatusCodeSuccess: ensureStatusCodeSuccess
            )
            .GetAwaiter()
            .GetResult();

    /// <summary>
    /// Sends a POST request with the specified content to the URI without expecting a typed response.
    /// </summary>
    /// <typeparam name="TRequest">The type of the request content.</typeparam>
    /// <param name="client">The HttpClientUtility instance.</param>
    /// <param name="uri">The URI to send the request to.</param>
    /// <param name="content">The content to send in the request body.</param>
    /// <param name="headers">Optional headers to include in the request.</param>
    /// <param name="timeoutSeconds">Optional timeout in seconds (overrides default timeout).</param>
    /// <returns>The raw HTTP response message.</returns>
    /// <exception cref="HttpRequestException">Thrown when the request fails.</exception>
    /// <exception cref="TimeoutException">Thrown when the request times out.</exception>
    /// <remarks>
    /// Sends a POST request with the specified content to the URI and returns the raw HTTP response.
    /// This is a synchronous version of the PostAsync method.
    ///
    /// Example usage:
    /// <code>
    /// // Send a notification without expecting a specific response type
    /// var notification = new NotificationModel { UserId = 123, Message = "Hello!" };
    /// var response = HttpClientUtility.Instance.Post(
    ///     "https://api.example.com/notifications",
    ///     notification
    /// );
    ///
    /// if (response.IsSuccessStatusCode)
    /// {
    ///     // Handle success
    /// }
    /// </code>
    /// </remarks>
    public static HttpResponseMessage? Post<TRequest>(
        this HttpClientUtility client,
        string uri,
        TRequest content,
        Dictionary<string, string>? headers = null,
        int? timeoutSeconds = null
    ) =>
        client
            .PostAsync(uri: uri, content: content, headers: headers, timeoutSeconds: timeoutSeconds)
            .GetAwaiter()
            .GetResult();

    /// <summary>
    /// Sends a PUT request with the specified content to the URI and returns the deserialized response.
    /// </summary>
    /// <typeparam name="TRequest">The type of the request content.</typeparam>
    /// <typeparam name="TResponse">The type to deserialize the response to.</typeparam>
    /// <param name="client">The HttpClientUtility instance.</param>
    /// <param name="uri">The URI to send the request to.</param>
    /// <param name="content">The content to send in the request body.</param>
    /// <param name="headers">Optional headers to include in the request.</param>
    /// <param name="timeoutSeconds">Optional timeout in seconds (overrides default timeout).</param>
    /// <param name="ensureStatusCodeSuccess">Optional flag to ensure the status code is success.</param>
    /// <returns>The deserialized response object.</returns>
    /// <exception cref="HttpRequestException">Thrown when the request fails.</exception>
    /// <exception cref="TimeoutException">Thrown when the request times out.</exception>
    /// <exception cref="JsonException">Thrown when the response cannot be deserialized.</exception>
    /// <remarks>
    /// Sends a PUT request with the specified content to the URI and deserializes the response.
    /// This is a synchronous version of the PutAsync method.
    ///
    /// Example usage:
    /// <code>
    /// // Update a user
    /// var updatedUser = new UserModel { Id = 123, Name = "John Updated", Email = "<EMAIL>" };
    /// var result = HttpClientUtility.Instance.Put&lt;UserModel, UserModel&gt;(
    ///     "https://api.example.com/users/123",
    ///     updatedUser
    /// );
    /// </code>
    /// </remarks>
    public static TResponse? Put<TRequest, TResponse>(
        this HttpClientUtility client,
        string uri,
        TRequest content,
        Dictionary<string, string>? headers = null,
        int? timeoutSeconds = null,
        bool ensureStatusCodeSuccess = true
    ) =>
        client
            .PutAsync<TRequest, TResponse>(
                uri: uri,
                content: content,
                headers: headers,
                timeoutSeconds: timeoutSeconds,
                ensureStatusCodeSuccess: ensureStatusCodeSuccess
            )
            .GetAwaiter()
            .GetResult();

    /// <summary>
    /// Sends a DELETE request to the specified URI and returns the deserialized response.
    /// </summary>
    /// <typeparam name="TResponse">The type to deserialize the response to.</typeparam>
    /// <param name="client">The HttpClientUtility instance.</param>
    /// <param name="uri">The URI to send the request to.</param>
    /// <param name="headers">Optional headers to include in the request.</param>
    /// <param name="timeoutSeconds">Optional timeout in seconds (overrides default timeout).</param>
    /// <param name="ensureStatusCodeSuccess">Optional flag to ensure the status code is success.</param>
    /// <returns>The deserialized response object.</returns>
    /// <exception cref="HttpRequestException">Thrown when the request fails.</exception>
    /// <exception cref="TimeoutException">Thrown when the request times out.</exception>
    /// <exception cref="JsonException">Thrown when the response cannot be deserialized.</exception>
    /// <remarks>
    /// Sends a DELETE request to the specified URI and deserializes the response.
    /// This is a synchronous version of the DeleteAsync method.
    ///
    /// Example usage:
    /// <code>
    /// // Delete a user and get deletion status
    /// var result = HttpClientUtility.Instance.Delete&lt;DeleteResult&gt;(
    ///     "https://api.example.com/users/123"
    /// );
    /// </code>
    /// </remarks>
    public static TResponse? Delete<TResponse>(
        this HttpClientUtility client,
        string uri,
        Dictionary<string, string>? headers = null,
        int? timeoutSeconds = null,
        bool ensureStatusCodeSuccess = true
    ) =>
        client
            .DeleteAsync<TResponse>(
                uri: uri,
                headers: headers,
                timeoutSeconds: timeoutSeconds,
                ensureStatusCodeSuccess: ensureStatusCodeSuccess
            )
            .GetAwaiter()
            .GetResult();

    /// <summary>
    /// Sends a DELETE request to the specified URI without expecting a typed response.
    /// </summary>
    /// <param name="client">The HttpClientUtility instance.</param>
    /// <param name="uri">The URI to send the request to.</param>
    /// <param name="headers">Optional headers to include in the request.</param>
    /// <param name="timeoutSeconds">Optional timeout in seconds (overrides default timeout).</param>
    /// <returns>The raw HTTP response message.</returns>
    /// <exception cref="HttpRequestException">Thrown when the request fails.</exception>
    /// <exception cref="TimeoutException">Thrown when the request times out.</exception>
    /// <remarks>
    /// Sends a DELETE request to the specified URI and returns the raw HTTP response.
    /// This is a synchronous version of the DeleteAsync method.
    ///
    /// Example usage:
    /// <code>
    /// // Delete a resource without expecting a specific response type
    /// var response = HttpClientUtility.Instance.Delete(
    ///     "https://api.example.com/resources/123"
    /// );
    ///
    /// if (response.IsSuccessStatusCode)
    /// {
    ///     // Resource deleted successfully
    /// }
    /// </code>
    /// </remarks>
    public static HttpResponseMessage Delete(
        this HttpClientUtility client,
        string uri,
        Dictionary<string, string>? headers = null,
        int? timeoutSeconds = null
    ) =>
        client
            .DeleteAsync(uri: uri, headers: headers, timeoutSeconds: timeoutSeconds)
            .GetAwaiter()
            .GetResult();

    /// <summary>
    /// Sends a GET request to the specified URI and returns the response as APIResponseData.
    /// </summary>
    /// <typeparam name="T">The type of data in the APIResponseData.</typeparam>
    /// <param name="client">The HttpClientUtility instance.</param>
    /// <param name="uri">The URI to send the request to.</param>
    /// <param name="headers">Optional headers to include in the request.</param>
    /// <param name="timeoutSeconds">Optional timeout in seconds.</param>
    /// <param name="ensureStatusCodeSuccess">Optional flag to ensure the status code is success.</param>
    /// <returns>The APIResponseData containing the response.</returns>
    /// <exception cref="HttpRequestException">Thrown when the request fails.</exception>
    /// <exception cref="TimeoutException">Thrown when the request times out.</exception>
    /// <exception cref="JsonException">Thrown when the response cannot be deserialized.</exception>
    /// <remarks>
    /// Sends a GET request to the specified URI and deserializes the response to APIResponseData.
    /// This is a synchronous version of the GetApiResponseDataAsync method.
    ///
    /// Example usage:
    /// <code>
    /// // Get user data from an API that returns APIResponseData
    /// var response = HttpClientUtility.Instance.GetApiResponseData&lt;UserModel&gt;(
    ///     "https://api.example.com/users/123"
    /// );
    ///
    /// if (response.IsSuccess)
    /// {
    ///     var user = response.Data;
    ///     Console.WriteLine($"User name: {user.Name}");
    /// }
    /// </code>
    /// </remarks>
    public static APIResponseData<T> GetApiResponseData<T>(
        this HttpClientUtility client,
        string uri,
        Dictionary<string, string>? headers = null,
        int? timeoutSeconds = null,
        bool ensureStatusCodeSuccess = true
    ) =>
        client
            .GetApiResponseDataAsync<T>(
                uri: uri,
                headers: headers,
                timeoutSeconds: timeoutSeconds,
                ensureStatusCodeSuccess: ensureStatusCodeSuccess
            )
            .GetAwaiter()
            .GetResult();

    /// <summary>
    /// Sends a POST request with the specified content to the URI and returns the response as APIResponseData.
    /// </summary>
    /// <typeparam name="TRequest">The type of the request content.</typeparam>
    /// <typeparam name="TResponse">The type of data in the APIResponseData.</typeparam>
    /// <param name="client">The HttpClientUtility instance.</param>
    /// <param name="uri">The URI to send the request to.</param>
    /// <param name="content">The content to send in the request body.</param>
    /// <param name="headers">Optional headers to include in the request.</param>
    /// <param name="timeoutSeconds">Optional timeout in seconds.</param>
    /// <param name="ensureStatusCodeSuccess">Optional flag to ensure the status code is success.</param>
    /// <returns>The APIResponseData containing the response.</returns>
    /// <exception cref="HttpRequestException">Thrown when the request fails.</exception>
    /// <exception cref="TimeoutException">Thrown when the request times out.</exception>
    /// <exception cref="JsonException">Thrown when the response cannot be deserialized.</exception>
    /// <remarks>
    /// Sends a POST request with the specified content to the URI and deserializes the response to APIResponseData.
    /// This is a synchronous version of the PostApiResponseDataAsync method.
    ///
    /// Example usage:
    /// <code>
    /// // Create a new user
    /// var newUser = new UserModel { Name = "John Doe", Email = "<EMAIL>" };
    /// var response = HttpClientUtility.Instance.PostApiResponseData&lt;UserModel, UserModel&gt;(
    ///     "https://api.example.com/users",
    ///     newUser
    /// );
    ///
    /// if (response.IsSuccess)
    /// {
    ///     var createdUser = response.Data;
    ///     Console.WriteLine($"Created user with ID: {createdUser.Id}");
    /// }
    /// </code>
    /// </remarks>
    public static APIResponseData<TResponse> PostApiResponseData<TRequest, TResponse>(
        this HttpClientUtility client,
        string uri,
        TRequest content,
        Dictionary<string, string>? headers = null,
        int? timeoutSeconds = null,
        bool ensureStatusCodeSuccess = true
    )
    {
        return client
            .PostApiResponseDataAsync<TRequest, TResponse>(
                uri: uri,
                content: content,
                headers: headers,
                timeoutSeconds: timeoutSeconds,
                ensureStatusCodeSuccess: ensureStatusCodeSuccess
            )
            .GetAwaiter()
            .GetResult();
    }
}

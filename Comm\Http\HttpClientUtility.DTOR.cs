namespace IDC.Utilities.Comm.Http;

public partial class HttpClientUtility
{
    /// <summary>
    /// Releases unmanaged resources and optionally releases managed resources.
    /// </summary>
    /// <param name="disposing">True to release managed resources; otherwise, false.</param>
    protected virtual void Dispose(bool disposing)
    {
        if (_disposed)
            return;

        if (disposing)
        {
            _httpClient.Dispose();
        }

        _disposed = true;
    }

    /// <summary>
    /// Releases all resources used by the HttpClientUtility.
    /// </summary>
    /// <remarks>
    /// This method calls Dispose(disposing: true) to free managed resources
    /// and suppresses finalization.
    /// <example>
    /// <code>
    /// HttpClientUtility client = new HttpClientUtility();
    /// client.Dispose();
    /// </code>
    /// </example>
    /// </remarks>
    public void Dispose()
    {
        Dispose(disposing: true);
        GC.SuppressFinalize(obj: this);
    }

    /// <summary>
    /// Finalizes an instance of the <see cref="HttpClientUtility"/> class.
    /// </summary>
    /// <remarks>
    /// Called by the runtime during finalization to release unmanaged resources.
    /// </remarks>
    ~HttpClientUtility()
    {
        Dispose(disposing: false);
    }
}

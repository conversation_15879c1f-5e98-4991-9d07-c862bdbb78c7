using System.ComponentModel.DataAnnotations;
using NpgsqlTypes;

namespace IDC.Utilities.Data;

public sealed partial class PostgreHelper
{
    /// <summary>
    /// Contains localized messages for PostgreHelper operations.
    /// </summary>
    /// <remarks>
    /// Provides standardized messages for various database operations and states:
    /// - Connection status messages
    /// - Transaction state messages
    /// - Disposal state messages
    ///
    /// > [!IMPORTANT]
    /// > All messages should be localized based on current culture
    ///
    /// > [!NOTE]
    /// > Default messages are in English, but can be overridden
    ///
    /// Example message format:
    /// <code>
    /// {
    ///   "type": "warning",
    ///   "code": "PG001",
    ///   "message": "The database transaction has already commenced.",
    ///   "timestamp": "2024-01-20T10:30:00Z"
    /// }
    /// </code>
    /// </remarks>
    /// <example>
    /// <code>
    /// // Custom messages
    /// var messages = new PostgreHelper.Messages
    /// {
    ///   MSG_CONNECTION_ESTABLISHED = "Koneksi ke database PostgreSQL telah dibuat.",
    ///   MSG_TRANSACTION_STARTED = "Transaksi database telah dimulai.",
    ///   MSG_ALREADY_DISPOSED = "PostgreHelper telah di-dispose."
    /// };
    ///
    /// // Usage with PostgreHelper
    /// var db = new PostgreHelper(
    ///   connectionString: "Host=localhost;Database=mydb;Username=user;Password=****",
    ///   messages: messages
    /// );
    /// </code>
    /// </example>
    /// <seealso href="https://www.npgsql.org/doc/logging.html">Npgsql Logging Documentation</seealso>
    /// <seealso href="https://learn.microsoft.com/en-us/dotnet/api/microsoft.extensions.logging">Microsoft.Extensions.Logging Documentation</seealso>
    public class Messages
    {
        /// <summary>
        /// Gets or sets the message displayed when a database connection is successfully established.
        /// </summary>
        /// <remarks>
        /// This message is used in logging and response objects to indicate successful connection state.
        /// The message can be customized for different languages or logging requirements.
        ///
        /// > [!NOTE]
        /// > Default message is in English but can be overridden during initialization
        ///
        /// > [!TIP]
        /// > Use culture-specific messages for better user experience
        ///
        /// Example response format:
        /// <code>
        /// {
        ///   "status": "success",
        ///   "code": "PG200",
        ///   "message": "The connection to the PostgreSQL database has been established.",
        ///   "timestamp": "2024-01-20T10:30:00Z",
        ///   "details": {
        ///     "host": "localhost",
        ///     "database": "mydb",
        ///     "port": 5432
        ///   }
        /// }
        /// </code>
        /// </remarks>
        /// <example>
        /// <code>
        /// var messages = new PostgreHelper.Messages
        /// {
        ///   MSG_CONNECTION_ESTABLISHED = "Database connection established successfully"
        /// };
        ///
        /// var db = new PostgreHelper(
        ///   connectionString: "Host=localhost;Database=mydb;Username=user;Password=****",
        ///   messages: messages
        /// );
        /// </code>
        /// </example>
        /// <seealso href="https://www.npgsql.org/doc/connection-string-parameters.html">Npgsql Connection Parameters</seealso>
        /// <seealso href="https://www.postgresql.org/docs/current/libpq-connect.html#LIBPQ-CONNSTRING">PostgreSQL Connection Strings</seealso>
        public string MSG_CONNECTION_ESTABLISHED { get; set; } =
            "The connection to the PostgreSQL database has been established.";

        /// <summary>
        /// Gets or sets the message displayed when a database connection has not been established.
        /// </summary>
        /// <remarks>
        /// This message is used in logging and exception messages to indicate failed or missing connection state.
        /// The message can be customized for different languages or logging requirements.
        ///
        /// > [!IMPORTANT]
        /// > This message is thrown when attempting operations without an active connection
        ///
        /// > [!TIP]
        /// > Always call Connect() before performing any database operations
        ///
        /// Example response format:
        /// <code>
        /// {
        ///   "status": "error",
        ///   "code": "PG401",
        ///   "message": "The connection to the PostgreSQL database has not been established.",
        ///   "timestamp": "2024-01-20T10:30:00Z",
        ///   "details": {
        ///     "connection_state": "closed",
        ///     "last_attempt": "2024-01-20T10:29:55Z",
        ///     "host": "localhost",
        ///     "database": "mydb"
        ///   }
        /// }
        /// </code>
        /// </remarks>
        /// <example>
        /// <code>
        /// var messages = new PostgreHelper.Messages
        /// {
        ///   MSG_CONNECTION_NOT_ESTABLISHED = "Koneksi ke database belum dibuat"
        /// };
        ///
        /// var db = new PostgreHelper(
        ///   connectionString: "Host=localhost;Database=mydb;Username=user;Password=****",
        ///   messages: messages
        /// );
        ///
        /// // This will throw an exception with the custom message
        /// db.ExecuteNonQuery("SELECT 1", out _); // Error: Connection not established
        /// </code>
        /// </example>
        /// <seealso href="https://www.npgsql.org/doc/connection-string-parameters.html">Npgsql Connection Parameters</seealso>
        /// <seealso href="https://www.postgresql.org/docs/current/libpq-connect.html">PostgreSQL Connection Documentation</seealso>
        public string MSG_CONNECTION_NOT_ESTABLISHED { get; set; } =
            "The connection to the PostgreSQL database has not been established.";

        /// <summary>
        /// Gets or sets the message displayed when attempting to start a transaction that is already in progress.
        /// </summary>
        /// <remarks>
        /// This message is used in logging and warning notifications to indicate duplicate transaction initialization.
        /// The message can be customized for different languages or logging requirements.
        ///
        /// > [!WARNING]
        /// > Multiple transaction starts will be ignored but logged as warnings
        ///
        /// > [!TIP]
        /// > Use TransactionCommit(reinitTransactions: true) to chain multiple transactions
        ///
        /// Example response format:
        /// <code>
        /// {
        ///   "status": "warning",
        ///   "code": "PG301",
        ///   "message": "The database transaction has already commenced.",
        ///   "timestamp": "2024-01-20T10:30:00Z",
        ///   "details": {
        ///     "connection_state": "open",
        ///     "transaction_state": "active",
        ///     "transaction_id": "tx_123456",
        ///     "started_at": "2024-01-20T10:29:55Z"
        ///   }
        /// }
        /// </code>
        /// </remarks>
        /// <example>
        /// <code>
        /// var messages = new PostgreHelper.Messages
        /// {
        ///   MSG_TRANSACTION_STARTED = "Transaksi database sudah berjalan"
        /// };
        ///
        /// var db = new PostgreHelper(
        ///   connectionString: "Host=localhost;Database=mydb;Username=user;Password=****",
        ///   messages: messages
        /// );
        ///
        /// // Second TransactionBegin will log warning with custom message
        /// db.Connect()
        ///   .TransactionBegin()
        ///   .TransactionBegin(); // Warning: Transaction already started
        /// </code>
        /// </example>
        /// <seealso href="https://www.npgsql.org/doc/transactions.html">Npgsql Transactions</seealso>
        /// <seealso href="https://www.postgresql.org/docs/current/sql-begin.html">PostgreSQL BEGIN Documentation</seealso>
        public string MSG_TRANSACTION_STARTED { get; set; } =
            "The database transaction has already commenced.";

        /// <summary>
        /// Gets or sets the message displayed when a database transaction has not been started.
        /// </summary>
        /// <remarks>
        /// This message is used in logging and exception messages to indicate missing transaction state.
        /// The message can be customized for different languages or logging requirements.
        ///
        /// > [!IMPORTANT]
        /// > This message is thrown when attempting transaction operations without an active transaction
        ///
        /// > [!TIP]
        /// > Always call TransactionBegin() before performing transaction operations
        ///
        /// Example response format:
        /// <code>
        /// {
        ///   "status": "error",
        ///   "code": "PG402",
        ///   "message": "The database transaction has not been initiated.",
        ///   "timestamp": "2024-01-20T10:30:00Z",
        ///   "details": {
        ///     "connection_state": "open",
        ///     "transaction_state": "none",
        ///     "last_operation": "commit_attempt"
        ///   }
        /// }
        /// </code>
        /// </remarks>
        /// <example>
        /// <code>
        /// var messages = new PostgreHelper.Messages
        /// {
        ///   MSG_TRANSACTION_NOT_STARTED = "Transaksi database belum dimulai"
        /// };
        ///
        /// var db = new PostgreHelper(
        ///   connectionString: "Host=localhost;Database=mydb;Username=user;Password=****",
        ///   messages: messages
        /// );
        ///
        /// // This will throw an exception with the custom message
        /// db.Connect()
        ///   .TransactionCommit(); // Error: Transaction not started
        /// </code>
        /// </example>
        /// <seealso href="https://www.npgsql.org/doc/transactions.html">Npgsql Transactions</seealso>
        /// <seealso href="https://www.postgresql.org/docs/current/tutorial-transactions.html">PostgreSQL Transactions Tutorial</seealso>
        public string MSG_TRANSACTION_NOT_STARTED { get; set; } =
            "The database transaction has not been initiated.";

        /// <summary>
        /// Gets or sets the message displayed when the PostgreHelper instance has been disposed.
        /// </summary>
        /// <remarks>
        /// This message is used in logging and exception messages to indicate disposal state.
        /// The message can be customized for different languages or logging requirements.
        ///
        /// > [!WARNING]
        /// > After disposal, all operations will throw ObjectDisposedException
        ///
        /// > [!IMPORTANT]
        /// > Ensure proper cleanup of resources before disposal
        ///
        /// Example response format:
        /// <code>
        /// {
        ///   "status": "error",
        ///   "code": "PG503",
        ///   "message": "The PostgreHelper has already been disposed.",
        ///   "timestamp": "2024-01-20T10:30:00Z",
        ///   "details": {
        ///     "disposed_at": "2024-01-20T10:29:55Z",
        ///     "connection_state": "closed",
        ///     "transaction_state": "disposed"
        ///   }
        /// }
        /// </code>
        /// </remarks>
        /// <example>
        /// <code>
        /// // Custom disposal message
        /// var messages = new PostgreHelper.Messages
        /// {
        ///   MSG_ALREADY_DISPOSED = "Database helper telah dinonaktifkan"
        /// };
        ///
        /// using var db = new PostgreHelper(
        ///   connectionString: "Host=localhost;Database=mydb;Username=user;Password=****",
        ///   messages: messages
        /// );
        ///
        /// // Will throw ObjectDisposedException with custom message after this point
        /// db.Dispose();
        /// </code>
        /// </example>
        /// <seealso href="https://learn.microsoft.com/en-us/dotnet/api/system.idisposable">IDisposable Interface</seealso>
        /// <seealso href="https://www.npgsql.org/doc/connection-lifecycle.html">Npgsql Connection Lifecycle</seealso>
        public string MSG_ALREADY_DISPOSED { get; set; } =
            "The PostgreHelper has already been disposed.";
    }

    /// <summary>
    /// Represents a stored procedure parameter.
    /// </summary>
    public class SPParameter
    {
        /// <summary>
        /// Gets or sets the name of the parameter.
        /// </summary>
        [Required(ErrorMessage = "Name is required")]
        public required string Name { get; set; }

        /// <summary>
        /// Gets or sets the value of the parameter.
        /// </summary>
        [Required(ErrorMessage = "Value is required")]
        public dynamic? Value { get; set; } = null;

        /// <summary>
        /// Gets or sets the data type of the parameter.
        /// </summary>
        public NpgsqlDbType DataType { get; set; } = NpgsqlDbType.Varchar;

        /// <summary>
        /// Sets the name, value, and data type of the parameter.
        /// </summary>
        /// <param name="name">The name of the parameter. Required.</param>
        /// <param name="value">The value of the parameter. Required.</param>
        /// <param name="dataType">The data type of the parameter.</param>
        /// <returns>The modified SPParameter instance.</returns>
        public virtual SPParameter With(
            [Required(ErrorMessage = "Name is required")] string name,
            [Required(ErrorMessage = "Value is required")] dynamic? value,
            NpgsqlDbType dataType
        )
        {
            Name = name;
            Value = value;
            DataType = dataType;

            return this;
        }

        /// <summary>
        /// Sets the name and value of the parameter and returns the updated parameter.
        /// </summary>
        /// <param name="name">The name of the parameter. Required.</param>
        /// <param name="value">The value of the parameter. Required.</param>
        /// <returns>The updated parameter.</returns>
        public virtual SPParameter With(
            [Required(ErrorMessage = "Name is required")] string name,
            [Required(ErrorMessage = "Value is required")] dynamic? value
        )
        {
            Name = name;
            Value = value;

            return this;
        }

        /// <summary>
        /// Sets the name of the parameter.
        /// </summary>
        /// <param name="name">The name of the parameter.</param>
        /// <returns>The instance of the parameter.</returns>
        /// <exception cref="ArgumentNullException">Thrown when the name is null or empty.</exception>
        public virtual SPParameter With([Required(ErrorMessage = "Name is required")] string name)
        {
            Name = name;
            return this;
        }

        /// <summary>
        /// Sets the value of the parameter and returns the modified parameter.
        /// </summary>
        /// <param name="value">The value to set.</param>
        /// <returns>The modified parameter.</returns>
        public virtual SPParameter With(
            [Required(ErrorMessage = "Value is required")] dynamic? value
        )
        {
            Value = value;
            return this;
        }

        /// <summary>
        /// Sets the data type of the parameter and returns the modified parameter.
        /// </summary>
        /// <param name="dataType">The NpgsqlDbType value representing the data type of the parameter.</param>
        /// <returns>The modified SPParameter object.</returns>
        public virtual SPParameter With(NpgsqlDbType dataType)
        {
            DataType = dataType;
            return this;
        }
    }

    public class SPCallInfo
    {
        /// <summary>
        /// Gets or sets the schema name.
        /// </summary>
        [Required(ErrorMessage = "Schema is required")]
        public string? Schema { get; set; }

        /// <summary>
        /// Gets or sets the stored procedure name.
        /// </summary>
        [Required(ErrorMessage = "SPName is required")]
        public string? SPName { get; set; }

        /// <summary>
        /// Gets or sets the stored procedure parameters.
        /// </summary>
        public SPParameter[]? Parameters { get; set; }
    }
}

using IDC.Utilities.Models.API;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;

namespace IDC.Utilities.Comm.Http;

/// <summary>
/// Provides extension methods for the HttpClientUtility class.
/// </summary>
/// <remarks>
/// This class extends the functionality of the HttpClientUtility class with methods
/// specifically designed to work with the APIResponseData type used in the application.
/// It provides convenient methods for handling API responses in a standardized way.
///
/// Features:
/// - Methods for handling APIResponseData responses
/// - Simplified error handling for API responses
/// - Support for JObject and dynamic data types
///
/// Example usage:
/// <code>
/// // Get data from an API that returns APIResponseData
/// var client = HttpClientUtility.Instance;
/// var userData = await client.GetApiResponseDataAsync&lt;UserModel&gt;("https://api.example.com/users/1");
///
/// // Check if the API call was successful
/// if (userData.IsSuccess)
/// {
///     // Use the data
///     var user = userData.Data;
/// }
/// </code>
///
/// > [!NOTE]
/// > These extension methods are designed to work with APIs that return data in the
/// > APIResponseData format used by this application.
/// </remarks>
public static class HttpClientUtilityExtensions
{
    /// <summary>
    /// Sends a GET request to the specified URI and returns the response as APIResponseData.
    /// </summary>
    /// <typeparam name="T">The type of data in the APIResponseData.</typeparam>
    /// <param name="client">The HttpClientUtility instance.</param>
    /// <param name="uri">The URI to send the request to.</param>
    /// <param name="headers">Optional headers to include in the request.</param>
    /// <param name="timeoutSeconds">Optional timeout in seconds.</param>
    /// <param name="ensureStatusCodeSuccess">Optional flag to ensure the status code is success.</param>
    /// <param name="cancellationToken">Optional cancellation token.</param>
    /// <returns>The APIResponseData containing the response.</returns>
    /// <exception cref="HttpRequestException">Thrown when the request fails.</exception>
    /// <exception cref="TaskCanceledException">Thrown when the request times out or is canceled.</exception>
    /// <exception cref="JsonException">Thrown when the response cannot be deserialized.</exception>
    /// <remarks>
    /// Sends a GET request to the specified URI and deserializes the response to APIResponseData.
    ///
    /// Example usage:
    /// <code>
    /// // Get user data from an API that returns APIResponseData
    /// var response = await HttpClientUtility.Instance.GetApiResponseDataAsync&lt;UserModel&gt;(
    ///     "https://api.example.com/users/123"
    /// );
    ///
    /// if (response.IsSuccess)
    /// {
    ///     var user = response.Data;
    ///     Console.WriteLine($"User name: {user.Name}");
    /// }
    /// else
    /// {
    ///     Console.WriteLine($"Error: {response.Message}");
    /// }
    /// </code>
    /// </remarks>
    public static async Task<APIResponseData<T>> GetApiResponseDataAsync<T>(
        this HttpClientUtility client,
        string uri,
        Dictionary<string, string>? headers = null,
        int? timeoutSeconds = null,
        bool ensureStatusCodeSuccess = true,
        CancellationToken cancellationToken = default
    )
    {
        return await client.GetAsync<APIResponseData<T>>(
                uri: uri,
                headers: headers,
                timeoutSeconds: timeoutSeconds,
                ensureStatusCodeSuccess: ensureStatusCodeSuccess,
                cancellationToken: cancellationToken
            ) ?? new APIResponseData<T>();
    }

    /// <summary>
    /// Sends a POST request with the specified content to the URI and returns the response as APIResponseData.
    /// </summary>
    /// <typeparam name="TRequest">The type of the request content.</typeparam>
    /// <typeparam name="TResponse">The type of data in the APIResponseData.</typeparam>
    /// <param name="client">The HttpClientUtility instance.</param>
    /// <param name="uri">The URI to send the request to.</param>
    /// <param name="content">The content to send in the request body.</param>
    /// <param name="headers">Optional headers to include in the request.</param>
    /// <param name="ensureStatusCodeSuccess">Optional flag to ensure the status code is success.</param>
    /// <param name="timeoutSeconds">Optional timeout in seconds.</param>
    /// <param name="cancellationToken">Optional cancellation token.</param>
    /// <returns>The APIResponseData containing the response.</returns>
    /// <exception cref="HttpRequestException">Thrown when the request fails.</exception>
    /// <exception cref="TaskCanceledException">Thrown when the request times out or is canceled.</exception>
    /// <exception cref="JsonException">Thrown when the response cannot be deserialized.</exception>
    /// <remarks>
    /// Sends a POST request with the specified content to the URI and deserializes the response to APIResponseData.
    ///
    /// Example usage:
    /// <code>
    /// // Create a new user
    /// var newUser = new UserModel { Name = "John Doe", Email = "<EMAIL>" };
    /// var response = await HttpClientUtility.Instance.PostApiResponseDataAsync&lt;UserModel, UserModel&gt;(
    ///     "https://api.example.com/users",
    ///     newUser
    /// );
    ///
    /// if (response.IsSuccess)
    /// {
    ///     var createdUser = response.Data;
    ///     Console.WriteLine($"Created user with ID: {createdUser.Id}");
    /// }
    /// </code>
    /// </remarks>
    public static async Task<APIResponseData<TResponse>> PostApiResponseDataAsync<
        TRequest,
        TResponse
    >(
        this HttpClientUtility client,
        string uri,
        TRequest content,
        Dictionary<string, string>? headers = null,
        int? timeoutSeconds = null,
        bool ensureStatusCodeSuccess = true,
        CancellationToken cancellationToken = default
    ) =>
        await client.PostAsync<TRequest, APIResponseData<TResponse>>(
            uri: uri,
            content: content,
            headers: headers,
            timeoutSeconds: timeoutSeconds,
            ensureStatusCodeSuccess: ensureStatusCodeSuccess,
            cancellationToken: cancellationToken
        ) ?? new APIResponseData<TResponse>();

    /// <summary>
    /// Sends a PUT request with the specified content to the URI and returns the response as APIResponseData.
    /// </summary>
    /// <typeparam name="TRequest">The type of the request content.</typeparam>
    /// <typeparam name="TResponse">The type of data in the APIResponseData.</typeparam>
    /// <param name="client">The HttpClientUtility instance.</param>
    /// <param name="uri">The URI to send the request to.</param>
    /// <param name="content">The content to send in the request body.</param>
    /// <param name="headers">Optional headers to include in the request.</param>
    /// <param name="timeoutSeconds">Optional timeout in seconds.</param>
    /// <param name="ensureStatusCodeSuccess">Optional flag to ensure the status code is success.</param>
    /// <param name="cancellationToken">Optional cancellation token.</param>
    /// <returns>The APIResponseData containing the response.</returns>
    /// <exception cref="HttpRequestException">Thrown when the request fails.</exception>
    /// <exception cref="TaskCanceledException">Thrown when the request times out or is canceled.</exception>
    /// <exception cref="JsonException">Thrown when the response cannot be deserialized.</exception>
    /// <remarks>
    /// Sends a PUT request with the specified content to the URI and deserializes the response to APIResponseData.
    ///
    /// Example usage:
    /// <code>
    /// // Update a user
    /// var updatedUser = new UserModel { Id = 123, Name = "John Updated", Email = "<EMAIL>" };
    /// var response = await HttpClientUtility.Instance.PutApiResponseDataAsync&lt;UserModel, UserModel&gt;(
    ///     "https://api.example.com/users/123",
    ///     updatedUser
    /// );
    ///
    /// if (response.IsSuccess)
    /// {
    ///     var result = response.Data;
    ///     Console.WriteLine($"Updated user: {result.Name}");
    /// }
    /// </code>
    /// </remarks>
    public static async Task<APIResponseData<TResponse>> PutApiResponseDataAsync<
        TRequest,
        TResponse
    >(
        this HttpClientUtility client,
        string uri,
        TRequest content,
        Dictionary<string, string>? headers = null,
        int? timeoutSeconds = null,
        bool ensureStatusCodeSuccess = true,
        CancellationToken cancellationToken = default
    )
    {
        return await client.PutAsync<TRequest, APIResponseData<TResponse>>(
                uri: uri,
                content: content,
                headers: headers,
                timeoutSeconds: timeoutSeconds,
                ensureStatusCodeSuccess: ensureStatusCodeSuccess,
                cancellationToken: cancellationToken
            ) ?? new APIResponseData<TResponse>();
    }

    /// <summary>
    /// Sends a DELETE request to the specified URI and returns the response as APIResponseData.
    /// </summary>
    /// <typeparam name="T">The type of data in the APIResponseData.</typeparam>
    /// <param name="client">The HttpClientUtility instance.</param>
    /// <param name="uri">The URI to send the request to.</param>
    /// <param name="headers">Optional headers to include in the request.</param>
    /// <param name="timeoutSeconds">Optional timeout in seconds.</param>
    /// <param name="ensureStatusCodeSuccess">Optional flag to ensure the status code is success.</param>
    /// <param name="cancellationToken">Optional cancellation token.</param>
    /// <returns>The APIResponseData containing the response.</returns>
    /// <exception cref="HttpRequestException">Thrown when the request fails.</exception>
    /// <exception cref="TaskCanceledException">Thrown when the request times out or is canceled.</exception>
    /// <exception cref="JsonException">Thrown when the response cannot be deserialized.</exception>
    /// <remarks>
    /// Sends a DELETE request to the specified URI and deserializes the response to APIResponseData.
    ///
    /// Example usage:
    /// <code>
    /// // Delete a user
    /// var response = await HttpClientUtility.Instance.DeleteApiResponseDataAsync&lt;DeleteResult&gt;(
    ///     "https://api.example.com/users/123"
    /// );
    ///
    /// if (response.IsSuccess)
    /// {
    ///     Console.WriteLine("User deleted successfully");
    /// }
    /// </code>
    /// </remarks>
    public static async Task<APIResponseData<T>> DeleteApiResponseDataAsync<T>(
        this HttpClientUtility client,
        string uri,
        Dictionary<string, string>? headers = null,
        int? timeoutSeconds = null,
        bool ensureStatusCodeSuccess = true,
        CancellationToken cancellationToken = default
    ) =>
        await client.DeleteAsync<APIResponseData<T>>(
            uri: uri,
            headers: headers,
            timeoutSeconds: timeoutSeconds,
            ensureStatusCodeSuccess: ensureStatusCodeSuccess,
            cancellationToken: cancellationToken
        ) ?? new APIResponseData<T>();

    /// <summary>
    /// Sends a GET request to the specified URI and returns the response as a JObject.
    /// </summary>
    /// <param name="client">The HttpClientUtility instance.</param>
    /// <param name="uri">The URI to send the request to.</param>
    /// <param name="headers">Optional headers to include in the request.</param>
    /// <param name="timeoutSeconds">Optional timeout in seconds.</param>
    /// <param name="ensureStatusCodeSuccess">Optional flag to ensure the status code is success.</param>
    /// <param name="cancellationToken">Optional cancellation token.</param>
    /// <returns>The response as a JObject.</returns>
    /// <exception cref="HttpRequestException">Thrown when the request fails.</exception>
    /// <exception cref="TaskCanceledException">Thrown when the request times out or is canceled.</exception>
    /// <exception cref="JsonException">Thrown when the response cannot be deserialized.</exception>
    /// <remarks>
    /// Sends a GET request to the specified URI and deserializes the response to a JObject.
    /// Useful when the response structure is not known in advance or is dynamic.
    ///
    /// Example usage:
    /// <code>
    /// // Get dynamic data as JObject
    /// var data = await HttpClientUtility.Instance.GetJObjectAsync(
    ///     "https://api.example.com/dynamic-data"
    /// );
    ///
    /// // Access properties dynamically
    /// var name = data["name"]?.ToString();
    /// var age = data["age"]?.Value&lt;int&gt;();
    /// </code>
    /// </remarks>
    public static async Task<JObject?> GetJObjectAsync(
        this HttpClientUtility client,
        string uri,
        Dictionary<string, string>? headers = null,
        int? timeoutSeconds = null,
        bool ensureStatusCodeSuccess = true,
        CancellationToken cancellationToken = default
    ) =>
        await client.GetAsync<JObject>(
            uri: uri,
            headers: headers,
            timeoutSeconds: timeoutSeconds,
            ensureStatusCodeSuccess: ensureStatusCodeSuccess,
            cancellationToken: cancellationToken
        );

    /// <summary>
    /// Sends a POST request with the specified content to the URI and returns the response as a JObject.
    /// </summary>
    /// <typeparam name="TRequest">The type of the request content.</typeparam>
    /// <param name="client">The HttpClientUtility instance.</param>
    /// <param name="uri">The URI to send the request to.</param>
    /// <param name="content">The content to send in the request body.</param>
    /// <param name="headers">Optional headers to include in the request.</param>
    /// <param name="timeoutSeconds">Optional timeout in seconds.</param>
    /// <param name="ensureStatusCodeSuccess">Optional flag to ensure the status code is success.</param>
    /// <param name="cancellationToken">Optional cancellation token.</param>
    /// <returns>The response as a JObject.</returns>
    /// <exception cref="HttpRequestException">Thrown when the request fails.</exception>
    /// <exception cref="TaskCanceledException">Thrown when the request times out or is canceled.</exception>
    /// <exception cref="JsonException">Thrown when the response cannot be deserialized.</exception>
    /// <remarks>
    /// Sends a POST request with the specified content to the URI and deserializes the response to a JObject.
    /// Useful when the response structure is not known in advance or is dynamic.
    ///
    /// Example usage:
    /// <code>
    /// // Post data and get dynamic response as JObject
    /// var request = new { name = "John", age = 30 };
    /// var response = await HttpClientUtility.Instance.PostJObjectAsync(
    ///     "https://api.example.com/process-data",
    ///     request
    /// );
    ///
    /// // Access properties dynamically
    /// var status = response["status"]?.ToString();
    /// var resultId = response["result"]?["id"]?.Value&lt;int&gt;();
    /// </code>
    /// </remarks>
    public static async Task<JObject?> PostJObjectAsync<TRequest>(
        this HttpClientUtility client,
        string uri,
        TRequest content,
        Dictionary<string, string>? headers = null,
        int? timeoutSeconds = null,
        bool ensureStatusCodeSuccess = true,
        CancellationToken cancellationToken = default
    ) =>
        await client.PostAsync<TRequest, JObject>(
            uri: uri,
            content: content,
            headers: headers,
            timeoutSeconds: timeoutSeconds,
            ensureStatusCodeSuccess: ensureStatusCodeSuccess,
            cancellationToken: cancellationToken
        );
}

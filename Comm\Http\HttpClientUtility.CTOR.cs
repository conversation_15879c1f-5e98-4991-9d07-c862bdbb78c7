using System.Net.Http.Headers;

namespace IDC.Utilities.Comm.Http;

public partial class HttpClientUtility
{
    /// <summary>
    /// Initializes the singleton instance with the specified system logging.
    /// </summary>
    /// <param name="systemLogging">
    /// Link: https://yourdocumentation.com/SystemLogging for custom SystemLogging type.
    /// </param>
    /// <returns>
    /// The initialized HttpClientUtility instance.
    /// </returns>
    /// <remarks>
    /// <example>
    /// <code>
    /// var clientUtility = HttpClientUtility.Initialize(systemLogging: yourLoggingInstance);
    /// </code>
    /// </example>
    /// </remarks>
    /// <exception cref="InvalidOperationException">
    /// Thrown if the instance is already initialized.
    /// </exception>
    public static HttpClientUtility Initialize(SystemLogging? systemLogging)
    {
        if (_instance is not null)
            throw new InvalidOperationException(message: "Instance already initialized.");

        _instance = new Lazy<HttpClientUtility>(
            valueFactory: () => new HttpClientUtility(systemLogging: systemLogging)
        );

        return Instance;
    }

    /// <summary>
    /// Initializes a new instance of the <see cref="HttpClientUtility"/> class with a system logging instance.
    /// </summary>
    /// <remarks>
    /// This public constructor allows injection of a <see cref="SystemLogging"/> instance.
    /// It initializes the HttpClient with default settings.
    /// <example>
    /// <code>
    /// var instance = new HttpClientUtility(systemLogging: loggingInstance);
    /// </code>
    /// </example>
    /// </remarks>
    /// <param name="systemLogging">See <see cref="SystemLogging"/> for details.</param>
    public HttpClientUtility(SystemLogging? systemLogging)
    {
        _systemLogging = systemLogging;
        _httpClient = new HttpClient { Timeout = TimeSpan.FromSeconds(value: 100) };

        // Set default headers
        _httpClient.DefaultRequestHeaders.Accept.Add(
            item: new MediaTypeWithQualityHeaderValue("application/json")
        );
    }

    /// <summary>
    /// Initializes a new instance of the <see cref="HttpClientUtility"/> class.
    /// </summary>
    /// <remarks>
    /// The constructor is private to enforce the singleton pattern.
    /// It initializes the HttpClient with default settings.
    /// </remarks>
    private HttpClientUtility()
    {
        _httpClient = new HttpClient { Timeout = TimeSpan.FromSeconds(value: 100) };

        // Set default headers
        _httpClient.DefaultRequestHeaders.Accept.Add(
            item: new MediaTypeWithQualityHeaderValue("application/json")
        );
    }

    /// <summary>
    /// Initializes a new instance of the <see cref="HttpClientUtility"/> class with a custom HttpClient.
    /// </summary>
    /// <remarks>
    /// This constructor is internal and used for testing purposes.
    /// It allows injecting a mock HttpClient for unit testing.
    /// </remarks>
    /// <param name="httpClient">The HttpClient to use.</param>
    internal HttpClientUtility(HttpClient httpClient)
    {
        _httpClient = httpClient ?? throw new ArgumentNullException(paramName: nameof(httpClient));
    }
}

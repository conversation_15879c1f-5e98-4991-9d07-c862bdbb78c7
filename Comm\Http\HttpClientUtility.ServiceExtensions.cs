using Microsoft.Extensions.DependencyInjection;

namespace IDC.Utilities.Comm.Http;

/// <summary>
/// Provides extension methods for registering HTTP client utilities with the dependency injection container.
/// </summary>
/// <remarks>
/// This class contains extension methods for the IServiceCollection interface to register
/// the HttpClientUtility as a service in the dependency injection container.
///
/// Example usage:
/// <code>
/// // In Program.cs or Startup.cs
/// services.AddHttpClientUtility();
/// </code>
///
/// > [!NOTE]
/// > The HttpClientUtility is registered as a singleton service.
/// </remarks>
public static class HttpClientUtilityServiceExtensions
{
    /// <summary>
    /// Adds the HttpClientUtility to the service collection.
    /// </summary>
    /// <param name="services">The service collection.</param>
    /// <param name="systemLogging">The optional SystemLogging instance.</param>
    /// <returns>The service collection for chaining.</returns>
    /// <remarks>
    /// Registers the HttpClientUtility as a singleton service in the dependency injection container.
    /// This allows the HttpClientUtility to be injected into other services.
    ///
    /// Example usage:
    /// <code>
    /// // In Program.cs or Startup.cs
    /// services.AddHttpClientUtility();
    ///
    /// // In a controller or service
    /// public class MyService
    /// {
    ///     private readonly HttpClientUtility _httpClient;
    ///
    ///     public MyService(HttpClientUtility httpClient)
    ///     {
    ///         _httpClient = httpClient;
    ///     }
    ///
    ///     public async Task&lt;UserModel&gt; GetUserAsync(int id)
    ///     {
    ///         return await _httpClient.GetAsync&lt;UserModel&gt;($"https://api.example.com/users/{id}");
    ///     }
    /// }
    /// </code>
    /// </remarks>
    public static IServiceCollection AddHttpClientUtility(
        this IServiceCollection services,
        SystemLogging? systemLogging = null
    )
    {
        // Register the HttpClientUtility as a singleton
        services.AddSingleton(
            implementationInstance: HttpClientUtility.Initialize(systemLogging: systemLogging)
        );
        return services;
    }
}

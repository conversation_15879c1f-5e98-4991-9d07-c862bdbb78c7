using System.Data;
using Microsoft.Data.Sqlite;
using Newtonsoft.Json.Linq;

namespace IDC.Utilities.Data;

public sealed partial class SQLiteHelper
{
    /// <summary>
    /// Checks if database connection is open and ready for operations
    /// </summary>
    /// <returns>
    /// A <see cref="bool"/> indicating connection status:
    /// <list type="bullet">
    ///   <item><description>true: Connection is open and ready</description></item>
    ///   <item><description>false: Connection is closed or invalid</description></item>
    /// </list>
    /// </returns>
    /// <remarks>
    /// Validates connection state without additional status message. Use this method to quickly check
    /// connection availability before executing database operations.
    ///
    /// Example request:
    /// <code>
    /// {
    ///   &quot;connectionString&quot;: &quot;Data Source=database.db&quot;
    /// }
    /// </code>
    ///
    /// Example usage:
    /// <example>
    /// <code>
    /// var db = new SQLiteHelper(connectionString: &quot;Data Source=database.db&quot;);
    ///
    /// if (db.IsConnected())
    ///     db.ExecuteNonQuery(query: &quot;INSERT INTO Users (Name) VALUES (@Name)&quot;, out _);
    /// </code>
    /// </example>
    ///
    /// &gt; [!NOTE]
    /// &gt; This method only checks connection state and does not attempt to reconnect if closed
    ///
    /// &gt; [!TIP]
    /// &gt; For detailed connection status with message, use <see cref="IsConnected(out string)"/> instead
    /// </remarks>
    /// <exception cref="ObjectDisposedException">
    /// Thrown when accessing disposed instance of <see cref="T:SQLiteHelper"/>
    /// </exception>
    /// <seealso cref="IsConnected(out string)"/>
    /// <seealso cref="CreateConnection"/>
    /// <seealso href="https://learn.microsoft.com/en-us/dotnet/standard/data/sqlite/">Microsoft.Data.Sqlite Documentation</seealso>
    public bool IsConnected()
    {
        ThrowIfDisposed();
        return _connection.State == ConnectionState.Open;
    }

    /// <summary>
    /// Checks connection status and returns detailed status message.
    /// </summary>
    /// <param name="message">
    /// Status message output containing connection details and state information.
    /// </param>
    /// <returns>
    /// A <see cref="bool"/> indicating connection status:
    /// <list type="bullet">
    ///   <item><description>true: Connection is open and ready</description></item>
    ///   <item><description>false: Connection is closed or invalid</description></item>
    /// </list>
    /// </returns>
    /// <remarks>
    /// Validates connection state and provides detailed status message for debugging and monitoring purposes.
    /// This overload is particularly useful when detailed connection information is needed.
    ///
    /// Example request:
    /// <code>
    /// {
    ///   "connectionString": "Data Source=database.db;Cache=Shared;Mode=ReadWrite"
    /// }
    /// </code>
    ///
    /// <example>
    /// <code>
    /// var db = new SQLiteHelper(connectionString: "Data Source=database.db");
    /// if (db.IsConnected(message: out string status))
    /// {
    ///     Console.WriteLine(value: $"Connection Status: {status}");
    ///     // Output: Connection Status: The connection to the SQLite database has been established.
    /// }
    /// </code>
    /// </example>
    ///
    /// > [!NOTE]
    /// > The status message includes connection string details with sensitive information masked
    ///
    /// > [!TIP]
    /// > Use this method during development and debugging for detailed connection diagnostics
    /// </remarks>
    /// <exception cref="ObjectDisposedException">
    /// Thrown when accessing disposed instance of <see cref="T:SQLiteHelper"/>
    /// </exception>
    /// <exception cref="InvalidOperationException">
    /// Thrown when connection validation fails unexpectedly
    /// </exception>
    /// <seealso cref="IsConnected()"/>
    /// <seealso cref="ConnectionIsValid"/>
    /// <seealso href="https://learn.microsoft.com/en-us/dotnet/standard/data/sqlite/connection-strings">
    /// SQLite Connection String Documentation
    /// </seealso>
    public bool IsConnected(out string message) => ConnectionIsValid(message: out message);

    /// <summary>
    /// Creates or reuses database connection with optional transaction support
    /// </summary>
    /// <param name="useTransaction">Flag to indicate whether to start a new transaction</param>
    /// <param name="disconnectFirst">Flag to indicate whether to disconnect existing connection before creating new one</param>
    /// <returns>Current <see cref="T:SQLiteHelper"/> instance for method chaining</returns>
    /// <remarks>
    /// Internal method that handles database connection management with support for transactions.
    /// Manages connection state and transaction initialization based on provided parameters.
    ///
    /// <example>
    /// <code>
    /// // Basic connection without transaction
    /// var db = new SQLiteHelper("Data Source=mydb.sqlite");
    /// db.CreateConnection(useTransaction: false);
    ///
    /// // Connection with new transaction
    /// db.CreateConnection(useTransaction: true);
    ///
    /// // Force new connection by disconnecting first
    /// db.CreateConnection(disconnectFirst: true);
    /// </code>
    /// </example>
    ///
    /// > [!IMPORTANT]
    /// > Always ensure proper transaction handling when useTransaction is true
    ///
    /// > [!NOTE]
    /// > Connection pooling is handled automatically by the SQLite provider
    /// </remarks>
    /// <exception cref="ObjectDisposedException">
    /// Thrown when attempting to use a disposed instance of <see cref="T:SQLiteHelper"/>
    /// </exception>
    /// <exception cref="SqliteException">
    /// Thrown when database connection fails
    /// </exception>
    /// <exception cref="InvalidOperationException">
    /// Thrown when attempting invalid connection state transitions
    /// </exception>
    /// <seealso cref="TransactionBegin"/>
    /// <seealso href="https://learn.microsoft.com/en-us/dotnet/standard/data/sqlite/connection-strings">SQLite Connection Strings</seealso>
    /// <seealso href="https://learn.microsoft.com/en-us/dotnet/standard/data/sqlite/transactions">SQLite Transaction Management</seealso>
    private SQLiteHelper CreateConnection(bool useTransaction = false, bool disconnectFirst = false)
    {
        try
        {
            ThrowIfDisposed();

            if (disconnectFirst)
                _connection.Close();

            if (!_connection.State.HasFlag(flag: ConnectionState.Open))
                _connection.Open();

            if (useTransaction)
                TransactionBegin();

            return this;
        }
        catch (Exception ex)
        {
            _logging?.LogError(exception: ex);
            throw;
        }
    }

    /// <summary>
    /// Creates a configured SQLite command object for executing database operations.
    /// </summary>
    /// <param name="query">The SQL query or command text to be executed</param>
    /// <param name="commandType">
    /// The <see cref="CommandType"/> that specifies how to interpret the query. Defaults to Text.
    /// </param>
    /// <returns>
    /// A configured <see cref="SqliteCommand"/> object ready for execution
    /// </returns>
    /// <remarks>
    /// Internal helper method that creates and configures a command object with proper validation and error handling.
    ///
    /// Example request:
    /// <code>
    /// {
    ///   "query": "SELECT * FROM Users WHERE Age > @age",
    ///   "commandType": "Text"
    /// }
    /// </code>
    ///
    /// <example>
    /// <code>
    /// using var command = CreateCommand(
    ///     query: "SELECT * FROM Users WHERE Age > @age",
    ///     commandType: CommandType.Text
    /// );
    /// command.Parameters.AddWithValue(parameterName: "@age", value: 18);
    /// using var reader = command.ExecuteReader();
    /// </code>
    /// </example>
    ///
    /// > [!IMPORTANT]
    /// > Always ensure connection is valid before creating command
    ///
    /// > [!TIP]
    /// > Use parameters to prevent SQL injection attacks
    ///
    /// > [!NOTE]
    /// > Command objects should be disposed after use
    /// </remarks>
    /// <exception cref="ArgumentException">
    /// Thrown when query parameter is null, empty, or consists only of white-space characters
    /// </exception>
    /// <exception cref="DataException">
    /// Thrown when database connection is invalid or not properly initialized
    /// </exception>
    /// <exception cref="ObjectDisposedException">
    /// Thrown when attempting to use a disposed instance of <see cref="T:SQLiteHelper"/>
    /// </exception>
    /// <seealso cref="SqliteCommand"/>
    /// <seealso cref="ConnectionIsValid"/>
    /// <seealso href="https://learn.microsoft.com/en-us/dotnet/standard/data/sqlite/parameters">
    /// SQLite Parameter Usage
    /// </seealso>
    /// <seealso href="https://learn.microsoft.com/en-us/dotnet/api/microsoft.data.sqlite.sqlitecommand">
    /// SqliteCommand Class
    /// </seealso>
    private SqliteCommand CreateCommand(string query, CommandType commandType = CommandType.Text)
    {
        try
        {
            ThrowIfDisposed();

            if (!ConnectionIsValid(message: out var errorMessage))
                throw new DataException(s: errorMessage);

            ArgumentException.ThrowIfNullOrWhiteSpace(query);

            var command = _connection.CreateCommand();
            command.CommandText = query;
            command.CommandType = commandType;

            return command;
        }
        catch (Exception ex)
        {
            _logging?.LogError(exception: ex);
            throw;
        }
    }

    /// <summary>
    /// Creates command object for executing queries with parameter values.
    /// </summary>
    /// <param name="query">
    /// SQL query text to execute against SQLite database. Must not be null or empty.
    /// </param>
    /// <param name="parameters">
    /// Dictionary of parameter names and their associated values to be used in query execution.
    /// </param>
    /// <param name="commandType">
    /// Type of command to execute. See <see cref="CommandType"/> for available options.
    /// Defaults to <see cref="CommandType.Text"/>.
    /// </param>
    /// <returns>
    /// Configured <see href="https://learn.microsoft.com/en-us/dotnet/api/microsoft.data.sqlite.sqlitecommand">SqliteCommand</see>
    /// instance for query execution.
    /// </returns>
    /// <remarks>
    /// Internal method for creating and configuring database command objects with parameter values.
    /// Validates connection state and query string before creating command.
    ///
    /// Features:
    /// - Connection validation
    /// - Query validation
    /// - Command type configuration
    /// - Parameter value assignment
    /// - Error logging
    /// - Resource management
    ///
    /// Example Request:
    /// <code>
    /// {
    ///   "query": "SELECT * FROM Users WHERE Department = @dept",
    ///   "parameters": {
    ///     "dept": "Engineering"
    ///   }
    /// }
    /// </code>
    ///
    /// Example Usage:
    /// <example>
    /// <code>
    /// using var command = CreateCommand(
    ///   query: "SELECT * FROM Users WHERE Active = @isActive",
    ///   parameters: new { isActive = true }
    /// );
    /// </code>
    /// </example>
    ///
    /// > [!IMPORTANT]
    /// > Command objects must be properly disposed after use
    /// </remarks>
    private SqliteCommand CreateCommand(string query, IDictionary<string, object> parameters, CommandType commandType = CommandType.Text)
    {
        try
        {
            ThrowIfDisposed();

            if (!ConnectionIsValid(message: out var errorMessage))
                throw new DataException(s: errorMessage);

            ArgumentException.ThrowIfNullOrWhiteSpace(query);

            var command = _connection.CreateCommand();
            command.CommandText = query;
            command.CommandType = commandType;

            foreach (var parameter in parameters)
            {
                command.Parameters.AddWithValue(parameterName: parameter.Key, value: parameter.Value);
            }

            return command;
        }
        catch (Exception ex)
        {
            _logging?.LogError(exception: ex);
            throw;
        }
    }

    /// <summary>
    /// Executes non-query SQL command and returns the number of affected rows.
    /// </summary>
    /// <remarks>
    /// Executes SQL commands that modify the database (INSERT, UPDATE, DELETE) and returns affected row count.
    /// Supports transaction management and proper error handling.
    ///
    /// Example request:
    /// <code>
    /// {
    ///   &quot;query&quot;: &quot;INSERT INTO Users (Name, Email, Age) VALUES (@name, @email, @age)&quot;,
    ///   &quot;parameters&quot;: {
    ///     &quot;name&quot;: &quot;John Doe&quot;,
    ///     &quot;email&quot;: &quot;<EMAIL>&quot;,
    ///     &quot;age&quot;: 30
    ///   }
    /// }
    /// </code>
    ///
    /// <example>
    /// <code>
    /// // Single operation
    /// var db = new SQLiteHelper(connectionString: &quot;Data Source=users.db&quot;);
    /// db.Connect()
    ///   .ExecuteNonQuery(
    ///     query: &quot;INSERT INTO Users (Name, Email) VALUES (@name, @email)&quot;,
    ///     affectedRows: out int insertedRows
    ///   )
    ///   .Disconnect();
    ///
    /// // Multiple operations in transaction
    /// db.Connect()
    ///   .TransactionBegin()
    ///   .ExecuteNonQuery(
    ///     query: &quot;UPDATE Users SET Status = @status WHERE Age &gt; @age&quot;,
    ///     affectedRows: out int updatedRows
    ///   )
    ///   .ExecuteNonQuery(
    ///     query: &quot;DELETE FROM Logs WHERE Timestamp &lt; @oldDate&quot;,
    ///     affectedRows: out int deletedRows
    ///   )
    ///   .TransactionCommit()
    ///   .Disconnect();
    /// </code>
    /// </example>
    ///
    /// &gt; [!IMPORTANT]
    /// &gt; Always use parameterized queries to prevent SQL injection attacks
    ///
    /// &gt; [!NOTE]
    /// &gt; For batch operations, consider wrapping multiple calls in a transaction
    ///
    /// &gt; [!TIP]
    /// &gt; Use the returned instance for method chaining with other operations
    ///
    /// &gt; [!CAUTION]
    /// &gt; Ensure proper error handling and transaction management for data consistency
    ///
    /// &gt; [!WARNING]
    /// &gt; Large batch operations may impact database performance
    /// </remarks>
    /// <param name="query">
    /// The SQL command text to execute (INSERT, UPDATE, DELETE, etc.)
    /// </param>
    /// <param name="affectedRows">
    /// Output parameter that returns the number of rows affected by the command execution
    /// </param>
    /// <returns>
    /// Current instance of <see cref="T:IDC.Utilities.Data.SQLiteHelper"/> for method chaining
    /// </returns>
    /// <exception cref="T:ArgumentException">
    /// Thrown when query is null, empty, or whitespace
    /// </exception>
    /// <exception cref="T:SqliteException">
    /// Thrown when SQL execution fails due to syntax errors or constraints
    /// </exception>
    /// <exception cref="T:ObjectDisposedException">
    /// Thrown when accessing a disposed instance
    /// </exception>
    /// <exception cref="T:InvalidOperationException">
    /// Thrown when database connection is not open
    /// </exception>
    /// <seealso cref="M:IDC.Utilities.Data.SQLiteHelper.ExecuteScalar"/>
    /// <seealso cref="M:IDC.Utilities.Data.SQLiteHelper.ExecuteReader"/>
    /// <seealso cref="M:IDC.Utilities.Data.SQLiteHelper.TransactionBegin"/>
    /// <seealso href="https://learn.microsoft.com/en-us/dotnet/standard/data/sqlite/bulk-insert">
    /// SQLite Bulk Insert Operations
    /// </seealso>
    /// <seealso href="https://learn.microsoft.com/en-us/dotnet/standard/data/sqlite/transactions">
    /// SQLite Transaction Management
    /// </seealso>
    /// <seealso href="https://www.sqlite.org/lang_transaction.html">
    /// SQLite Transaction Documentation
    /// </seealso>
    public SQLiteHelper ExecuteNonQuery(string query, out int affectedRows)
    {
        try
        {
            ThrowIfDisposed();
            ArgumentException.ThrowIfNullOrWhiteSpace(query);

            using var command = CreateCommand(query: query);
            affectedRows = command.ExecuteNonQuery();

            return this;
        }
        catch (Exception ex)
        {
            _logging?.LogError(exception: ex);
            throw;
        }
    }

    /// <summary>
    /// Executes SQL query with parameterized input and returns number of affected rows.
    /// </summary>
    /// <remarks>
    /// Supports transaction management and proper error handling.
    ///
    /// Example request:
    /// <code>
    /// {
    ///   &quot;query&quot;: &quot;INSERT INTO Users (Name, Email) VALUES (@name, @email)&quot;,
    ///   &quot;parameters&quot;: {
    ///     &quot;name&quot;: &quot;John Doe&quot;,
    ///     &quot;email&quot;: &quot;<EMAIL>&quot;
    ///   }
    /// }
    /// </code>
    ///
    /// <example>
    /// <code>
    /// // Single operation
    /// var db = new SQLiteHelper(connectionString: &quot;Data Source=users.db&quot;);
    /// db.Connect()
    ///   .ExecuteNonQuery(
    ///     query: &quot;INSERT INTO Users (Name, Email) VALUES (@name, @email)&quot;,
    ///     parameters: new { name = &quot;John Doe&quot;, email = &quot;<EMAIL>&quot; },
    ///     affectedRows: out int insertedRows
    ///   )
    ///   .Disconnect();
    ///
    /// // Multiple operations in transaction
    /// db.Connect()
    ///   .TransactionBegin()
    ///   .ExecuteNonQuery(
    ///     query: &quot;UPDATE Users SET Status = @status WHERE Age &gt; @age&quot;,
    ///     parameters: new { status = &quot;Active&quot;, age = 18 },
    ///     affectedRows: out int updatedRows
    ///   )
    ///   .ExecuteNonQuery(
    ///     query: &quot;DELETE FROM Logs WHERE Timestamp &lt; @oldDate&quot;,
    ///     parameters: new { oldDate = DateTime.Now.AddDays(-30) },
    ///     affectedRows: out int deletedRows
    ///   )
    ///   .TransactionCommit()
    ///   .Disconnect();
    /// </code>
    /// </example>
    ///
    /// &gt; [!IMPORTANT]
    /// &gt; Always use parameterized queries to prevent SQL injection attacks
    ///
    /// &gt; [!NOTE]
    /// &gt; For batch operations, consider wrapping multiple calls in a transaction
    ///
    /// &gt; [!TIP]
    /// &gt; Use the returned instance for method chaining with other operations
    ///
    /// &gt; [!CAUTION]
    /// &gt; Ensure proper error handling and transaction management for data consistency
    ///
    /// &gt; [!WARNING]
    /// &gt; Large batch operations may impact database performance
    /// </remarks>
    /// <param name="query">
    /// The SQL command text to execute (INSERT, UPDATE, DELETE, etc.)
    /// </param>
    /// <param name="parameters">
    /// Parameters to be inserted into the SQL query
    /// </param>
    /// <param name="affectedRows">
    /// Output parameter that returns the number of rows affected by the command execution
    /// </param>
    /// <returns>
    /// Current instance of <see cref="T:IDC.Utilities.Data.SQLiteHelper"/> for method chaining
    /// </returns>
    /// <seealso cref="M:IDC.Utilities.Data.SQLiteHelper.ExecuteScalar"/>
    /// <seealso cref="M:IDC.Utilities.Data.SQLiteHelper.ExecuteReader"/>
    /// <seealso cref="M:IDC.Utilities.Data.SQLiteHelper.TransactionBegin"/>
    public SQLiteHelper ExecuteNonQuery(
        string query,
        IDictionary<string, object> parameters,
        out int affectedRows
    )
    {
        try
        {
            ThrowIfDisposed();
            ArgumentException.ThrowIfNullOrWhiteSpace(query);

            using var command = CreateCommand(query: query, parameters: parameters);
            affectedRows = command.ExecuteNonQuery();

            return this;
        }
        catch (Exception ex)
        {
            _logging?.LogError(exception: ex);
            throw;
        }
    }

    /// <summary>
    /// Executes SQL query returning scalar value from the first row and column of the result set.
    /// </summary>
    /// <param name="query">The SQL query text to execute (typically aggregate functions)</param>
    /// <param name="result">
    /// Output parameter containing the first column of first row in the result set, or null if no rows
    /// </param>
    /// <returns>
    /// Current instance of <see cref="T:SQLiteHelper"/> for method chaining
    /// </returns>
    /// <remarks>
    /// Executes queries that return a single value, typically used with aggregate functions (COUNT, SUM, AVG, etc.).
    /// Returns the first column of the first row in the result set.
    ///
    /// Example request:
    /// <code>
    /// {
    ///   "query": "SELECT COUNT(*) FROM Users WHERE Age > @minAge",
    ///   "parameters": {
    ///     "minAge": 18
    ///   }
    /// }
    /// </code>
    ///
    /// <example>
    /// <code>
    /// var db = new SQLiteHelper(connectionString: "Data Source=analytics.db");
    ///
    /// // Get total user count
    /// db.Connect()
    ///   .ExecuteScalar(
    ///     query: "SELECT COUNT(*) FROM Users",
    ///     result: out object? totalUsers
    ///   )
    ///   .Disconnect();
    ///
    /// // Calculate average age
    /// db.Connect()
    ///   .ExecuteScalar(
    ///     query: "SELECT AVG(Age) FROM Users WHERE Status = @status",
    ///     result: out object? avgAge
    ///   )
    ///   .Disconnect();
    /// </code>
    /// </example>
    ///
    /// > [!IMPORTANT]
    /// > Result is returned as object type - cast to appropriate type before use
    ///
    /// > [!NOTE]
    /// > Returns null if query returns no rows
    ///
    /// > [!TIP]
    /// > Ideal for aggregate functions and existence checks
    /// </remarks>
    /// <exception cref="ArgumentException">
    /// Thrown when query is null, empty, or whitespace
    /// </exception>
    /// <exception cref="SqliteException">
    /// Thrown when SQL execution fails due to syntax errors or constraints
    /// </exception>
    /// <exception cref="InvalidCastException">
    /// Thrown when result cannot be converted to expected type
    /// </exception>
    /// <exception cref="ObjectDisposedException">
    /// Thrown when accessing a disposed instance
    /// </exception>
    /// <seealso cref="ExecuteNonQuery"/>
    /// <seealso cref="ExecuteReader"/>
    /// <seealso href="https://learn.microsoft.com/en-us/dotnet/standard/data/sqlite/aggregate-functions">
    /// SQLite Aggregate Functions
    /// </seealso>
    /// <seealso href="https://www.sqlite.org/lang_aggfunc.html">
    /// SQLite Aggregate Functions Reference
    /// </seealso>
    public SQLiteHelper ExecuteScalar(string query, out object? result)
    {
        try
        {
            ThrowIfDisposed();
            ArgumentException.ThrowIfNullOrWhiteSpace(query);

            using var command = CreateCommand(query: query);
            result = command.ExecuteScalar();

            return this;
        }
        catch (Exception ex)
        {
            _logging?.LogError(exception: ex);
            throw;
        }
    }

    /// <summary>
    /// Executes SQL query returning data reader for row-by-row result processing.
    /// </summary>
    /// <param name="query">The SQL query text to execute</param>
    /// <param name="reader">Output parameter containing the <see cref="SqliteDataReader"/> for result processing</param>
    /// <returns>Current instance of <see cref="T:SQLiteHelper"/> for method chaining</returns>
    /// <remarks>
    /// Provides forward-only, read-only access to query results using a <see cref="SqliteDataReader"/>.
    /// Optimized for memory efficiency when processing large result sets.
    ///
    /// Example request:
    /// <code>
    /// {
    ///   "query": "SELECT Id, Name, Email FROM Users WHERE Department = @dept",
    ///   "parameters": {
    ///     "dept": "Engineering"
    ///   }
    /// }
    /// </code>
    ///
    /// <example>
    /// <code>
    /// var db = new SQLiteHelper(connectionString: "Data Source=users.db");
    ///
    /// // Basic row iteration
    /// db.Connect()
    ///   .ExecuteReader(
    ///     query: "SELECT Name, Email FROM Users",
    ///     reader: out var dataReader
    ///   );
    ///
    /// while (dataReader.Read())
    /// {
    ///     var name = dataReader.GetString(columnName: "Name");
    ///     var email = dataReader.GetString(columnName: "Email");
    ///     Console.WriteLine($"{name}: {email}");
    /// }
    ///
    /// // Using with type checking
    /// db.Connect()
    ///   .ExecuteReader(
    ///     query: "SELECT Id, Age, IsActive FROM Users",
    ///     reader: out var typedReader
    ///   );
    ///
    /// while (typedReader.Read())
    /// {
    ///     var id = typedReader.GetInt32(columnName: "Id");
    ///     var age = typedReader.IsDBNull(columnName: "Age")
    ///         ? null
    ///         : typedReader.GetInt32(columnName: "Age");
    ///     var isActive = typedReader.GetBoolean(columnName: "IsActive");
    /// }
    /// </code>
    /// </example>
    ///
    /// > [!IMPORTANT]
    /// > Reader must be closed after use to release database resources
    ///
    /// > [!NOTE]
    /// > Connection must remain open while reader is in use
    ///
    /// > [!TIP]
    /// > Use GetOrdinal() to get column index by name for better performance
    ///
    /// > [!CAUTION]
    /// > Always check for DBNull before accessing nullable columns
    /// </remarks>
    /// <exception cref="ArgumentException">
    /// Thrown when query is null, empty, or whitespace
    /// </exception>
    /// <exception cref="SqliteException">
    /// Thrown when SQL execution fails due to syntax errors or constraints
    /// </exception>
    /// <exception cref="InvalidOperationException">
    /// Thrown when database connection is not open
    /// </exception>
    /// <exception cref="ObjectDisposedException">
    /// Thrown when accessing a disposed instance
    /// </exception>
    /// <seealso cref="SqliteDataReader"/>
    /// <seealso cref="ExecuteScalar"/>
    /// <seealso cref="ExecuteQuery"/>
    /// <seealso href="https://learn.microsoft.com/en-us/dotnet/standard/data/sqlite/types">
    /// SQLite Data Types
    /// </seealso>
    /// <seealso href="https://learn.microsoft.com/en-us/dotnet/api/microsoft.data.sqlite.sqlitedatareader">
    /// SqliteDataReader Class
    /// </seealso>
    public SQLiteHelper ExecuteReader(string query, out SqliteDataReader reader)
    {
        try
        {
            ThrowIfDisposed();
            ArgumentException.ThrowIfNullOrWhiteSpace(query);

            using var command = CreateCommand(query: query);
            reader = command.ExecuteReader();

            return this;
        }
        catch (Exception ex)
        {
            _logging?.LogError(exception: ex);
            throw;
        }
    }

    /// <summary>
    /// Executes SQL query and returns results as a list of JSON objects.
    /// </summary>
    /// <param name="query">The SQL query text to execute</param>
    /// <param name="results">Output parameter containing the query results as a <see cref="List{JObject}"/></param>
    /// <returns>Current instance of <see cref="T:SQLiteHelper"/> for method chaining</returns>
    /// <remarks>
    /// Provides JSON-formatted access to query results using <see cref="JObject"/>.
    /// Optimized for JSON data representation and serialization.
    ///
    /// Example request:
    /// <code>
    /// {
    ///   "query": "SELECT Id, Name, Email FROM Users WHERE Department = @dept",
    ///   "parameters": {
    ///     "dept": "Engineering"
    ///   }
    /// }
    /// </code>
    ///
    /// <example>
    /// <code>
    /// var db = new SQLiteHelper(connectionString: "Data Source=users.db");
    ///
    /// // Basic JSON access
    /// db.Connect()
    ///   .ExecuteQuery(
    ///     query: "SELECT Name, Email FROM Users",
    ///     results: out List&lt;JObject&gt; usersJson
    ///   );
    ///
    /// foreach (var user in usersJson)
    /// {
    ///     var name = user["Name"]?.ToString();
    ///     var email = user["Email"]?.ToString();
    ///     Console.WriteLine($"{name}: {email}");
    /// }
    ///
    /// // Using with type conversion
    /// db.Connect()
    ///   .ExecuteQuery(
    ///     query: "SELECT Id, Age, IsActive FROM Users",
    ///     results: out List&lt;JObject&gt; typedJson
    ///   );
    ///
    /// foreach (var user in typedJson)
    /// {
    ///     var id = user["Id"]?.Value&lt;int&gt;();
    ///     var age = user["Age"]?.Value&lt;int?&gt;();
    ///     var isActive = user["IsActive"]?.Value&lt;bool&gt;();
    /// }
    /// </code>
    /// </example>
    ///
    /// > [!IMPORTANT]
    /// > Each row is converted to a JObject with column names as properties
    ///
    /// > [!NOTE]
    /// > Null values are preserved in the JSON structure
    ///
    /// > [!TIP]
    /// > Use strong typing with Value&lt;T&gt;() for type-safe access
    ///
    /// > [!CAUTION]
    /// > Always check for null values before accessing properties
    /// </remarks>
    /// <exception cref="ArgumentException">
    /// Thrown when query is null, empty, or whitespace
    /// </exception>
    /// <exception cref="SqliteException">
    /// Thrown when SQL execution fails due to syntax errors or constraints
    /// </exception>
    /// <exception cref="InvalidOperationException">
    /// Thrown when database connection is not open
    /// </exception>
    /// <exception cref="ObjectDisposedException">
    /// Thrown when accessing a disposed instance
    /// </exception>
    /// <seealso cref="JObject"/>
    /// <seealso cref="ExecuteReader"/>
    /// <seealso cref="ExecuteScalar"/>
    /// <seealso href="https://www.newtonsoft.com/json/help/html/T_Newtonsoft_Json_Linq_JObject.htm">
    /// JObject Class
    /// </seealso>
    /// <seealso href="https://www.newtonsoft.com/json/help/html/QueryingLINQtoJSON.htm">
    /// Querying JSON with LINQ
    /// </seealso>
    public SQLiteHelper ExecuteQuery(string query, out List<JObject> results)
    {
        try
        {
            ThrowIfDisposed();
            ArgumentException.ThrowIfNullOrWhiteSpace(query);

            results = [];
            using var command = CreateCommand(query: query);
            using var reader = command.ExecuteReader();

            while (reader.Read())
            {
                var row = new JObject();
                for (var i = 0; i < reader.FieldCount; i++)
                {
                    var name = reader.GetName(i);
                    var value = reader.IsDBNull(i) ? null : reader.GetValue(i);
                    row[name] = value == null ? null : JToken.FromObject(value);
                }
                results.Add(row);
            }

            return this;
        }
        catch (Exception ex)
        {
            _logging?.LogError(exception: ex);
            throw;
        }
    }

    /// <summary>
    /// Executes SQL query and returns results as a DataTable object.
    /// </summary>
    /// <param name="query">The SQL query text to execute</param>
    /// <param name="results">Output parameter containing the query results as a <see cref="DataTable"/></param>
    /// <returns>Current instance of <see cref="T:SQLiteHelper"/> for method chaining</returns>
    /// <remarks>
    /// Provides table-formatted access to query results using a <see cref="DataTable"/>.
    /// Optimized for structured data representation and manipulation.
    ///
    /// Example request:
    /// <code>
    /// {
    ///   "query": "SELECT Id, Name, Email FROM Users WHERE Department = @dept",
    ///   "parameters": {
    ///     "dept": "Engineering"
    ///   }
    /// }
    /// </code>
    ///
    /// <example>
    /// <code>
    /// var db = new SQLiteHelper(connectionString: "Data Source=users.db");
    ///
    /// // Basic table access
    /// db.Connect()
    ///   .ExecuteQuery(
    ///     query: "SELECT Name, Email FROM Users",
    ///     results: out DataTable usersTable
    ///   );
    ///
    /// foreach (DataRow row in usersTable.Rows)
    /// {
    ///     var name = row["Name"].ToString();
    ///     var email = row["Email"].ToString();
    ///     Console.WriteLine($"{name}: {email}");
    /// }
    ///
    /// // Using with type conversion
    /// db.Connect()
    ///   .ExecuteQuery(
    ///     query: "SELECT Id, Age, IsActive FROM Users",
    ///     results: out DataTable typedTable
    ///   );
    ///
    /// foreach (DataRow row in typedTable.Rows)
    /// {
    ///     var id = Convert.ToInt32(row["Id"]);
    ///     var age = row["Age"] == DBNull.Value
    ///         ? null
    ///         : Convert.ToInt32(row["Age"]);
    ///     var isActive = Convert.ToBoolean(row["IsActive"]);
    /// }
    /// </code>
    /// </example>
    ///
    /// > [!IMPORTANT]
    /// > Dispose DataTable after use to release memory resources
    ///
    /// > [!NOTE]
    /// > DataTable loads entire result set into memory
    ///
    /// > [!TIP]
    /// > Use column names for better code readability
    ///
    /// > [!CAUTION]
    /// > Check for DBNull before accessing nullable columns
    /// </remarks>
    /// <exception cref="ArgumentException">
    /// Thrown when query is null, empty, or whitespace
    /// </exception>
    /// <exception cref="SqliteException">
    /// Thrown when SQL execution fails due to syntax errors or constraints
    /// </exception>
    /// <exception cref="InvalidOperationException">
    /// Thrown when database connection is not open
    /// </exception>
    /// <exception cref="ObjectDisposedException">
    /// Thrown when accessing a disposed instance
    /// </exception>
    /// <seealso cref="DataTable"/>
    /// <seealso cref="ExecuteReader"/>
    /// <seealso cref="ExecuteScalar"/>
    /// <seealso href="https://learn.microsoft.com/en-us/dotnet/api/system.data.datatable">
    /// DataTable Class
    /// </seealso>
    /// <seealso href="https://learn.microsoft.com/en-us/dotnet/framework/data/adonet/dataset-datatable-dataview">
    /// DataTable Architecture
    /// </seealso>
    public SQLiteHelper ExecuteQuery(string query, out DataTable results)
    {
        try
        {
            ThrowIfDisposed();
            ArgumentException.ThrowIfNullOrWhiteSpace(query);

            results = new();
            using var command = CreateCommand(query: query);
            using var reader = command.ExecuteReader();
            results.Load(reader);

            return this;
        }
        catch (Exception ex)
        {
            _logging?.LogError(exception: ex);
            throw;
        }
    }
}

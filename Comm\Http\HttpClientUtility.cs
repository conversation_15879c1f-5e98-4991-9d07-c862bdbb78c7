using System.Net;
using System.Text;
using Newtonsoft.Json;

namespace IDC.Utilities.Comm.Http;

/// <summary>
/// Provides a reusable HTTP client for making HTTP requests with connection pooling.
/// </summary>
/// <remarks>
/// This utility class implements a singleton pattern to maintain a single HttpClient instance
/// for efficient connection reuse. It supports all common HTTP methods and provides
/// serialization/deserialization of request and response data.
///
/// Features:
/// - Connection pooling through static HttpClient
/// - Support for all common HTTP methods (GET, POST, PUT, DELETE)
/// - Automatic serialization/deserialization of JSON data
/// - Configurable timeouts and headers
/// - Proper error handling with detailed exceptions
/// - Resource-efficient implementation
///
/// Example usage:
/// <code>
/// // Basic GET request
/// var client = HttpClientUtility.Instance;
/// var response = await client.GetAsync&lt;UserModel&gt;("https://api.example.com/users/1");
///
/// // POST request with data
/// var newUser = new UserModel { Name = "John <PERSON>", Email = "<EMAIL>" };
/// var createdUser = await client.PostAsync&lt;UserModel, UserModel&gt;(
///     "https://api.example.com/users",
///     newUser
/// );
/// </code>
///
/// > [!NOTE]
/// > This implementation follows best practices for HttpClient usage by maintaining
/// > a single instance to avoid socket exhaustion.
///
/// > [!IMPORTANT]
/// > Default timeout is 100 seconds but can be configured per request.
/// </remarks>
public partial class HttpClientUtility : IDisposable
{
    /// <summary>
    /// Sends a GET request to the specified URI and returns the deserialized response.
    /// </summary>
    /// <typeparam name="TResponse">
    /// The type to deserialize the response to. See
    /// <see href="https://learn.microsoft.com/en-us/dotnet/api/system.type"/> for more
    /// details.
    /// </typeparam>
    /// <param name="uri">The URI to send the request to. Must be a valid HTTP/HTTPS URL.</param>
    /// <param name="headers">Optional dictionary of request headers. See <see href="https://learn.microsoft.com/en-us/dotnet/api/system.net.http.headers.httpheaders"/> for details.</param>
    /// <param name="timeoutSeconds">Optional timeout in seconds. Overrides the default 100-second timeout if specified.</param>
    /// <param name="ensureStatusCodeSuccess">When true, throws an exception for non-success status codes.</param>
    /// <param name="cancellationToken">Optional token to cancel the request. See <see href="https://learn.microsoft.com/en-us/dotnet/api/system.threading.cancellationtoken"/> for details.</param>
    /// <returns>The deserialized response object of type <typeparamref name="TResponse"/> or null if the response is empty.</returns>
    /// <exception cref="HttpRequestException">Thrown when the request fails or returns a non-success status code (if ensureStatusCodeSuccess is true).</exception>
    /// <exception cref="TaskCanceledException">Thrown when the request is canceled via cancellationToken.</exception>
    /// <exception cref="TimeoutException">Thrown when the request exceeds the specified timeout.</exception>
    /// <exception cref="JsonException">Thrown when the response cannot be deserialized to type <typeparamref name="TResponse"/>.</exception>
    /// <remarks>
    /// This method sends a GET request and automatically deserializes the JSON response to the specified type.
    /// It supports connection pooling, timeout configuration, and custom headers.
    ///
    /// > [!NOTE]
    /// > The method will return null if the response content is empty or null.
    ///
    /// > [!IMPORTANT]
    /// > Always dispose of the returned object if it implements IDisposable.
    ///
    /// Example usage:
    /// <code>
    /// // Basic GET request
    /// var user = await HttpClientUtility.Instance.GetAsync&lt;UserModel&gt;(
    ///     uri: "https://api.example.com/users/123"
    /// );
    ///
    /// // GET request with headers and timeout
    /// var users = await HttpClientUtility.Instance.GetAsync&lt;List&lt;UserModel&gt;&gt;(
    ///     uri: "https://api.example.com/users",
    ///     headers: new Dictionary&lt;string, string&gt;
    ///     {
    ///         { "Authorization", "Bearer token123" },
    ///         { "Custom-Header", "Value" }
    ///     },
    ///     timeoutSeconds: 30
    /// );
    /// </code>
    /// </remarks>
    public async Task<TResponse?> GetAsync<TResponse>(
        string uri,
        Dictionary<string, string>? headers = null,
        int? timeoutSeconds = null,
        bool ensureStatusCodeSuccess = true,
        CancellationToken cancellationToken = default
    )
    {
        var result = default(TResponse?);
        try
        {
            using var request = new HttpRequestMessage(method: HttpMethod.Get, requestUri: uri);
            result = await SendRequestAsync<TResponse>(
                request: request,
                headers: headers,
                timeoutSeconds: timeoutSeconds,
                EnsureStatusCodeSuccess: ensureStatusCodeSuccess,
                cancellationToken: cancellationToken
            );
        }
        catch (Exception ex)
        {
            _systemLogging?.LogError(exception: ex);
        }

        return result;
    }

    /// <summary>
    /// Sends a POST request with the specified content to the URI and returns the deserialized response.
    /// </summary>
    /// <typeparam name="TRequest">The type of the request content.</typeparam>
    /// <typeparam name="TResponse">The type to deserialize the response to.</typeparam>
    /// <param name="uri">The URI to send the request to.</param>
    /// <param name="content">The content to send in the request body.</param>
    /// <param name="headers">Optional headers to include in the request.</param>
    /// <param name="timeoutSeconds">Optional timeout in seconds (overrides default timeout).</param>
    /// <param name="ensureStatusCodeSuccess">Indicates whether to ensure the HTTP response has a successful status code.</param>
    /// <param name="cancellationToken">Optional cancellation token.</param>
    /// <returns>The deserialized response object.</returns>
    /// <exception cref="HttpRequestException">Thrown when the request fails.</exception>
    /// <exception cref="TaskCanceledException">Thrown when the request times out or is canceled.</exception>
    /// <exception cref="JsonException">Thrown when the response cannot be deserialized.</exception>
    /// <remarks>
    /// Sends a POST request with the specified content to the URI and deserializes the response.
    ///
    /// Example usage:
    /// <code>
    /// // Create a new user
    /// var newUser = new UserModel { Name = "John Doe", Email = "<EMAIL>" };
    /// var createdUser = await HttpClientUtility.Instance.PostAsync&lt;UserModel, UserModel&gt;(
    ///     "https://api.example.com/users",
    ///     newUser
    /// );
    /// </code>
    /// </remarks>
    public async Task<TResponse?> PostAsync<TRequest, TResponse>(
        string uri,
        TRequest content,
        Dictionary<string, string>? headers = null,
        int? timeoutSeconds = null,
        bool ensureStatusCodeSuccess = true,
        CancellationToken cancellationToken = default
    )
    {
        var result = default(TResponse?);
        try
        {
            using var request = new HttpRequestMessage(method: HttpMethod.Post, requestUri: uri)
            {
                Content = CreateJsonContent(content: content),
            };

            result = await SendRequestAsync<TResponse>(
                request: request,
                headers: headers,
                timeoutSeconds: timeoutSeconds,
                EnsureStatusCodeSuccess: ensureStatusCodeSuccess,
                cancellationToken: cancellationToken
            );
        }
        catch (Exception ex)
        {
            _systemLogging?.LogError(exception: ex);
        }

        return result;
    }

    /// <summary>
    /// Sends a POST request with the specified content to the URI without expecting a typed response.
    /// </summary>
    /// <typeparam name="TRequest">The type of the request content.</typeparam>
    /// <param name="uri">The URI to send the request to.</param>
    /// <param name="content">The content to send in the request body.</param>
    /// <param name="headers">Optional headers to include in the request.</param>
    /// <param name="timeoutSeconds">Optional timeout in seconds (overrides default timeout).</param>
    /// <param name="cancellationToken">Optional cancellation token.</param>
    /// <returns>The raw HTTP response message.</returns>
    /// <exception cref="HttpRequestException">Thrown when the request fails.</exception>
    /// <exception cref="TaskCanceledException">Thrown when the request times out or is canceled.</exception>
    /// <remarks>
    /// Sends a POST request with the specified content to the URI and returns the raw HTTP response.
    /// Useful when you don't need to deserialize the response or when you want to handle the response manually.
    ///
    /// Example usage:
    /// <code>
    /// // Send a notification without expecting a specific response type
    /// var notification = new NotificationModel { UserId = 123, Message = "Hello!" };
    /// var response = await HttpClientUtility.Instance.PostAsync(
    ///     "https://api.example.com/notifications",
    ///     notification
    /// );
    ///
    /// if (response.IsSuccessStatusCode)
    /// {
    ///     // Handle success
    /// }
    /// </code>
    /// </remarks>
    public async Task<HttpResponseMessage?> PostAsync<TRequest>(
        string uri,
        TRequest content,
        Dictionary<string, string>? headers = null,
        int? timeoutSeconds = null,
        CancellationToken cancellationToken = default
    )
    {
        HttpResponseMessage? result = null;
        try
        {
            using var request = new HttpRequestMessage(method: HttpMethod.Post, requestUri: uri)
            {
                Content = CreateJsonContent(content: content),
            };

            result = await SendRequestWithoutDeserializationAsync(
                request: request,
                headers: headers,
                timeoutSeconds: timeoutSeconds,
                cancellationToken: cancellationToken
            );
        }
        catch (Exception ex)
        {
            _systemLogging?.LogError(exception: ex);
        }

        return result;
    }

    /// <summary>
    /// Sends a PUT request with the specified content to the URI and returns the deserialized response.
    /// </summary>
    /// <typeparam name="TRequest">The type of the request content.</typeparam>
    /// <typeparam name="TResponse">The type to deserialize the response to.</typeparam>
    /// <param name="uri">The URI to send the request to.</param>
    /// <param name="content">The content to send in the request body.</param>
    /// <param name="headers">Optional headers to include in the request.</param>
    /// <param name="timeoutSeconds">Optional timeout in seconds (overrides default timeout).</param>
    /// <param name="ensureStatusCodeSuccess">Indicates whether to ensure the HTTP response has a successful status code.</param>
    /// <param name="cancellationToken">Optional cancellation token.</param>
    /// <returns>The deserialized response object.</returns>
    /// <exception cref="HttpRequestException">Thrown when the request fails.</exception>
    /// <exception cref="TaskCanceledException">Thrown when the request times out or is canceled.</exception>
    /// <exception cref="JsonException">Thrown when the response cannot be deserialized.</exception>
    /// <remarks>
    /// Sends a PUT request with the specified content to the URI and deserializes the response.
    ///
    /// Example usage:
    /// <code>
    /// // Update a user
    /// var updatedUser = new UserModel { Id = 123, Name = "John Updated", Email = "<EMAIL>" };
    /// var result = await HttpClientUtility.Instance.PutAsync&lt;UserModel, UserModel&gt;(
    ///     "https://api.example.com/users/123",
    ///     updatedUser
    /// );
    /// </code>
    /// </remarks>
    public async Task<TResponse?> PutAsync<TRequest, TResponse>(
        string uri,
        TRequest content,
        Dictionary<string, string>? headers = null,
        int? timeoutSeconds = null,
        bool ensureStatusCodeSuccess = true,
        CancellationToken cancellationToken = default
    )
    {
        var result = default(TResponse?);
        try
        {
            using var request = new HttpRequestMessage(method: HttpMethod.Put, requestUri: uri)
            {
                Content = CreateJsonContent(content: content),
            };

            result = await SendRequestAsync<TResponse>(
                request: request,
                headers: headers,
                timeoutSeconds: timeoutSeconds,
                EnsureStatusCodeSuccess: ensureStatusCodeSuccess,
                cancellationToken: cancellationToken
            );
        }
        catch (Exception ex)
        {
            _systemLogging?.LogError(exception: ex);
        }

        return result;
    }

    /// <summary>
    /// Sends a DELETE request to the specified URI and returns the deserialized response.
    /// </summary>
    /// <typeparam name="TResponse">The type to deserialize the response to.</typeparam>
    /// <param name="uri">The URI to send the request to.</param>
    /// <param name="headers">Optional headers to include in the request.</param>
    /// <param name="timeoutSeconds">Optional timeout in seconds (overrides default timeout).</param>
    /// <param name="ensureStatusCodeSuccess">Indicates whether to ensure the HTTP response has a successful status code.</param>
    /// <param name="cancellationToken">Optional cancellation token.</param>
    /// <returns>The deserialized response object.</returns>
    /// <exception cref="HttpRequestException">Thrown when the request fails.</exception>
    /// <exception cref="TaskCanceledException">Thrown when the request times out or is canceled.</exception>
    /// <exception cref="JsonException">Thrown when the response cannot be deserialized.</exception>
    /// <remarks>
    /// Sends a DELETE request to the specified URI and deserializes the response.
    ///
    /// Example usage:
    /// <code>
    /// // Delete a user and get deletion status
    /// var result = await HttpClientUtility.Instance.DeleteAsync&lt;DeleteResult&gt;(
    ///     "https://api.example.com/users/123"
    /// );
    /// </code>
    /// </remarks>
    public async Task<TResponse?> DeleteAsync<TResponse>(
        string uri,
        Dictionary<string, string>? headers = null,
        int? timeoutSeconds = null,
        bool ensureStatusCodeSuccess = true,
        CancellationToken cancellationToken = default
    )
    {
        var result = default(TResponse?);
        try
        {
            using var request = new HttpRequestMessage(method: HttpMethod.Delete, requestUri: uri);
            result = await SendRequestAsync<TResponse>(
                request: request,
                headers: headers,
                timeoutSeconds: timeoutSeconds,
                EnsureStatusCodeSuccess: ensureStatusCodeSuccess,
                cancellationToken: cancellationToken
            );
        }
        catch (Exception ex)
        {
            _systemLogging?.LogError(exception: ex);
        }

        return result;
    }

    /// <summary>
    /// Sends a DELETE request to the specified URI without expecting a typed response.
    /// </summary>
    /// <param name="uri">The URI to send the request to.</param>
    /// <param name="headers">Optional headers to include in the request.</param>
    /// <param name="timeoutSeconds">Optional timeout in seconds (overrides default timeout).</param>
    /// <param name="cancellationToken">Optional cancellation token.</param>
    /// <returns>The raw HTTP response message.</returns>
    /// <exception cref="HttpRequestException">Thrown when the request fails.</exception>
    /// <exception cref="TaskCanceledException">Thrown when the request times out or is canceled.</exception>
    /// <remarks>
    /// Sends a DELETE request to the specified URI and returns the raw HTTP response.
    /// Useful when you don't need to deserialize the response or when you want to handle the response manually.
    ///
    /// Example usage:
    /// <code>
    /// // Delete a resource without expecting a specific response type
    /// var response = await HttpClientUtility.Instance.DeleteAsync(
    ///     "https://api.example.com/resources/123"
    /// );
    ///
    /// if (response.IsSuccessStatusCode)
    /// {
    ///     // Resource deleted successfully
    /// }
    /// </code>
    /// </remarks>
    public async Task<HttpResponseMessage> DeleteAsync(
        string uri,
        Dictionary<string, string>? headers = null,
        int? timeoutSeconds = null,
        CancellationToken cancellationToken = default
    )
    {
        HttpResponseMessage? result = null;
        try
        {
            using var request = new HttpRequestMessage(method: HttpMethod.Delete, requestUri: uri);
            result = await SendRequestWithoutDeserializationAsync(
                request: request,
                headers: headers,
                timeoutSeconds: timeoutSeconds,
                cancellationToken: cancellationToken
            );
        }
        catch (Exception ex)
        {
            _systemLogging?.LogError(exception: ex);
        }

        return result ?? new HttpResponseMessage(statusCode: HttpStatusCode.InternalServerError);
    }

    /// <summary>
    /// Creates a StringContent object with JSON content.
    /// </summary>
    /// <typeparam name="T">The type of the content.</typeparam>
    /// <param name="content">The content to serialize.</param>
    /// <returns>A StringContent object with JSON content.</returns>
    private static StringContent CreateJsonContent<T>(T content) =>
        new(
            content: JsonConvert.SerializeObject(value: content),
            encoding: Encoding.UTF8,
            mediaType: "application/json"
        );

    /// <summary>
    /// Sends an HTTP request and deserializes the response.
    /// </summary>
    /// <typeparam name="TResponse">The type to deserialize the response to.</typeparam>
    /// <param name="request">The HTTP request message.</param>
    /// <param name="headers">Optional headers to include in the request.</param>
    /// <param name="timeoutSeconds">Optional timeout in seconds.</param>
    /// <param name="EnsureStatusCodeSuccess">Indicates whether to ensure the HTTP response status code is a success.</param>
    /// <param name="cancellationToken">Optional cancellation token.</param>
    /// <returns>The deserialized response object.</returns>
    private async Task<TResponse?> SendRequestAsync<TResponse>(
        HttpRequestMessage request,
        Dictionary<string, string>? headers = null,
        int? timeoutSeconds = null,
        bool EnsureStatusCodeSuccess = true,
        CancellationToken cancellationToken = default
    )
    {
        // Add headers if provided
        if (headers != null)
            foreach (var header in headers)
                request.Headers.Add(name: header.Key, value: header.Value);

        // Use a timeout if specified
        var timeoutToken = GetTimeoutToken(timeoutSeconds: timeoutSeconds);
        using var linkedTokenSource = CancellationTokenSource.CreateLinkedTokenSource(
            token1: cancellationToken,
            token2: timeoutToken
        );

        try
        {
            var response = await _httpClient.SendAsync(
                request: request,
                completionOption: HttpCompletionOption.ResponseContentRead,
                cancellationToken: linkedTokenSource.Token
            );

            if (EnsureStatusCodeSuccess)
                await EnsureSuccessStatusCodeAsync(response: response);

            var content = await response.Content.ReadAsStringAsync(
                cancellationToken: linkedTokenSource.Token
            );

            if (string.IsNullOrEmpty(value: content))
                return default;

            return JsonConvert.DeserializeObject<TResponse>(value: content);
        }
        catch (OperationCanceledException) when (timeoutToken.IsCancellationRequested)
        {
            throw new TimeoutException(
                $"The request to {request.RequestUri} timed out after {timeoutSeconds ?? _httpClient.Timeout.TotalSeconds} seconds."
            );
        }
    }

    /// <summary>
    /// Sends an HTTP request without deserializing the response.
    /// </summary>
    /// <param name="request">The HTTP request message.</param>
    /// <param name="headers">Optional headers to include in the request.</param>
    /// <param name="timeoutSeconds">Optional timeout in seconds.</param>
    /// <param name="cancellationToken">Optional cancellation token.</param>
    /// <returns>The HTTP response message.</returns>
    private async Task<HttpResponseMessage> SendRequestWithoutDeserializationAsync(
        HttpRequestMessage request,
        Dictionary<string, string>? headers = null,
        int? timeoutSeconds = null,
        CancellationToken cancellationToken = default
    )
    {
        // Add headers if provided
        if (headers != null)
            foreach (var header in headers)
                request.Headers.Add(header.Key, header.Value);

        // Use a timeout if specified
        var timeoutToken = GetTimeoutToken(timeoutSeconds);
        using var linkedTokenSource = CancellationTokenSource.CreateLinkedTokenSource(
            cancellationToken,
            timeoutToken
        );

        try
        {
            var response = await _httpClient.SendAsync(
                request: request,
                completionOption: HttpCompletionOption.ResponseContentRead,
                cancellationToken: linkedTokenSource.Token
            );

            return response;
        }
        catch (OperationCanceledException) when (timeoutToken.IsCancellationRequested)
        {
            throw new TimeoutException(
                $"The request to {request.RequestUri} timed out after {timeoutSeconds ?? _httpClient.Timeout.TotalSeconds} seconds."
            );
        }
    }

    /// <summary>
    /// Creates a cancellation token that will be canceled after the specified timeout.
    /// </summary>
    /// <param name="timeoutSeconds">The timeout in seconds.</param>
    /// <returns>A cancellation token.</returns>
    private static CancellationToken GetTimeoutToken(int? timeoutSeconds)
    {
        if (!timeoutSeconds.HasValue)
            return CancellationToken.None;

        return new CancellationTokenSource(
            delay: TimeSpan.FromSeconds(value: timeoutSeconds.Value)
        ).Token;
    }

    /// <summary>
    /// Ensures that the HTTP response has a success status code.
    /// </summary>
    /// <param name="response">The HTTP response message.</param>
    /// <returns>A task representing the asynchronous operation.</returns>
    /// <exception cref="HttpRequestException">Thrown when the response has a non-success status code.</exception>
    private static async Task EnsureSuccessStatusCodeAsync(HttpResponseMessage response)
    {
        if (response.IsSuccessStatusCode)
            return;

        var content = await response.Content.ReadAsStringAsync();
        var errorMessage =
            $"HTTP request failed with status code {(int)response.StatusCode} ({response.StatusCode}).";

        if (!string.IsNullOrEmpty(value: content))
            errorMessage += $" Response content: {content}";

        throw new HttpRequestException(
            message: errorMessage,
            inner: null,
            statusCode: response.StatusCode
        );
    }
}

using System.ComponentModel.DataAnnotations;
using IDC.Utilities.Models.Data;
using static IDC.Utilities.Data.PostgreHelper;

namespace IDC.Utilities.Validations;

public static class Common
{
    /// <summary>
    /// Ensures that the specified argument is not null or white space.
    /// </summary>
    /// <param name="arg">The argument value.</param>
    /// <param name="argName">The name of the argument.</param>
    /// <exception cref="Exception">Thrown when the argument is null or white space.</exception>
    public static void EnsureNotNullOrWhiteSpace(
        this string? arg,
        string argName,
        string onNullMessage =
            "Parameter '{argName}' can not be null, empty, or constains only white space."
    )
    {
        if (string.IsNullOrWhiteSpace(arg))
            throw new ArgumentNullException(onNullMessage.Replace("{argName}", argName));
    }

    /// <summary>
    /// Ensures that the specified argument is not null.
    /// </summary>
    /// <param name="arg">The argument value.</param>
    /// <param name="argName">The name of the argument.</param>
    /// <exception cref="Exception">Thrown when the argument is null.</exception>
    public static void EnsureNotNull(
        this object? arg,
        string argName,
        string onNullMessage = "Parameter '{argName}' can not be null."
    )
    {
        if (arg is null)
            throw new ArgumentNullException(onNullMessage.Replace("{argName}", argName));
    }

    /// <summary>
    /// Ensures that the file exists at the specified file path.
    /// </summary>
    /// <param name="filePath">The path of the file to check.</param>
    /// <param name="onNotFoundMessage">The message to display if the file is not found.</param>
    /// <exception cref="FileNotFoundException">Thrown if the file does not exist.</exception>
    public static void EnsureFilePath(
        this string filePath,
        string onNotFoundMessage = "File cannot be found at the address: '{filePath}'."
    )
    {
        if (!File.Exists(filePath))
            throw new FileNotFoundException(onNotFoundMessage.Replace("{filePath}", filePath));
    }

    /// <summary>
    /// Ensures that the given data model is valid by performing validation on it.
    /// </summary>
    /// <typeparam name="T">The type of the data model.</typeparam>
    /// <param name="dataModel">The data model to validate.</param>
    /// <param name="result">The list of validation results.</param>
    /// <returns>True if the data model is valid, false otherwise.</returns>
    /// <exception cref="ArgumentNullException">Thrown if the data model is null.</exception>
    private static bool TryEnsureModel<T>(this T? dataModel, out List<ValidationResult> result)
    {
        if (dataModel is null)
            throw new ArgumentNullException(nameof(dataModel));

        var validate = new List<ValidationResult>();

        bool isValid = Validator.TryValidateObject(
            dataModel,
            new ValidationContext(dataModel),
            validate,
            true
        );

        result = validate;
        return isValid;
    }

    /// <summary>
    /// Ensures that the connection string is valid.
    /// </summary>
    /// <param name="connectionString">The connection string to validate.</param>
    /// <exception cref="ArgumentException">Thrown if the connection string is not valid.</exception>
    public static void EnsureModel(this CommonConnectionString connectionString)
    {
        if (!connectionString.TryEnsureModel(out var validate))
            throw new ArgumentException(validate[0].ErrorMessage);
    }

    /// <summary>
    /// Ensures the validity of the SPCallInfo object.
    /// </summary>
    /// <param name="spCallInfo">The SPCallInfo object to validate.</param>
    /// <exception cref="ArgumentException">Thrown when the SPCallInfo object is invalid.</exception>
    public static void EnsureModel(this SPCallInfo spCallInfo)
    {
        if (!spCallInfo.TryEnsureModel(out var validate))
            throw new ArgumentException(validate[0].ErrorMessage);
    }

    /// <summary>
    /// Ensures that the specified stored procedure parameter is valid, throwing an exception if it is not.
    /// </summary>
    /// <param name="spParameter">The stored procedure parameter to validate.</param>
    /// <exception cref="ArgumentException">Thrown if the stored procedure parameter fails validation.</exception>
    public static void EnsureModel(this SPParameter spParameter)
    {
        if (!spParameter.TryEnsureModel(out var validate))
            throw new ArgumentException(validate[0].ErrorMessage);
    }
}

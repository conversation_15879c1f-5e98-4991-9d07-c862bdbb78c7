# Copilot Guidelines for IDC.UTILITY Project

## General Instructions
- No need to explain what you've done. Just show the code.
- When you need to explain anything, always use "Bahasa Indonesia."

## Coding Standards
- Always add argument names when calling any method or function.
- Implement nullable and null safety in all code.
- Use simplified collection initialization and always utilize collection expressions.
- Ensure functions always return the class as the data type to enable method chaining.

## Documentation Standards
- Use English with a formal accent when generating documentation.
- Include the following sections in documentation:
  - **Summary**: A short description.
  - **Remarks**: Detailed explanations, including example code or request bodies for APIs.
  - **Parameters**: Provide links to documentation for custom non-generic data types.
  - **Returns**: Describe the return value.
  - **Exceptions**: List possible exceptions.
- Generate documentation for non-public methods as well.
- Use `<example>` and `<code>` tags for example code.
- Wrap JSON in the `code` tag when displaying it.
- Ensure XML documentation is compatible with DocFX and adheres to its rules for rendering to markdown.
- Limit documentation lines to a maximum of 100 characters.

### Alerts in Documentation
- Use the following alert types:
  - **NOTE**: `> [!NOTE] > Information the user should notice even if skimming.`
  - **TIP**: `> [!TIP] > Helpful information.`
  - **IMPORTANT**: `> [!IMPORTANT] > Critical information.`
  - **CAUTION**: `> [!CAUTION] > Potentially risky information.`
  - **WARNING**: `> [!WARNING] > Dangerous information.`

## Code Examples
- Always provide example code, regardless of its simplicity, in the **Remarks** section.
- Include example request bodies for APIs.

## Method Variants
- When asked to create a synchronous version, do not change or remove the existing method or function.
- For each existing method or function, also create an async version with callback parameters and cancellation tokens.

## Commit Message Standards
- Always provide detailed information regarding what changes occurred in each file.
- For every change, provide a list of the files.
using IDC.Utilities.Models.API;
using Newtonsoft.Json.Linq;

namespace IDC.Utilities.Comm.Http;

/// <summary>
/// Provides example usage of the HttpClientUtility class.
/// </summary>
/// <remarks>
/// This class demonstrates how to use the HttpClientUtility for various HTTP operations.
/// It includes examples for GET, POST, PUT, and DELETE requests with different response types.
///
/// > [!NOTE]
/// > This class is for demonstration purposes only and is not intended for production use.
///
/// > [!TIP]
/// > Use these examples as a reference when implementing your own HTTP client code.
/// </remarks>
public class HttpClientExample
{
    /// <summary>
    /// Demonstrates how to make a GET request to retrieve a strongly-typed object.
    /// </summary>
    /// <param name="id">The ID of the resource to retrieve.</param>
    /// <param name="cancellationToken">Optional cancellation token.</param>
    /// <returns>The retrieved user object.</returns>
    /// <remarks>
    /// This example shows how to make a GET request to retrieve a user by ID.
    ///
    /// Example usage:
    /// <code>
    /// var example = new HttpClientExample();
    /// var user = await example.GetUserByIdAsync(123);
    /// Console.WriteLine($"User name: {user.Name}");
    /// </code>
    /// </remarks>
    public async Task<UserModel?> GetUserByIdAsync(
        int id,
        CancellationToken cancellationToken = default
    )
    {
        var client = HttpClientUtility.Instance;

        // Add authorization header
        var headers = new Dictionary<string, string>
        {
            { "Authorization", "Bearer your-token-here" },
        };

        // Make the request with a 30-second timeout
        return await client.GetAsync<UserModel>(
            uri: $"https://api.example.com/users/{id}",
            headers: headers,
            timeoutSeconds: 30,
            cancellationToken: cancellationToken
        );
    }

    /// <summary>
    /// Demonstrates how to make a POST request to create a resource.
    /// </summary>
    /// <param name="user">The user object to create.</param>
    /// <param name="cancellationToken">Optional cancellation token.</param>
    /// <returns>The created user object.</returns>
    /// <remarks>
    /// This example shows how to make a POST request to create a new user.
    ///
    /// Example usage:
    /// <code>
    /// var example = new HttpClientExample();
    /// var newUser = new UserModel { Name = "John Doe", Email = "<EMAIL>" };
    /// var createdUser = await example.CreateUserAsync(newUser);
    /// Console.WriteLine($"Created user with ID: {createdUser.Id}");
    /// </code>
    /// </remarks>
    public async Task<UserModel?> CreateUserAsync(
        UserModel user,
        CancellationToken cancellationToken = default
    )
    {
        var client = HttpClientUtility.Instance;

        return await client.PostAsync<UserModel, UserModel>(
            uri: "https://api.example.com/users",
            content: user,
            cancellationToken: cancellationToken
        );
    }

    /// <summary>
    /// Demonstrates how to make a PUT request to update a resource.
    /// </summary>
    /// <param name="user">The user object to update.</param>
    /// <param name="cancellationToken">Optional cancellation token.</param>
    /// <returns>The updated user object.</returns>
    /// <remarks>
    /// This example shows how to make a PUT request to update an existing user.
    ///
    /// Example usage:
    /// <code>
    /// var example = new HttpClientExample();
    /// var user = await example.GetUserByIdAsync(123);
    /// user.Name = "Updated Name";
    /// var updatedUser = await example.UpdateUserAsync(user);
    /// Console.WriteLine($"Updated user name: {updatedUser.Name}");
    /// </code>
    /// </remarks>
    public async Task<UserModel?> UpdateUserAsync(
        UserModel user,
        CancellationToken cancellationToken = default
    )
    {
        var client = HttpClientUtility.Instance;

        return await client.PutAsync<UserModel, UserModel>(
            uri: $"https://api.example.com/users/{user.Id}",
            content: user,
            cancellationToken: cancellationToken
        );
    }

    /// <summary>
    /// Demonstrates how to make a DELETE request to delete a resource.
    /// </summary>
    /// <param name="id">The ID of the resource to delete.</param>
    /// <param name="cancellationToken">Optional cancellation token.</param>
    /// <returns>True if the deletion was successful, false otherwise.</returns>
    /// <remarks>
    /// This example shows how to make a DELETE request to delete a user by ID.
    ///
    /// Example usage:
    /// <code>
    /// var example = new HttpClientExample();
    /// var success = await example.DeleteUserAsync(123);
    /// Console.WriteLine($"User deletion {(success ? "succeeded" : "failed")}");
    /// </code>
    /// </remarks>
    public async Task<bool> DeleteUserAsync(int id, CancellationToken cancellationToken = default)
    {
        var client = HttpClientUtility.Instance;

        var response = await client.DeleteAsync(
            uri: $"https://api.example.com/users/{id}",
            cancellationToken: cancellationToken
        );

        return response.IsSuccessStatusCode;
    }

    /// <summary>
    /// Demonstrates how to make a GET request that returns an APIResponseData object.
    /// </summary>
    /// <param name="id">The ID of the resource to retrieve.</param>
    /// <param name="cancellationToken">Optional cancellation token.</param>
    /// <returns>The API response containing the user data.</returns>
    /// <remarks>
    /// This example shows how to make a GET request that returns an APIResponseData object.
    ///
    /// Example usage:
    /// <code>
    /// var example = new HttpClientExample();
    /// var response = await example.GetUserWithApiResponseAsync(123);
    ///
    /// if (response.IsSuccess)
    /// {
    ///     var user = response.Data;
    ///     Console.WriteLine($"User name: {user.Name}");
    /// }
    /// else
    /// {
    ///     Console.WriteLine($"Error: {response.Message}");
    /// }
    /// </code>
    /// </remarks>
    public async Task<APIResponseData<UserModel>> GetUserWithApiResponseAsync(
        int id,
        CancellationToken cancellationToken = default
    )
    {
        var client = HttpClientUtility.Instance;

        return await client.GetApiResponseDataAsync<UserModel>(
            uri: $"https://api.example.com/users/{id}",
            cancellationToken: cancellationToken
        );
    }

    /// <summary>
    /// Demonstrates how to make a POST request that returns an APIResponseData object.
    /// </summary>
    /// <param name="user">The user object to create.</param>
    /// <param name="cancellationToken">Optional cancellation token.</param>
    /// <returns>The API response containing the created user data.</returns>
    /// <remarks>
    /// This example shows how to make a POST request that returns an APIResponseData object.
    ///
    /// Example usage:
    /// <code>
    /// var example = new HttpClientExample();
    /// var newUser = new UserModel { Name = "John Doe", Email = "<EMAIL>" };
    /// var response = await example.CreateUserWithApiResponseAsync(newUser);
    ///
    /// if (response.IsSuccess)
    /// {
    ///     var createdUser = response.Data;
    ///     Console.WriteLine($"Created user with ID: {createdUser.Id}");
    /// }
    /// </code>
    /// </remarks>
    public async Task<APIResponseData<UserModel>> CreateUserWithApiResponseAsync(
        UserModel user,
        CancellationToken cancellationToken = default
    )
    {
        var client = HttpClientUtility.Instance;

        return await client.PostApiResponseDataAsync<UserModel, UserModel>(
            uri: "https://api.example.com/users",
            content: user,
            cancellationToken: cancellationToken
        );
    }

    /// <summary>
    /// Demonstrates how to make a GET request that returns a dynamic JObject.
    /// </summary>
    /// <param name="endpoint">The API endpoint to call.</param>
    /// <param name="cancellationToken">Optional cancellation token.</param>
    /// <returns>The response as a JObject.</returns>
    /// <remarks>
    /// This example shows how to make a GET request that returns a dynamic JObject.
    /// Useful when the response structure is not known in advance or is dynamic.
    ///
    /// Example usage:
    /// <code>
    /// var example = new HttpClientExample();
    /// var data = await example.GetDynamicDataAsync("weather/forecast");
    ///
    /// // Access properties dynamically
    /// var temperature = data["current"]?["temperature"]?.Value&lt;double&gt;();
    /// var conditions = data["current"]?["conditions"]?.ToString();
    /// </code>
    /// </remarks>
    public async Task<JObject?> GetDynamicDataAsync(
        string endpoint,
        CancellationToken cancellationToken = default
    )
    {
        var client = HttpClientUtility.Instance;

        return await client.GetJObjectAsync(
            uri: $"https://api.example.com/{endpoint}",
            cancellationToken: cancellationToken
        );
    }
}

/// <summary>
/// Represents a user model for demonstration purposes.
/// </summary>
/// <remarks>
/// This class is used in the HttpClientExample to demonstrate HTTP requests with a concrete model.
/// </remarks>
public class UserModel
{
    /// <summary>
    /// Gets or sets the user ID.
    /// </summary>
    /// <value>The unique identifier for the user.</value>
    public int Id { get; set; }

    /// <summary>
    /// Gets or sets the user's name.
    /// </summary>
    /// <value>The full name of the user.</value>
    public string? Name { get; set; }

    /// <summary>
    /// Gets or sets the user's email address.
    /// </summary>
    /// <value>The email address of the user.</value>
    public string? Email { get; set; }
}

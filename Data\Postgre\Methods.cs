using System.Data;
using IDC.Utilities.Validations;
using Newtonsoft.Json.Linq;
using Npgsql;

namespace IDC.Utilities.Data;

public sealed partial class <PERSON>gre<PERSON>elper
{
    /// <summary>
    /// Checks if database connection is open.
    /// </summary>
    /// <returns>
    /// <see cref="bool"/> indicating connection status. True if connected, false otherwise.
    /// See <see href="https://www.npgsql.org/doc/api/Npgsql.NpgsqlConnection.html#Npgsql_NpgsqlConnection_State">
    /// Connection States</see> for more details.
    /// </returns>
    /// <remarks>
    /// Performs quick connection state validation without additional status information.
    /// Useful for simple connection checks before executing commands.
    ///
    /// Features:
    /// - Fast connection validation
    /// - No additional status message
    /// - Thread-safe operation
    /// - Automatic disposal check
    ///
    /// Example Request:
    /// <code>
    /// {
    ///   // No parameters required
    /// }
    /// </code>
    ///
    /// Example Usage:
    /// <example>
    /// <code>
    /// var db = new PostgreHelper(
    ///     connectionString: "Host=localhost;Database=mydb;Username=user;Password=****"
    /// );
    ///
    /// if (db.IsConnected())
    /// {
    ///     db.ExecuteNonQuery(
    ///         query: "INSERT INTO Users (Name) VALUES (@name)",
    ///         affectedRows: out int rows
    ///     );
    /// }
    /// </code>
    /// </example>
    ///
    /// > [!IMPORTANT]
    /// > Always validate connection before executing database operations
    ///
    /// > [!NOTE]
    /// > For detailed status information, use IsConnected(out string message) overload
    ///
    /// > [!TIP]
    /// > Use in conditional statements for quick connection checks
    ///
    /// > [!CAUTION]
    /// > Connection state may change between validation and command execution
    ///
    /// > [!WARNING]
    /// > Does not provide detailed error information on connection failure
    /// </remarks>
    /// <exception cref="ObjectDisposedException">
    /// Thrown when accessing disposed <see cref="PostgreHelper"/> instance.
    /// See <see href="https://learn.microsoft.com/en-us/dotnet/api/system.objectdisposedexception">
    /// ObjectDisposedException</see> for more details.
    /// </exception>
    /// <seealso cref="IsConnected(out string)"/>
    /// <seealso cref="NpgsqlConnection.State"/>
    /// <seealso href="https://www.npgsql.org/doc/connection-state.html">
    /// Npgsql Connection State Documentation
    /// </seealso>
    public bool IsConnected()
    {
        ThrowIfDisposed();
        return _connection.State == ConnectionState.Open;
    }

    /// <summary>
    /// Checks connection status and returns status message.
    /// </summary>
    /// <param name="message">
    /// Output parameter containing detailed connection status message.
    /// See <see href="https://www.npgsql.org/doc/api/Npgsql.NpgsqlConnection.html#Npgsql_NpgsqlConnection_State">
    /// Connection States</see> for possible values.
    /// </param>
    /// <returns>
    /// Current <see cref="PostgreHelper"/> instance for method chaining.
    /// </returns>
    /// <remarks>
    /// Validates connection state and provides detailed status information.
    /// Supports method chaining for fluent API usage.
    ///
    /// Features:
    /// - Connection state validation
    /// - Detailed status reporting
    /// - Method chaining support
    /// - Error logging
    ///
    /// Example Request:
    /// <code>
    /// {
    ///   "message": "String output parameter for status message"
    /// }
    /// </code>
    ///
    /// Example Usage:
    /// <example>
    /// <code>
    /// var db = new PostgreHelper("connection_string");
    ///
    /// if (db.IsConnected(message: out string status))
    /// {
    ///     Console.WriteLine($"Database connection status: {status}");
    ///     db.ExecuteNonQuery(
    ///         query: "INSERT INTO logs (status) VALUES (@status)",
    ///         affectedRows: out _
    ///     );
    /// }
    /// </code>
    /// </example>
    ///
    /// > [!IMPORTANT]
    /// > Always check connection status before executing commands
    ///
    /// > [!NOTE]
    /// > Status message includes server version and connection details
    ///
    /// > [!TIP]
    /// > Use this method for connection health monitoring
    ///
    /// > [!CAUTION]
    /// > Connection state can change after validation
    /// </remarks>
    /// <exception cref="ObjectDisposedException">
    /// Thrown when accessing disposed <see cref="PostgreHelper"/> instance.
    /// </exception>
    /// <exception cref="PostgresException">
    /// Thrown when PostgreSQL-specific error occurs during validation.
    /// </exception>
    /// <exception cref="Exception">
    /// Rethrows any exceptions that occur during validation with full stack trace.
    /// </exception>
    /// <seealso cref="NpgsqlConnection.State"/>
    /// <seealso cref="ConnectionState"/>
    /// <seealso href="https://www.npgsql.org/doc/connection-state.html">
    /// Npgsql Connection State Documentation
    /// </seealso>
    public bool IsConnected(out string message) => ConnectionIsValid(message: out message);

    /// <summary>
    /// Creates or reuses database connection with optional transaction.
    /// </summary>
    /// <param name="useTransaction">
    /// Optional boolean flag to start a transaction. Defaults to false.
    /// </param>
    /// <param name="disconnectFirst">
    /// Optional boolean flag to force disconnect before connecting. Defaults to false.
    /// </param>
    /// <returns>
    /// Current <see cref="PostgreHelper"/> instance for method chaining.
    /// </returns>
    /// <remarks>
    /// Internal method for synchronous connection management.
    /// Opens connection if closed and optionally starts transaction.
    ///
    /// Features:
    /// - Synchronous connection handling
    /// - Optional transaction support
    /// - Connection state management
    /// - Method chaining capability
    /// - Automatic resource management
    ///
    /// Example Request:
    /// <code>
    /// {
    ///   "useTransaction": true,
    ///   "disconnectFirst": false
    /// }
    /// </code>
    ///
    /// Example Usage:
    /// <example>
    /// <code>
    /// var db = CreateConnection(
    ///   useTransaction: true,
    ///   disconnectFirst: false
    /// );
    ///
    /// try
    /// {
    ///   db.ExecuteNonQuery(
    ///     query: "INSERT INTO Users (Name) VALUES (@name)",
    ///     affectedRows: out int rows
    ///   )
    ///   .Commit();
    /// }
    /// catch
    /// {
    ///   db.Rollback();
    ///   throw;
    /// }
    /// </code>
    /// </example>
    ///
    /// > [!IMPORTANT]
    /// > Ensure proper transaction handling (Commit/Rollback) when useTransaction is true
    ///
    /// > [!NOTE]
    /// > Connection state is preserved unless disconnectFirst is true
    ///
    /// > [!TIP]
    /// > For asynchronous operations, use CreateConnectionAsync instead
    ///
    /// > [!CAUTION]
    /// > Multiple active transactions are not supported
    ///
    /// > [!WARNING]
    /// > Ensure proper error handling and transaction management
    /// </remarks>
    /// <exception cref="ObjectDisposedException">
    /// Thrown when accessing disposed <see cref="PostgreHelper"/> instance.
    /// </exception>
    /// <exception cref="PostgresException">
    /// Thrown when PostgreSQL-specific error occurs during connection.
    /// </exception>
    /// <exception cref="Exception">
    /// Rethrows any exceptions that occur during connection with full stack trace.
    /// </exception>
    /// <seealso cref="NpgsqlConnection.Open"/>
    /// <seealso cref="NpgsqlConnection.Close"/>
    /// <seealso href="https://www.npgsql.org/doc/api/Npgsql.NpgsqlConnection.html">
    /// Npgsql Connection Documentation
    /// </seealso>
    private PostgreHelper CreateConnection(
        bool useTransaction = false,
        bool disconnectFirst = false
    )
    {
        try
        {
            ThrowIfDisposed();

            if (disconnectFirst)
                _connection.Close();

            if (!_connection.State.HasFlag(flag: ConnectionState.Open))
                _connection.Open();

            if (useTransaction)
                TransactionBegin();

            return this;
        }
        catch (Exception ex)
        {
            _logging?.LogError(exception: ex);
            throw;
        }
    }

    /// <summary>
    /// Creates or reuses database connection asynchronously with optional transaction.
    /// </summary>
    /// <param name="useTransaction">
    /// Optional boolean flag to start a transaction. Defaults to false.
    /// </param>
    /// <param name="disconnectFirst">
    /// Optional boolean flag to force disconnect before connecting. Defaults to false.
    /// </param>
    /// <param name="cancellationToken">
    /// Optional <see cref="CancellationToken"/> to cancel the asynchronous operation.
    /// Defaults to <see cref="CancellationToken.None"/>.
    /// </param>
    /// <returns>
    /// <see cref="Task{TResult}"/> containing current <see cref="PostgreHelper"/> instance for method chaining.
    /// </returns>
    /// <remarks>
    /// Internal method for asynchronous connection management.
    /// Opens connection if closed and optionally starts transaction.
    ///
    /// Features:
    /// - Asynchronous connection handling
    /// - Optional transaction support
    /// - Connection state management
    /// - Cancellation support
    /// - Method chaining capability
    ///
    /// Example Request:
    /// <code>
    /// {
    ///   "useTransaction": true,
    ///   "disconnectFirst": false,
    ///   "cancellationToken": "CancellationToken instance"
    /// }
    /// </code>
    ///
    /// Example Usage:
    /// <example>
    /// <code>
    /// var cts = new CancellationTokenSource(TimeSpan.FromSeconds(30));
    ///
    /// var db = await CreateConnectionAsync(
    ///   useTransaction: true,
    ///   disconnectFirst: false,
    ///   cancellationToken: cts.Token
    /// );
    ///
    /// try
    /// {
    ///   // Perform database operations
    ///   await db.CommitAsync();
    /// }
    /// catch
    /// {
    ///   await db.RollbackAsync();
    ///   throw;
    /// }
    /// </code>
    /// </example>
    ///
    /// > [!IMPORTANT]
    /// > Ensure proper transaction handling (Commit/Rollback) when useTransaction is true
    ///
    /// > [!NOTE]
    /// > Connection state is preserved unless disconnectFirst is true
    ///
    /// > [!TIP]
    /// > Use cancellationToken for timeout handling in long-running operations
    ///
    /// > [!CAUTION]
    /// > Multiple active transactions are not supported
    ///
    /// > [!WARNING]
    /// > Cancellation may leave the connection in an undefined state
    /// </remarks>
    /// <exception cref="ObjectDisposedException">
    /// Thrown when accessing disposed <see cref="PostgreHelper"/> instance.
    /// </exception>
    /// <exception cref="OperationCanceledException">
    /// Thrown when operation is canceled via cancellationToken.
    /// </exception>
    /// <exception cref="PostgresException">
    /// Thrown when PostgreSQL-specific error occurs during connection.
    /// </exception>
    /// <exception cref="Exception">
    /// Rethrows any exceptions that occur during connection with full stack trace.
    /// </exception>
    /// <seealso cref="NpgsqlConnection.OpenAsync"/>
    /// <seealso cref="NpgsqlConnection.CloseAsync"/>
    /// <seealso href="https://www.npgsql.org/doc/api/Npgsql.NpgsqlConnection.html">
    /// Npgsql Connection Documentation
    /// </seealso>
    private async Task<PostgreHelper> CreateConnectionAsync(
        bool useTransaction = false,
        bool disconnectFirst = false,
        CancellationToken cancellationToken = default
    )
    {
        try
        {
            ThrowIfDisposed();

            if (disconnectFirst)
                await _connection.CloseAsync();

            if (!_connection.State.HasFlag(flag: ConnectionState.Open))
                await _connection.OpenAsync(cancellationToken: cancellationToken);

            if (useTransaction)
                TransactionBegin();

            return this;
        }
        catch (Exception ex)
        {
            _logging?.LogError(exception: ex);
            throw;
        }
    }

    /// <summary>
    /// Creates command object for executing queries.
    /// </summary>
    /// <param name="query">
    /// SQL query text to execute against PostgreSQL database. Must not be null or empty.
    /// </param>
    /// <param name="commandType">
    /// Type of command to execute. See <see cref="CommandType"/> for available options.
    /// Defaults to <see cref="CommandType.Text"/>.
    /// </param>
    /// <returns>
    /// Configured <see href="https://www.npgsql.org/doc/api/Npgsql.NpgsqlCommand.html">NpgsqlCommand</see>
    /// instance for query execution.
    /// </returns>
    /// <remarks>
    /// Internal method for creating and configuring database command objects.
    /// Validates connection state and query string before creating command.
    ///
    /// Features:
    /// - Connection validation
    /// - Query validation
    /// - Command type configuration
    /// - Error logging
    /// - Resource management
    ///
    /// Example Request:
    /// <code>
    /// {
    ///   "query": "SELECT * FROM users WHERE id = @userId",
    ///   "commandType": "CommandType.Text"
    /// }
    /// </code>
    ///
    /// Example Usage:
    /// <example>
    /// <code>
    /// using var command = CreateCommand(
    ///   query: "SELECT * FROM Users WHERE Active = @isActive",
    ///   commandType: CommandType.Text
    /// );
    ///
    /// command.Parameters.AddWithValue(
    ///   parameterName: "isActive",
    ///   value: true
    /// );
    /// </code>
    /// </example>
    ///
    /// > [!IMPORTANT]
    /// > Command objects must be properly disposed after use
    ///
    /// > [!NOTE]
    /// > Connection must be valid before creating command
    ///
    /// > [!TIP]
    /// > Use parameters to prevent SQL injection
    ///
    /// > [!CAUTION]
    /// > Ensure proper error handling when using returned command
    /// </remarks>
    /// <exception cref="ArgumentException">Thrown when query parameter is null or whitespace.</exception>
    /// <exception cref="DataException">Thrown when database connection is invalid.</exception>
    /// <exception cref="ObjectDisposedException">Thrown when accessing disposed PostgreHelper instance.</exception>
    /// <exception cref="PostgresException">Thrown when PostgreSQL error occurs during command creation.</exception>
    /// <exception cref="Exception">Rethrows any exceptions that occur during command creation.</exception>
    /// <seealso cref="NpgsqlCommand"/>
    /// <seealso cref="CommandType"/>
    /// <seealso href="https://www.npgsql.org/doc/api/Npgsql.NpgsqlCommand.html">
    /// Npgsql Command Documentation
    /// </seealso>
    private NpgsqlCommand CreateCommand(string query, CommandType commandType = CommandType.Text)
    {
        try
        {
            ThrowIfDisposed();

            if (!ConnectionIsValid(message: out var errorMessage))
                throw new DataException(s: errorMessage);

            ArgumentException.ThrowIfNullOrWhiteSpace(query);

            var command = _connection.CreateCommand();
            command.CommandText = query;
            command.CommandType = commandType;

            return command;
        }
        catch (Exception ex)
        {
            _logging?.LogError(exception: ex);
            throw;
        }
    }

    /// <summary>
    /// Executes non-query SQL command.
    /// </summary>
    /// <param name="query">
    /// SQL command text to execute against PostgreSQL database. Must not be null or empty.
    /// </param>
    /// <param name="affectedRows">
    /// Output parameter containing the number of rows affected by the command.
    /// </param>
    /// <returns>
    /// Current <see cref="PostgreHelper"/> instance for method chaining.
    /// </returns>
    /// <remarks>
    /// Executes non-query SQL commands (INSERT, UPDATE, DELETE) and returns number of affected rows.
    /// Supports method chaining and automatic resource disposal.
    ///
    /// Features:
    /// - Synchronous execution
    /// - Method chaining
    /// - Automatic resource disposal
    /// - Transaction support
    /// - Error logging
    ///
    /// Example Request:
    /// <code>
    /// {
    ///   "query": "INSERT INTO users (name, email) VALUES (@name, @email)",
    ///   "affectedRows": "Integer output parameter"
    /// }
    /// </code>
    ///
    /// Example Usage:
    /// <example>
    /// <code>
    /// db.Connect()
    ///   .ExecuteNonQuery(
    ///     query: "INSERT INTO Users (Name, Email) VALUES ('John', '<EMAIL>')",
    ///     affectedRows: out int rows
    ///   )
    ///   .Commit()
    ///   .Disconnect();
    ///
    /// Console.WriteLine($"Affected rows: {rows}");
    /// </code>
    /// </example>
    ///
    /// > [!IMPORTANT]
    /// > Ensure proper disposal of the PostgreHelper instance after use
    ///
    /// > [!NOTE]
    /// > For asynchronous operations, use ExecuteNonQueryAsync instead
    ///
    /// > [!TIP]
    /// > Use parameterized queries to prevent SQL injection
    ///
    /// > [!CAUTION]
    /// > Ensure proper transaction handling for data consistency
    /// </remarks>
    /// <exception cref="ArgumentException">Thrown when query parameter is null or whitespace.</exception>
    /// <exception cref="ObjectDisposedException">Thrown when accessing a disposed PostgreHelper instance.</exception>
    /// <exception cref="PostgresException">Thrown when a PostgreSQL error occurs during execution.</exception>
    /// <exception cref="Exception">Rethrows any exceptions that occur during execution with full stack trace.</exception>
    /// <seealso cref="NpgsqlCommand.ExecuteNonQuery"/>
    /// <seealso href="https://www.npgsql.org/doc/api/Npgsql.NpgsqlCommand.html#Npgsql_NpgsqlCommand_ExecuteNonQuery">
    /// Npgsql ExecuteNonQuery Documentation
    /// </seealso>
    public PostgreHelper ExecuteNonQuery(string query, out int affectedRows)
    {
        try
        {
            ThrowIfDisposed();
            ArgumentException.ThrowIfNullOrWhiteSpace(query);

            using var command = CreateCommand(query: query);
            affectedRows = command.ExecuteNonQuery();

            return this;
        }
        catch (Exception ex)
        {
            _logging?.LogError(exception: ex);
            throw;
        }
    }

    /// <summary>
    /// Executes non-query SQL command asynchronously.
    /// </summary>
    /// <param name="query">
    /// SQL command text to execute against PostgreSQL database. Must not be null or empty.
    /// </param>
    /// <param name="callback">
    /// Optional <see cref="Action{T}"/> delegate to process affected rows count.
    /// </param>
    /// <param name="cancellationToken">
    /// Optional token to cancel the asynchronous operation. See <see cref="CancellationToken"/>.
    /// </param>
    /// <returns>
    /// Tuple containing:
    /// - helper: Current <see cref="PostgreHelper"/> instance for method chaining
    /// - affectedRows: Number of rows affected by the command
    /// </returns>
    /// <remarks>
    /// Executes non-query SQL commands asynchronously (INSERT, UPDATE, DELETE).
    /// Supports cancellation, method chaining, and optional result processing.
    ///
    /// Features:
    /// - Asynchronous execution
    /// - Cancellation support
    /// - Method chaining
    /// - Optional callback processing
    /// - Automatic resource disposal
    ///
    /// Example Request:
    /// <code>
    /// {
    ///   "query": "INSERT INTO users (name, email) VALUES (@name, @email)",
    ///   "callback": "Optional Action delegate to process affected rows",
    ///   "cancellationToken": "Optional CancellationToken instance"
    /// }
    /// </code>
    ///
    /// Example Usage:
    /// <example>
    /// <code>
    /// var cts = new CancellationTokenSource();
    ///
    /// var (db, rows) = await db.Connect()
    ///   .ExecuteNonQueryAsync(
    ///     query: "INSERT INTO Users (Name) VALUES ('John')",
    ///     callback: count => Console.WriteLine($"Affected rows: {count}"),
    ///     cancellationToken: cts.Token
    ///   );
    ///
    /// if (rows > 0)
    ///   await db.CommitAsync();
    /// else
    ///   await db.RollbackAsync();
    /// </code>
    /// </example>
    ///
    /// > [!IMPORTANT]
    /// > Ensure proper disposal of the PostgreHelper instance after use
    ///
    /// > [!NOTE]
    /// > The callback is executed before the method returns
    ///
    /// > [!TIP]
    /// > Use parameterized queries to prevent SQL injection
    ///
    /// > [!CAUTION]
    /// > Long-running operations should implement cancellation logic
    /// </remarks>
    /// <exception cref="ArgumentException">Thrown when query parameter is null or whitespace.</exception>
    /// <exception cref="ObjectDisposedException">Thrown when accessing a disposed PostgreHelper instance.</exception>
    /// <exception cref="OperationCanceledException">Thrown when operation is canceled via cancellationToken.</exception>
    /// <exception cref="PostgresException">Thrown when a PostgreSQL error occurs during execution.</exception>
    /// <exception cref="Exception">Rethrows any exceptions that occur during execution with full stack trace.</exception>
    /// <seealso cref="NpgsqlCommand.ExecuteNonQueryAsync"/>
    /// <seealso href="https://www.npgsql.org/doc/api/Npgsql.NpgsqlCommand.html#Npgsql_NpgsqlCommand_ExecuteNonQueryAsync">
    /// Npgsql ExecuteNonQueryAsync Documentation
    /// </seealso>
    public async Task<(PostgreHelper helper, int affectedRows)> ExecuteNonQueryAsync(
        string query,
        Action<int>? callback = null,
        CancellationToken cancellationToken = default
    )
    {
        try
        {
            ThrowIfDisposed();
            ArgumentException.ThrowIfNullOrWhiteSpace(query);

            using var command = CreateCommand(query: query);
            int affectedRows = await command.ExecuteNonQueryAsync(
                cancellationToken: cancellationToken
            );

            callback?.Invoke(obj: affectedRows);

            return (helper: this, affectedRows);
        }
        catch (Exception ex)
        {
            _logging?.LogError(exception: ex);
            throw;
        }
    }

    /// <summary>
    /// Executes SQL query returning scalar value.
    /// </summary>
    /// <param name="query">
    /// SQL query text to execute against PostgreSQL database. Must not be null or empty.
    /// </param>
    /// <param name="result">
    /// Query result as nullable object containing first column of first row.
    /// </param>
    /// <returns>
    /// Current <see cref="PostgreHelper"/> instance for method chaining.
    /// </returns>
    /// <remarks>
    /// Executes a SQL query and returns a single value from the first row and column.
    /// Ideal for aggregate functions (COUNT, SUM, etc) and single value queries.
    ///
    /// Features:
    /// - Synchronous execution
    /// - Nullable result handling
    /// - Method chaining
    /// - Automatic resource disposal
    /// - Type-safe conversion support
    ///
    /// Example Request:
    /// <code>
    /// {
    ///   "query": "SELECT COUNT(*) FROM users WHERE active = true",
    ///   "result": "Object output parameter"
    /// }
    /// </code>
    ///
    /// Example Usage:
    /// <example>
    /// <code>
    /// db.Connect()
    ///   .ExecuteScalar(
    ///     query: "SELECT COUNT(*) FROM Users",
    ///     result: out object? count
    ///   )
    ///   .Disconnect();
    ///
    /// // Convert result to specific type
    /// int userCount = Convert.ToInt32(count);
    /// </code>
    /// </example>
    ///
    /// > [!IMPORTANT]
    /// > Ensure proper disposal of the PostgreHelper instance after use
    ///
    /// > [!NOTE]
    /// > For asynchronous operations, use ExecuteScalarAsync instead
    ///
    /// > [!TIP]
    /// > Use Convert.ChangeType or explicit casting to convert the result to the expected type
    ///
    /// > [!CAUTION]
    /// > Result may be null if the query returns no rows or NULL value
    /// </remarks>
    /// <exception cref="ArgumentException">Thrown when query parameter is null or whitespace.</exception>
    /// <exception cref="ObjectDisposedException">Thrown when accessing a disposed PostgreHelper instance.</exception>
    /// <exception cref="InvalidCastException">Thrown when result cannot be converted to expected type.</exception>
    /// <exception cref="Exception">Rethrows any exceptions that occur during execution with full stack trace.</exception>
    /// <seealso cref="NpgsqlCommand.ExecuteScalar"/>
    /// <seealso href="https://www.npgsql.org/doc/api/Npgsql.NpgsqlCommand.html#Npgsql_NpgsqlCommand_ExecuteScalar">
    /// Npgsql ExecuteScalar Documentation
    /// </seealso>
    public PostgreHelper ExecuteScalar(string query, out object? result)
    {
        try
        {
            ThrowIfDisposed();
            ArgumentException.ThrowIfNullOrWhiteSpace(query);

            using var command = CreateCommand(query: query);
            result = command.ExecuteScalar();

            return this;
        }
        catch (Exception ex)
        {
            _logging?.LogError(exception: ex);
            throw;
        }
    }

    /// <summary>
    /// Executes SQL query returning scalar value asynchronously.
    /// </summary>
    /// <param name="query">
    /// SQL query text to execute against PostgreSQL database. Must not be null or empty.
    /// </param>
    /// <param name="callback">
    /// Optional <see cref="Action{T}"/> delegate to process the scalar result. Accepts nullable object parameter.
    /// </param>
    /// <param name="cancellationToken">
    /// Token to cancel the asynchronous operation. See <see cref="CancellationToken"/>.
    /// </param>
    /// <returns>
    /// Tuple containing:
    /// - helper: Current <see cref="PostgreHelper"/> instance for method chaining
    /// - result: Nullable object containing first column of first row
    /// </returns>
    /// <remarks>
    /// Executes a SQL query asynchronously and returns a single value from the first row and column.
    /// Ideal for aggregate functions (COUNT, SUM, etc) and single value queries.
    ///
    /// Features:
    /// - Asynchronous execution
    /// - Nullable result handling
    /// - Cancellation support
    /// - Method chaining
    /// - Optional callback processing
    ///
    /// Example Request:
    /// <code>
    /// {
    ///   "query": "SELECT COUNT(*) FROM users WHERE active = true",
    ///   "callback": "Optional Action delegate to process result",
    ///   "cancellationToken": "Optional CancellationToken instance"
    /// }
    /// </code>
    ///
    /// Example Usage:
    /// <example>
    /// <code>
    /// var (db, count) = await db.Connect()
    ///   .ExecuteScalarAsync(
    ///     query: "SELECT COUNT(*) FROM Users",
    ///     callback: result => Console.WriteLine($"Total users: {result}"),
    ///     cancellationToken: new CancellationTokenSource().Token
    ///   );
    ///
    /// // Convert result to specific type
    /// int userCount = Convert.ToInt32(count);
    /// </code>
    /// </example>
    ///
    /// > [!IMPORTANT]
    /// > Ensure proper disposal of the PostgreHelper instance after use
    ///
    /// > [!NOTE]
    /// > The callback is executed before the method returns, allowing for immediate result processing
    ///
    /// > [!TIP]
    /// > Use Convert.ChangeType or explicit casting to convert the result to the expected type
    ///
    /// > [!CAUTION]
    /// > Result may be null if the query returns no rows or NULL value
    /// </remarks>
    /// <exception cref="ArgumentException">Thrown when query parameter is null or whitespace.</exception>
    /// <exception cref="ObjectDisposedException">Thrown when accessing a disposed PostgreHelper instance.</exception>
    /// <exception cref="OperationCanceledException">Thrown when operation is canceled via cancellationToken.</exception>
    /// <exception cref="InvalidCastException">Thrown when result cannot be converted to expected type.</exception>
    /// <exception cref="Exception">Rethrows any exceptions that occur during execution with full stack trace.</exception>
    /// <seealso cref="NpgsqlCommand.ExecuteScalarAsync"/>
    /// <seealso href="https://www.npgsql.org/doc/api/Npgsql.NpgsqlCommand.html#Npgsql_NpgsqlCommand_ExecuteScalarAsync">
    /// Npgsql ExecuteScalarAsync Documentation
    /// </seealso>
    public async Task<(PostgreHelper helper, object? result)> ExecuteScalarAsync(
        string query,
        Action<object?>? callback = null,
        CancellationToken cancellationToken = default
    )
    {
        try
        {
            ThrowIfDisposed();
            ArgumentException.ThrowIfNullOrWhiteSpace(query);

            using var command = CreateCommand(query: query);
            var result = await command.ExecuteScalarAsync(cancellationToken: cancellationToken);

            callback?.Invoke(obj: result);

            return (helper: this, result);
        }
        catch (Exception ex)
        {
            _logging?.LogError(exception: ex);
            throw;
        }
    }

    /// <summary>
    /// Executes SQL query returning data reader.
    /// </summary>
    /// <param name="query">
    /// SQL query text to execute against PostgreSQL database. Must not be null or empty.
    /// </param>
    /// <param name="reader">
    /// Query result as <see href="https://www.npgsql.org/doc/api/Npgsql.NpgsqlDataReader.html">NpgsqlDataReader</see>
    /// for row-by-row data access.
    /// </param>
    /// <returns>Current <see cref="PostgreHelper"/> instance for method chaining.</returns>
    /// <remarks>
    /// Executes a SQL query and provides direct access to results via NpgsqlDataReader.
    /// Supports method chaining and manual result iteration.
    ///
    /// Features:
    /// - Synchronous execution
    /// - Row-by-row data access
    /// - Method chaining
    /// - Manual iteration control
    /// - Type-safe column access
    ///
    /// Example Request:
    /// <code>
    /// {
    ///   "query": "SELECT id, name, email FROM users WHERE active = true",
    ///   "reader": "NpgsqlDataReader output parameter"
    /// }
    /// </code>
    ///
    /// Example Usage:
    /// <example>
    /// <code>
    /// db.Connect()
    ///   .ExecuteReader(
    ///     query: "SELECT * FROM Users",
    ///     reader: out var reader
    ///   );
    ///
    /// while (reader.Read())
    /// {
    ///     var id = reader.GetInt32(name: "id");
    ///     var name = reader.GetString(name: "name");
    ///     Console.WriteLine($"ID: {id}, Name: {name}");
    /// }
    /// </code>
    /// </example>
    ///
    /// > [!IMPORTANT]
    /// > Ensure proper disposal of both the PostgreHelper instance and the DataReader after use
    ///
    /// > [!NOTE]
    /// > For asynchronous operations, use ExecuteReaderAsync instead
    ///
    /// > [!TIP]
    /// > Use GetFieldValue&lt;T&gt; or strongly-typed Get methods for type-safe column access
    /// </remarks>
    /// <exception cref="ArgumentException">Thrown when query parameter is null or whitespace.</exception>
    /// <exception cref="ObjectDisposedException">Thrown when accessing a disposed PostgreHelper instance.</exception>
    /// <exception cref="Exception">Rethrows any exceptions that occur during execution with full stack trace.</exception>
    /// <seealso cref="NpgsqlCommand.ExecuteReader"/>
    /// <seealso cref="NpgsqlDataReader"/>
    /// <seealso href="https://www.npgsql.org/doc/api/Npgsql.NpgsqlDataReader.html">NpgsqlDataReader Documentation</seealso>
    /// <seealso href="https://www.npgsql.org/doc/api/Npgsql.NpgsqlCommand.html">Npgsql Documentation</seealso>
    public PostgreHelper ExecuteReader(string query, out NpgsqlDataReader reader)
    {
        try
        {
            ThrowIfDisposed();
            ArgumentException.ThrowIfNullOrWhiteSpace(query);

            using var command = CreateCommand(query: query);
            reader = command.ExecuteReader();

            return this;
        }
        catch (Exception ex)
        {
            _logging?.LogError(exception: ex);
            throw;
        }
    }

    /// <summary>
    /// Executes SQL query returning data reader asynchronously with support for cancellation and callback processing.
    /// </summary>
    /// <param name="query">
    /// SQL query text to execute against PostgreSQL database. Must not be null or empty.
    /// </param>
    /// <param name="callback">
    /// Optional callback action to process the <see href="https://www.npgsql.org/doc/api/Npgsql.NpgsqlDataReader.html">NpgsqlDataReader</see>.
    /// </param>
    /// <param name="cancellationToken">
    /// Token to cancel the asynchronous operation. See <see cref="CancellationToken"/>.
    /// </param>
    /// <returns>
    /// Tuple containing:
    /// - helper: Current <see cref="PostgreHelper"/> instance for method chaining
    /// - reader: <see href="https://www.npgsql.org/doc/api/Npgsql.NpgsqlDataReader.html">NpgsqlDataReader</see>
    /// for data access
    /// </returns>
    /// <remarks>
    /// Executes a SQL query asynchronously and provides direct access to results via NpgsqlDataReader.
    /// Supports cancellation, method chaining, and optional result processing via callback.
    ///
    /// Features:
    /// - Asynchronous execution
    /// - Row-by-row data access
    /// - Cancellation support
    /// - Method chaining
    /// - Optional callback processing
    ///
    /// Example Request:
    /// <code>
    /// {
    ///   "query": "SELECT id, name, email FROM users WHERE active = true",
    ///   "callback": "Optional Action delegate to process DataReader",
    ///   "cancellationToken": "Optional CancellationToken instance"
    /// }
    /// </code>
    ///
    /// Example Usage:
    /// <example>
    /// <code>
    /// var (db, reader) = await db.Connect()
    ///   .ExecuteReaderAsync(
    ///     query: "SELECT * FROM Users",
    ///     callback: reader => {
    ///       while(reader.Read())
    ///         Console.WriteLine(reader["Name"]);
    ///     },
    ///     cancellationToken: new CancellationTokenSource().Token
    ///   );
    /// </code>
    /// </example>
    ///
    /// > [!IMPORTANT]
    /// > Ensure proper disposal of both the PostgreHelper instance and the DataReader after use
    ///
    /// > [!NOTE]
    /// > The callback is executed before the method returns, allowing for immediate result processing
    ///
    /// > [!TIP]
    /// > Use GetFieldValue&lt;T&gt; for type-safe column access
    /// </remarks>
    /// <exception cref="ArgumentException">Thrown when query parameter is null or whitespace.</exception>
    /// <exception cref="ObjectDisposedException">Thrown when accessing a disposed PostgreHelper instance.</exception>
    /// <exception cref="OperationCanceledException">Thrown when operation is canceled via cancellationToken.</exception>
    /// <exception cref="Exception">Rethrows any exceptions that occur during execution with full stack trace.</exception>
    /// <seealso cref="NpgsqlCommand.ExecuteReaderAsync"/>
    /// <seealso cref="NpgsqlDataReader"/>
    /// <seealso href="https://www.npgsql.org/doc/api/Npgsql.NpgsqlDataReader.html">NpgsqlDataReader Documentation</seealso>
    /// <seealso href="https://www.npgsql.org/doc/api/Npgsql.NpgsqlCommand.html">Npgsql Documentation</seealso>
    public async Task<(PostgreHelper helper, NpgsqlDataReader reader)> ExecuteReaderAsync(
        string query,
        Action<NpgsqlDataReader>? callback = null,
        CancellationToken cancellationToken = default
    )
    {
        try
        {
            ThrowIfDisposed();
            ArgumentException.ThrowIfNullOrWhiteSpace(query);

            using var command = CreateCommand(query: query);
            var reader = await command.ExecuteReaderAsync(cancellationToken: cancellationToken);

            callback?.Invoke(obj: reader);

            return (helper: this, reader);
        }
        catch (Exception ex)
        {
            _logging?.LogError(exception: ex);
            throw;
        }
    }

    /// <summary>
    /// Executes SQL query returning results as JObject list.
    /// </summary>
    /// <param name="query">SQL query text to execute against PostgreSQL database. Must not be null or empty.</param>
    /// <param name="results">
    /// Query results as <see cref="List{T}"/> of <see href="https://www.newtonsoft.com/json/help/html/T_Newtonsoft_Json_Linq_JObject.htm">JObject</see>.
    /// Each row is represented as a JObject with column names as properties.
    /// </param>
    /// <returns>Current <see cref="PostgreHelper"/> instance for method chaining.</returns>
    /// <remarks>
    /// Executes a SQL query and returns results in JSON format using JObject.
    /// Supports method chaining and automatic resource disposal.
    ///
    /// Features:
    /// - Synchronous execution
    /// - JSON formatted results
    /// - Null value handling
    /// - Method chaining
    /// - Automatic resource disposal
    ///
    /// Example Request:
    /// <code>
    /// {
    ///   "query": "SELECT id, name, email FROM users WHERE active = true",
    ///   "results": "List&lt;JObject&gt; output parameter"
    /// }
    /// </code>
    ///
    /// Example Usage:
    /// <example>
    /// <code>
    /// db.Connect()
    ///   .ExecuteQuery(
    ///     query: "SELECT * FROM Users",
    ///     results: out List&lt;JObject&gt; users
    ///   )
    ///   .Disconnect();
    ///
    /// users.ForEach(user => Console.WriteLine(user["Name"]));
    /// </code>
    /// </example>
    ///
    /// > [!IMPORTANT]
    /// > Ensure proper disposal of the PostgreHelper instance after use
    ///
    /// > [!NOTE]
    /// > For large result sets, consider using ExecuteReader instead
    ///
    /// > [!TIP]
    /// > Use column aliases in your SQL query to customize the JSON property names
    /// </remarks>
    /// <exception cref="ArgumentException">Thrown when query parameter is null or whitespace.</exception>
    /// <exception cref="ObjectDisposedException">Thrown when accessing a disposed PostgreHelper instance.</exception>
    /// <exception cref="Exception">Rethrows any exceptions that occur during execution with full stack trace.</exception>
    /// <seealso cref="NpgsqlCommand.ExecuteReader"/>
    /// <seealso cref="JObject"/>
    /// <seealso href="https://www.newtonsoft.com/json/help/html/T_Newtonsoft_Json_Linq_JObject.htm">JObject Documentation</seealso>
    /// <seealso href="https://www.npgsql.org/doc/api/Npgsql.NpgsqlCommand.html">Npgsql Documentation</seealso>
    public PostgreHelper ExecuteQuery(string query, out List<JObject> results)
    {
        try
        {
            ThrowIfDisposed();
            ArgumentException.ThrowIfNullOrWhiteSpace(query);

            results = [];
            using var command = CreateCommand(query: query);
            using var reader = command.ExecuteReader();

            while (reader.Read())
            {
                var row = new JObject();
                for (var i = 0; i < reader.FieldCount; i++)
                {
                    var name = reader.GetName(ordinal: i);
                    var value = reader.IsDBNull(ordinal: i) ? null : reader.GetValue(ordinal: i);
                    row[name] = value == null ? null : JToken.FromObject(o: value);
                }
                results.Add(item: row);
            }

            return this;
        }
        catch (Exception ex)
        {
            _logging?.LogError(exception: ex);
            throw;
        }
    }

    /// <summary>
    /// Executes SQL query returning results as JObject list asynchronously.
    /// </summary>
    /// <param name="query">SQL query text to execute against PostgreSQL database.</param>
    /// <param name="callback">
    /// Optional callback action to process the results. Takes <see cref="List{JObject}"/> as parameter.
    /// </param>
    /// <param name="cancellationToken">
    /// Token to cancel the asynchronous operation. See <see cref="CancellationToken"/>.
    /// </param>
    /// <returns>
    /// Tuple containing:
    /// - helper: Current <see cref="PostgreHelper"/> instance for method chaining
    /// - results: Query results as <see cref="List{JObject}"/>
    /// </returns>
    /// <remarks>
    /// Executes a SQL query asynchronously and returns results in JSON format using JObject.
    /// Supports cancellation, method chaining, and optional result processing via callback.
    ///
    /// Features:
    /// - Asynchronous execution
    /// - JSON formatted results
    /// - Null value handling
    /// - Cancellation support
    /// - Method chaining
    ///
    /// Example Request:
    /// <code>
    /// {
    ///   "query": "SELECT id, name, email FROM users WHERE active = true",
    ///   "callback": "Optional Action delegate to process results",
    ///   "cancellationToken": "Optional CancellationToken instance"
    /// }
    /// </code>
    ///
    /// Example Usage:
    /// <example>
    /// <code>
    /// var (db, users) = await db.Connect()
    ///   .ExecuteQueryAsync(
    ///     query: "SELECT * FROM Users",
    ///     callback: results => results.ForEach(user => Console.WriteLine(user["Name"])),
    ///     cancellationToken: new CancellationTokenSource().Token
    ///   );
    /// </code>
    /// </example>
    ///
    /// > [!IMPORTANT]
    /// > Ensure proper disposal of the PostgreHelper instance after use
    ///
    /// > [!NOTE]
    /// > The callback is executed before the method returns, allowing for immediate result processing
    ///
    /// > [!TIP]
    /// > For large result sets, consider using ExecuteReaderAsync instead
    /// </remarks>
    /// <exception cref="ArgumentException">Thrown when query parameter is null or whitespace.</exception>
    /// <exception cref="ObjectDisposedException">Thrown when accessing a disposed PostgreHelper instance.</exception>
    /// <exception cref="OperationCanceledException">Thrown when operation is canceled via cancellationToken.</exception>
    /// <exception cref="Exception">Rethrows any exceptions that occur during execution with full stack trace.</exception>
    /// <seealso cref="NpgsqlCommand.ExecuteReaderAsync"/>
    /// <seealso cref="NpgsqlDataReader.ReadAsync"/>
    /// <seealso cref="NpgsqlDataReader.IsDBNullAsync"/>
    /// <seealso href="https://www.newtonsoft.com/json/help/html/T_Newtonsoft_Json_Linq_JObject.htm">JObject Documentation</seealso>
    /// <seealso href="https://www.npgsql.org/doc/api/Npgsql.NpgsqlCommand.html">Npgsql Documentation</seealso>
    public async Task<(PostgreHelper helper, List<JObject> results)> ExecuteQueryAsync(
        string query,
        Action<List<JObject>>? callback = null,
        CancellationToken cancellationToken = default
    )
    {
        try
        {
            ThrowIfDisposed();
            ArgumentException.ThrowIfNullOrWhiteSpace(query);

            var results = new List<JObject>();
            using var command = CreateCommand(query: query);
            using var reader = await command.ExecuteReaderAsync(
                cancellationToken: cancellationToken
            );

            while (await reader.ReadAsync(cancellationToken: cancellationToken))
            {
                var row = new JObject();
                for (var i = 0; i < reader.FieldCount; i++)
                {
                    var name = reader.GetName(ordinal: i);
                    var value = await reader.IsDBNullAsync(
                        ordinal: i,
                        cancellationToken: cancellationToken
                    )
                        ? null
                        : reader.GetValue(ordinal: i);
                    row[name] = value == null ? null : JToken.FromObject(o: value);
                }
                results.Add(item: row);
            }

            callback?.Invoke(obj: results);

            return (this, results);
        }
        catch (Exception ex)
        {
            _logging?.LogError(exception: ex);
            throw;
        }
    }

    /// <summary>
    /// Executes SQL query and returns results in a DataTable format.
    /// </summary>
    /// <param name="query">SQL query text to be executed against the PostgreSQL database.</param>
    /// <param name="results">Query results as <see cref="System.Data.DataTable"/>.</param>
    /// <returns>Current <see cref="PostgreHelper"/> instance for method chaining.</returns>
    /// <remarks>
    /// Executes a SQL query and returns results in table format using DataTable.
    /// Supports method chaining and automatic resource disposal.
    ///
    /// Features:
    /// - Synchronous execution
    /// - Method chaining
    /// - Automatic resource disposal
    ///
    /// Example Request:
    /// <code>
    /// {
    ///   "query": "SELECT id, name, email FROM users WHERE active = true",
    ///   "results": "DataTable output parameter"
    /// }
    /// </code>
    ///
    /// Example Usage:
    /// <example>
    /// <code>
    /// db.Connect()
    ///   .ExecuteQuery(
    ///     query: "SELECT * FROM Users",
    ///     results: out DataTable users
    ///   )
    ///   .Disconnect();
    ///
    /// foreach (DataRow row in users.Rows)
    ///     Console.WriteLine(row["Name"]);
    /// </code>
    /// </example>
    ///
    /// > [!IMPORTANT]
    /// > Ensure proper disposal of the PostgreHelper instance after use
    ///
    /// > [!NOTE]
    /// > For large result sets, consider using ExecuteReader instead
    /// </remarks>
    /// <exception cref="ArgumentException">Thrown when query parameter is null or whitespace.</exception>
    /// <exception cref="ObjectDisposedException">Thrown when accessing a disposed PostgreHelper instance.</exception>
    /// <exception cref="Exception">Rethrows any exceptions that occur during execution with full stack trace.</exception>
    /// <seealso cref="NpgsqlCommand.ExecuteReader"/>
    /// <seealso cref="DataTable.Load"/>
    /// <seealso href="https://www.npgsql.org/doc/api/Npgsql.NpgsqlCommand.html">Npgsql Documentation</seealso>
    /// <seealso href="https://learn.microsoft.com/en-us/dotnet/api/system.data.datatable">DataTable Documentation</seealso>
    public PostgreHelper ExecuteQuery(string query, out DataTable results)
    {
        try
        {
            ThrowIfDisposed();
            ArgumentException.ThrowIfNullOrWhiteSpace(query);

            results = new();
            using var command = CreateCommand(query: query);
            using var reader = command.ExecuteReader();
            results.Load(reader: reader);

            return this;
        }
        catch (Exception ex)
        {
            _logging?.LogError(exception: ex);
            throw;
        }
    }

    /// <summary>
    /// Executes SQL query returning results as DataTable asynchronously.
    /// </summary>
    /// <param name="query">SQL query text to be executed against the PostgreSQL database.</param>
    /// <param name="callback">Optional callback action to process the DataTable results after query execution.</param>
    /// <param name="cancellationToken">Token to cancel the asynchronous operation. Default is CancellationToken.None.</param>
    /// <returns>
    /// A tuple containing:
    /// - helper: The current PostgreHelper instance for method chaining
    /// - results: Query results as <see cref="System.Data.DataTable"/>
    /// </returns>
    /// <remarks>
    /// Executes a SQL query asynchronously and returns results in table format using DataTable.
    /// Supports cancellation and optional result processing via callback.
    ///
    /// Features:
    /// - Asynchronous execution
    /// - Cancellation support
    /// - Method chaining
    /// - Optional result processing
    /// - Automatic resource disposal
    ///
    /// Example Request:
    /// <code>
    /// {
    ///   "query": "SELECT id, name, email FROM users WHERE active = true",
    ///   "cancellationToken": "CancellationToken instance",
    ///   "callback": "Action delegate to process results"
    /// }
    /// </code>
    ///
    /// Example Usage:
    /// <example>
    /// <code>
    /// var (db, users) = await db.Connect()
    ///   .ExecuteQueryAsync(
    ///     query: "SELECT * FROM Users",
    ///     callback: table => table.Rows.Cast&lt;DataRow&gt;().ToList().ForEach(row => Console.WriteLine(row["Name"])),
    ///     cancellationToken: token
    ///   );
    /// </code>
    /// </example>
    ///
    /// > [!IMPORTANT]
    /// > Ensure proper disposal of the PostgreHelper instance after use
    ///
    /// > [!NOTE]
    /// > The callback is executed before the method returns, allowing for immediate result processing
    /// </remarks>
    /// <exception cref="ArgumentException">Thrown when query parameter is null or whitespace.</exception>
    /// <exception cref="ObjectDisposedException">Thrown when accessing a disposed PostgreHelper instance.</exception>
    /// <exception cref="OperationCanceledException">Thrown when operation is canceled via cancellationToken.</exception>
    /// <exception cref="Exception">Rethrows any exceptions that occur during execution with full stack trace.</exception>
    /// <seealso cref="NpgsqlCommand.ExecuteReaderAsync"/>
    /// <seealso cref="DataTable.Load"/>
    /// <seealso href="https://www.npgsql.org/doc/api/Npgsql.NpgsqlCommand.html#Npgsql_NpgsqlCommand_ExecuteReaderAsync">Npgsql Documentation</seealso>
    /// <seealso href="https://learn.microsoft.com/en-us/dotnet/api/system.data.datatable">DataTable Documentation</seealso>
    public async Task<(PostgreHelper helper, DataTable results)> ExecuteQueryAsync(
        string query,
        Action<DataTable>? callback = null,
        CancellationToken cancellationToken = default
    )
    {
        try
        {
            ThrowIfDisposed();
            ArgumentException.ThrowIfNullOrWhiteSpace(argument: query);

            var results = new DataTable();
            using var command = CreateCommand(query: query);
            using var reader = await command.ExecuteReaderAsync(
                cancellationToken: cancellationToken
            );
            results.Load(reader: reader);

            callback?.Invoke(obj: results);

            return (this, results);
        }
        catch (Exception ex)
        {
            _logging?.LogError(exception: ex);
            throw;
        }
    }

    #region StoreProcedure Executor
    /// <summary>
    /// Creates a configured <see cref="NpgsqlCommand"/> for executing a stored procedure.
    /// </summary>
    /// <param name="spCallInfo">
    /// Information about the stored procedure call, including schema, procedure name, and parameters.
    /// </param>
    /// <returns>
    /// Configured <see cref="NpgsqlCommand"/> instance for stored procedure execution.
    /// </returns>
    /// <remarks>
    /// Internal method for creating and configuring a command object to execute a PostgreSQL stored procedure.
    /// Validates the connection and stored procedure call information before creating the command.
    ///
    /// Features:
    /// - Connection validation
    /// - Stored procedure name and schema configuration
    /// - Parameter binding with type and value
    /// - Error logging
    ///
    /// Example Request:
    /// <code>
    /// {
    ///   "spCallInfo": {
    ///     "Schema": "public",
    ///     "SPName": "my_procedure",
    ///     "Parameters": [
    ///       { "Name": "param1", "DataType": NpgsqlDbType.Integer, "Value": 42 }
    ///     ]
    ///   }
    /// }
    /// </code>
    ///
    /// Example Usage:
    /// <example>
    /// <code>
    /// var spInfo = new SPCallInfo
    /// {
    ///     Schema = "public",
    ///     SPName = "my_procedure",
    ///     Parameters = new[]
    ///     {
    ///         new SPParameter("param1", NpgsqlDbType.Integer, 42)
    ///     }
    /// };
    /// using var command = CreateCommand(spInfo);
    /// var result = command.ExecuteNonQuery();
    /// </code>
    /// </example>
    ///
    /// > [!IMPORTANT]
    /// > Command objects must be disposed after use
    ///
    /// > [!NOTE]
    /// > Connection must be valid before creating command
    ///
    /// > [!TIP]
    /// > Use parameters to prevent SQL injection
    ///
    /// > [!CAUTION]
    /// > Ensure parameter types match the stored procedure signature
    /// </remarks>
    /// <exception cref="ObjectDisposedException">
    /// Thrown when accessing a disposed <see cref="PostgreHelper"/> instance.
    /// </exception>
    /// <exception cref="DataException">
    /// Thrown when the database connection is invalid.
    /// </exception>
    /// <exception cref="Exception">
    /// Rethrows any exceptions that occur during command creation with full stack trace.
    /// </exception>
    /// <seealso cref="NpgsqlCommand"/>
    /// <seealso cref="CommandType.StoredProcedure"/>
    /// <seealso href="https://www.npgsql.org/doc/api/Npgsql.NpgsqlCommand.html">NpgsqlCommand Documentation</seealso>
    private NpgsqlCommand CreateCommand(SPCallInfo spCallInfo)
    {
        try
        {
            spCallInfo.EnsureModel();
            ThrowIfDisposed();

            if (!ConnectionIsValid(message: out var errorMessage))
                throw new DataException(s: errorMessage);

            var command = _connection.CreateCommand();
            command.CommandText = $"{spCallInfo.Schema}.{spCallInfo.SPName}";
            command.CommandType = CommandType.StoredProcedure;

            if (spCallInfo.Parameters is not null && spCallInfo.Parameters.Length > 0)
                foreach (var param in spCallInfo.Parameters)
                    command.Parameters.AddWithValue(
                        parameterName: param.Name,
                        parameterType: param.DataType,
                        value: param.Value ?? DBNull.Value
                    );

            return command;
        }
        catch (Exception ex)
        {
            _logging?.LogError(exception: ex);
            throw;
        }
    }

    /// <summary>
    /// Executes a stored procedure as a non-query command (such as INSERT, UPDATE, DELETE) and returns the number of affected rows.
    /// </summary>
    /// <param name="spCallInfo">
    /// Information about the stored procedure call, including schema, procedure name, and parameters.
    /// </param>
    /// <param name="affectedRows">
    /// Output parameter containing the number of rows affected by the stored procedure execution.
    /// </param>
    /// <returns>
    /// Current <see cref="PostgreHelper"/> instance for method chaining.
    /// </returns>
    /// <remarks>
    /// Executes a PostgreSQL stored procedure that does not return a result set, and provides the number of affected rows.
    /// Supports method chaining and automatic resource disposal.
    ///
    /// Features:
    /// - Synchronous execution of stored procedures
    /// - Returns affected rows count
    /// - Method chaining support
    /// - Automatic resource disposal
    /// - Error logging
    ///
    /// Example Usage:
    /// <example>
    /// <code>
    /// var spInfo = new SPCallInfo
    /// {
    ///     Schema = "public",
    ///     SPName = "update_user_status",
    ///     Parameters = new[]
    ///     {
    ///         new SPParameter("user_id", NpgsqlDbType.Integer, 1),
    ///         new SPParameter("is_active", NpgsqlDbType.Boolean, true)
    ///     }
    /// };
    /// db.ExecuteNonQuery(spInfo, out int? rows);
    /// Console.WriteLine($"Rows affected: {rows}");
    /// </code>
    /// </example>
    ///
    /// > [!IMPORTANT]
    /// > Ensure the stored procedure and parameters are correctly defined in <paramref name="spCallInfo"/>.
    ///
    /// > [!NOTE]
    /// > For asynchronous execution, use <see cref="ExecuteNonQueryAsync(SPCallInfo, Action{int?}?, CancellationToken)"/>.
    ///
    /// > [!TIP]
    /// > Use this method for procedures that modify data but do not return result sets.
    ///
    /// > [!CAUTION]
    /// > Ensure proper transaction handling if the procedure modifies multiple tables.
    /// </remarks>
    /// <exception cref="ObjectDisposedException">
    /// Thrown when accessing a disposed <see cref="PostgreHelper"/> instance.
    /// </exception>
    /// <exception cref="DataException">
    /// Thrown when the database connection is invalid.
    /// </exception>
    /// <exception cref="PostgresException">
    /// Thrown when a PostgreSQL-specific error occurs during execution.
    /// </exception>
    /// <exception cref="Exception">
    /// Rethrows any exceptions that occur during execution with full stack trace.
    /// </exception>
    /// <seealso cref="NpgsqlCommand.ExecuteNonQuery"/>
    /// <seealso href="https://www.npgsql.org/doc/api/Npgsql.NpgsqlCommand.html#Npgsql_NpgsqlCommand_ExecuteNonQuery">
    /// Npgsql ExecuteNonQuery Documentation
    /// </seealso>
    public PostgreHelper ExecuteNonQuery(SPCallInfo spCallInfo, out int? affectedRows)
    {
        try
        {
            spCallInfo.EnsureModel();
            ThrowIfDisposed();

            using var command = CreateCommand(spCallInfo: spCallInfo);
            affectedRows = command.ExecuteNonQuery();

            return this;
        }
        catch (Exception ex)
        {
            _logging?.LogError(exception: ex);
            throw;
        }
    }

    /// <summary>
    /// Executes a stored procedure as a non-query command asynchronously and returns the number of affected rows.
    /// </summary>
    /// <param name="spCallInfo">
    /// Information about the stored procedure call, including schema, procedure name, and parameters.
    /// </param>
    /// <param name="callback">
    /// Optional <see cref="Action{T}"/> delegate to process the affected rows count as <see cref="int?"/>.
    /// </param>
    /// <param name="cancellationToken">
    /// Optional <see cref="CancellationToken"/> to cancel the asynchronous operation. Defaults to <see cref="CancellationToken.None"/>.
    /// </param>
    /// <returns>
    /// A <see cref="Task{TResult}"/> containing a tuple with:
    /// - helper: The current <see cref="PostgreHelper"/> instance for method chaining
    /// - affectedRows: The number of rows affected by the stored procedure execution as <see cref="int?"/>
    /// </returns>
    /// <remarks>
    /// Executes a PostgreSQL stored procedure asynchronously that does not return a result set, and provides the number of affected rows.
    /// Supports cancellation, method chaining, and optional result processing via callback.
    ///
    /// Features:
    /// - Asynchronous execution of stored procedures
    /// - Returns affected rows count
    /// - Cancellation support
    /// - Method chaining support
    /// - Optional callback processing
    /// - Automatic resource disposal
    /// - Error logging
    ///
    /// Example Usage:
    /// <example>
    /// <code>
    /// var spInfo = new SPCallInfo
    /// {
    ///     Schema = "public",
    ///     SPName = "update_user_status",
    ///     Parameters = new[]
    ///     {
    ///         new SPParameter("user_id", NpgsqlDbType.Integer, 1),
    ///         new SPParameter("is_active", NpgsqlDbType.Boolean, true)
    ///     }
    /// };
    /// var (db, rows) = await db.ExecuteNonQueryAsync(
    ///     spInfo,
    ///     callback: count => Console.WriteLine($"Rows affected: {count}")
    /// );
    /// </code>
    /// </example>
    ///
    /// > [!IMPORTANT]
    /// > Ensure the stored procedure and parameters are correctly defined in <paramref name="spCallInfo"/>.
    ///
    /// > [!NOTE]
    /// > The callback is executed before the method returns, allowing for immediate result processing.
    ///
    /// > [!TIP]
    /// > Use this method for procedures that modify data but do not return result sets.
    ///
    /// > [!CAUTION]
    /// > Ensure proper transaction handling if the procedure modifies multiple tables.
    /// </remarks>
    /// <exception cref="ObjectDisposedException">
    /// Thrown when accessing a disposed <see cref="PostgreHelper"/> instance.
    /// </exception>
    /// <exception cref="DataException">
    /// Thrown when the database connection is invalid.
    /// </exception>
    /// <exception cref="OperationCanceledException">
    /// Thrown when the operation is canceled via <paramref name="cancellationToken"/>.
    /// </exception>
    /// <exception cref="PostgresException">
    /// Thrown when a PostgreSQL-specific error occurs during execution.
    /// </exception>
    /// <exception cref="Exception">
    /// Rethrows any exceptions that occur during execution with full stack trace.
    /// </exception>
    /// <seealso cref="NpgsqlCommand.ExecuteNonQueryAsync"/>
    /// <seealso href="https://www.npgsql.org/doc/api/Npgsql.NpgsqlCommand.html#Npgsql_NpgsqlCommand_ExecuteNonQueryAsync">
    /// Npgsql ExecuteNonQueryAsync Documentation
    /// </seealso>
    public async Task<(PostgreHelper helper, int? affectedRows)> ExecuteNonQueryAsync(
        SPCallInfo spCallInfo,
        Action<int?>? callback = null,
        CancellationToken cancellationToken = default
    )
    {
        try
        {
            spCallInfo.EnsureModel();
            ThrowIfDisposed();

            using var command = CreateCommand(spCallInfo: spCallInfo);
            var affectedRows = await command.ExecuteNonQueryAsync(
                cancellationToken: cancellationToken
            );

            callback?.Invoke(affectedRows);

            return (helper: this, affectedRows);
        }
        catch (Exception ex)
        {
            _logging?.LogError(exception: ex);
            throw;
        }
    }

    /// <summary>
    /// Executes a stored procedure and returns a scalar value (the first column of the first row).
    /// </summary>
    /// <param name="sPCallInfo">
    /// Information about the stored procedure call, including schema, procedure name, and parameters.
    /// </param>
    /// <returns>
    /// Current <see cref="PostgreHelper"/> instance for method chaining.
    /// </returns>
    /// <remarks>
    /// Executes a PostgreSQL stored procedure and retrieves a single scalar value from the result.
    /// This is typically used for aggregate functions or procedures that return a single value.
    /// Supports method chaining and automatic resource disposal.
    ///
    /// Features:
    /// - Synchronous execution of stored procedures
    /// - Returns scalar value (first column of first row)
    /// - Method chaining support
    /// - Automatic resource disposal
    /// - Error logging
    ///
    /// Example Usage:
    /// <example>
    /// <code>
    /// var spInfo = new SPCallInfo
    /// {
    ///     Schema = "public",
    ///     SPName = "get_user_count",
    ///     Parameters = Array.Empty&lt;SPParameter&gt;()
    /// };
    /// db.ExecuteScalar(spInfo);
    /// </code>
    /// </example>
    ///
    /// > [!IMPORTANT]
    /// > Ensure the stored procedure and parameters are correctly defined in <paramref name="sPCallInfo"/>.
    ///
    /// > [!NOTE]
    /// > For asynchronous execution, use <see cref="ExecuteScalarAsync(SPCallInfo, Action{object?}?, CancellationToken)"/>.
    ///
    /// > [!TIP]
    /// > Use this method for procedures that return a single value.
    ///
    /// > [!CAUTION]
    /// > Ensure proper transaction handling if the procedure modifies data.
    /// </remarks>
    /// <exception cref="ObjectDisposedException">
    /// Thrown when accessing a disposed <see cref="PostgreHelper"/> instance.
    /// </exception>
    /// <exception cref="DataException">
    /// Thrown when the database connection is invalid.
    /// </exception>
    /// <exception cref="PostgresException">
    /// Thrown when a PostgreSQL-specific error occurs during execution.
    /// </exception>
    /// <exception cref="Exception">
    /// Rethrows any exceptions that occur during execution with full stack trace.
    /// </exception>
    /// <seealso cref="NpgsqlCommand.ExecuteScalar"/>
    /// <seealso href="https://www.npgsql.org/doc/api/Npgsql.NpgsqlCommand.html#Npgsql_NpgsqlCommand_ExecuteScalar">
    /// Npgsql ExecuteScalar Documentation
    /// </seealso>
    public PostgreHelper ExecuteScalar(SPCallInfo sPCallInfo)
    {
        try
        {
            sPCallInfo.EnsureModel();
            ThrowIfDisposed();

            using var command = CreateCommand(spCallInfo: sPCallInfo);
            var result = command.ExecuteScalar();

            return this;
        }
        catch (Exception ex)
        {
            _logging?.LogError(exception: ex);
            throw;
        }
    }

    /// <summary>
    /// Executes a stored procedure asynchronously and returns a scalar value (the first column of the first row).
    /// </summary>
    /// <param name="spCallInfo">
    /// Information about the stored procedure call, including schema, procedure name, and parameters.
    /// </param>
    /// <param name="callback">
    /// Optional <see cref="Action{T}"/> delegate to process the scalar result as <see cref="object?"/>.
    /// </param>
    /// <param name="cancellationToken">
    /// Optional <see cref="CancellationToken"/> to cancel the asynchronous operation. Defaults to <see cref="CancellationToken.None"/>.
    /// </param>
    /// <returns>
    /// A <see cref="Task{TResult}"/> containing a tuple with:
    /// - helper: The current <see cref="PostgreHelper"/> instance for method chaining
    /// - result: The scalar value returned by the stored procedure as <see cref="object?"/>
    /// </returns>
    /// <remarks>
    /// Executes a PostgreSQL stored procedure asynchronously and retrieves a single scalar value from the result.
    /// This is typically used for aggregate functions or procedures that return a single value.
    /// Supports cancellation, method chaining, and optional result processing via callback.
    ///
    /// Features:
    /// - Asynchronous execution of stored procedures
    /// - Returns scalar value (first column of first row)
    /// - Cancellation support
    /// - Method chaining support
    /// - Optional callback processing
    /// - Automatic resource disposal
    /// - Error logging
    ///
    /// Example Usage:
    /// <example>
    /// <code>
    /// var spInfo = new SPCallInfo
    /// {
    ///     Schema = "public",
    ///     SPName = "get_user_count",
    ///     Parameters = Array.Empty&lt;SPParameter&gt;()
    /// };
    /// var (db, count) = await db.ExecuteScalarAsync(
    ///     spInfo,
    ///     callback: result => Console.WriteLine($"User count: {result}")
    /// );
    /// </code>
    /// </example>
    ///
    /// > [!IMPORTANT]
    /// > Ensure the stored procedure and parameters are correctly defined in <paramref name="spCallInfo"/>.
    ///
    /// > [!NOTE]
    /// > The callback is executed before the method returns, allowing for immediate result processing.
    ///
    /// > [!TIP]
    /// > Use this method for procedures that return a single value.
    ///
    /// > [!CAUTION]
    /// > Ensure proper transaction handling if the procedure modifies data.
    /// </remarks>
    /// <exception cref="ObjectDisposedException">
    /// Thrown when accessing a disposed <see cref="PostgreHelper"/> instance.
    /// </exception>
    /// <exception cref="DataException">
    /// Thrown when the database connection is invalid.
    /// </exception>
    /// <exception cref="OperationCanceledException">
    /// Thrown when the operation is canceled via <paramref name="cancellationToken"/>.
    /// </exception>
    /// <exception cref="PostgresException">
    /// Thrown when a PostgreSQL-specific error occurs during execution.
    /// </exception>
    /// <exception cref="Exception">
    /// Rethrows any exceptions that occur during execution with full stack trace.
    /// </exception>
    /// <seealso cref="NpgsqlCommand.ExecuteScalarAsync"/>
    /// <seealso href="https://www.npgsql.org/doc/api/Npgsql.NpgsqlCommand.html#Npgsql_NpgsqlCommand_ExecuteScalarAsync">
    /// Npgsql ExecuteScalarAsync Documentation
    /// </seealso>
    public async Task<(PostgreHelper helper, object? result)> ExecuteScalarAsync(
        SPCallInfo spCallInfo,
        Action<object?>? callback = null,
        CancellationToken cancellationToken = default
    )
    {
        try
        {
            spCallInfo.EnsureModel();
            ThrowIfDisposed();

            using var command = CreateCommand(spCallInfo: spCallInfo);
            var result = await command.ExecuteScalarAsync(cancellationToken: cancellationToken);

            callback?.Invoke(obj: result);

            return (helper: this, result);
        }
        catch (Exception ex)
        {
            _logging?.LogError(exception: ex);
            throw;
        }
    }

    /// <summary>
    /// Executes a stored procedure and returns a data reader for row-by-row access to the result set.
    /// </summary>
    /// <param name="spCallInfo">
    /// Information about the stored procedure call, including schema, procedure name, and parameters.
    /// </param>
    /// <param name="reader">
    /// Output parameter containing the <see cref="NpgsqlDataReader"/> for reading the result set.
    /// </param>
    /// <returns>
    /// Current <see cref="PostgreHelper"/> instance for method chaining.
    /// </returns>
    /// <remarks>
    /// Executes a PostgreSQL stored procedure and provides direct access to the results via <see cref="NpgsqlDataReader"/>.
    /// Supports method chaining and manual result iteration.
    ///
    /// Features:
    /// - Synchronous execution of stored procedures
    /// - Row-by-row data access
    /// - Method chaining support
    /// - Manual iteration control
    /// - Type-safe column access
    /// - Error logging
    ///
    /// Example Usage:
    /// <example>
    /// <code>
    /// var spInfo = new SPCallInfo
    /// {
    ///     Schema = "public",
    ///     SPName = "get_active_users",
    ///     Parameters = Array.Empty&lt;SPParameter&gt;()
    /// };
    /// db.ExecuteReader(spInfo, out var reader);
    /// while (reader.Read())
    /// {
    ///     var id = reader.GetInt32(reader.GetOrdinal("id"));
    ///     var name = reader.GetString(reader.GetOrdinal("name"));
    ///     Console.WriteLine($"ID: {id}, Name: {name}");
    /// }
    /// reader.Close();
    /// </code>
    /// </example>
    ///
    /// > [!IMPORTANT]
    /// > Ensure proper disposal of both the <see cref="PostgreHelper"/> instance and the <see cref="NpgsqlDataReader"/> after use.
    ///
    /// > [!NOTE]
    /// > For asynchronous execution, use <see cref="ExecuteReaderAsync(SPCallInfo, Action{NpgsqlDataReader}?, CancellationToken)"/>.
    ///
    /// > [!TIP]
    /// > Use strongly-typed Get methods for type-safe column access.
    /// </remarks>
    /// <exception cref="ObjectDisposedException">
    /// Thrown when accessing a disposed <see cref="PostgreHelper"/> instance.
    /// </exception>
    /// <exception cref="DataException">
    /// Thrown when the database connection is invalid.
    /// </exception>
    /// <exception cref="PostgresException">
    /// Thrown when a PostgreSQL-specific error occurs during execution.
    /// </exception>
    /// <exception cref="Exception">
    /// Rethrows any exceptions that occur during execution with full stack trace.
    /// </exception>
    /// <seealso cref="NpgsqlCommand.ExecuteReader"/>
    /// <seealso cref="NpgsqlDataReader"/>
    /// <seealso href="https://www.npgsql.org/doc/api/Npgsql.NpgsqlDataReader.html">NpgsqlDataReader Documentation</seealso>
    /// <seealso href="https://www.npgsql.org/doc/api/Npgsql.NpgsqlCommand.html">Npgsql Documentation</seealso>
    public PostgreHelper ExecuteReader(SPCallInfo spCallInfo, out NpgsqlDataReader reader)
    {
        try
        {
            spCallInfo.EnsureModel();
            ThrowIfDisposed();

            using var command = CreateCommand(spCallInfo: spCallInfo);
            reader = command.ExecuteReader();

            return this;
        }
        catch (Exception ex)
        {
            _logging?.LogError(exception: ex);
            throw;
        }
    }

    /// <summary>
    /// Executes a stored procedure asynchronously and returns a data reader for row-by-row access to the result set.
    /// </summary>
    /// <param name="spCallInfo">
    /// Information about the stored procedure call, including schema, procedure name, and parameters.
    /// </param>
    /// <param name="callback">
    /// Optional <see cref="Action{T}"/> delegate to process the <see cref="NpgsqlDataReader"/> after execution.
    /// </param>
    /// <param name="cancellationToken">
    /// Optional <see cref="CancellationToken"/> to cancel the asynchronous operation. Defaults to <see cref="CancellationToken.None"/>.
    /// </param>
    /// <returns>
    /// A <see cref="Task{TResult}"/> containing a tuple with:
    /// - helper: The current <see cref="PostgreHelper"/> instance for method chaining
    /// - reader: The <see cref="NpgsqlDataReader"/> for reading the result set
    /// </returns>
    /// <remarks>
    /// Executes a PostgreSQL stored procedure asynchronously and provides direct access to the results via <see cref="NpgsqlDataReader"/>.
    /// Supports cancellation, method chaining, and optional result processing via callback.
    ///
    /// Features:
    /// - Asynchronous execution of stored procedures
    /// - Row-by-row data access
    /// - Cancellation support
    /// - Method chaining support
    /// - Optional callback processing
    /// - Error logging
    ///
    /// Example Usage:
    /// <example>
    /// <code>
    /// var spInfo = new SPCallInfo
    /// {
    ///     Schema = "public",
    ///     SPName = "get_active_users",
    ///     Parameters = Array.Empty&lt;SPParameter&gt;()
    /// };
    /// var (db, reader) = await db.ExecuteReaderAsync(
    ///     spInfo,
    ///     callback: r =>
    ///     {
    ///         while (r.Read())
    ///             Console.WriteLine(r["name"]);
    ///     }
    /// );
    /// </code>
    /// </example>
    ///
    /// > [!IMPORTANT]
    /// > Ensure proper disposal of both the <see cref="PostgreHelper"/> instance and the <see cref="NpgsqlDataReader"/> after use.
    ///
    /// > [!NOTE]
    /// > The callback is executed before the method returns, allowing for immediate result processing.
    ///
    /// > [!TIP]
    /// > Use strongly-typed Get methods for type-safe column access.
    /// </remarks>
    /// <exception cref="ObjectDisposedException">
    /// Thrown when accessing a disposed <see cref="PostgreHelper"/> instance.
    /// </exception>
    /// <exception cref="DataException">
    /// Thrown when the database connection is invalid.
    /// </exception>
    /// <exception cref="OperationCanceledException">
    /// Thrown when the operation is canceled via <paramref name="cancellationToken"/>.
    /// </exception>
    /// <exception cref="PostgresException">
    /// Thrown when a PostgreSQL-specific error occurs during execution.
    /// </exception>
    /// <exception cref="Exception">
    /// Rethrows any exceptions that occur during execution with full stack trace.
    /// </exception>
    /// <seealso cref="NpgsqlCommand.ExecuteReaderAsync"/>
    /// <seealso cref="NpgsqlDataReader"/>
    /// <seealso href="https://www.npgsql.org/doc/api/Npgsql.NpgsqlDataReader.html">NpgsqlDataReader Documentation</seealso>
    /// <seealso href="https://www.npgsql.org/doc/api/Npgsql.NpgsqlCommand.html">Npgsql Documentation</seealso>
    public async Task<(PostgreHelper helper, NpgsqlDataReader reader)> ExecuteReaderAsync(
        SPCallInfo spCallInfo,
        Action<NpgsqlDataReader>? callback = null,
        CancellationToken cancellationToken = default
    )
    {
        try
        {
            spCallInfo.EnsureModel();
            ThrowIfDisposed();

            using var command = CreateCommand(spCallInfo: spCallInfo);
            var reader = await command.ExecuteReaderAsync(cancellationToken: cancellationToken);

            callback?.Invoke(obj: reader);

            return (helper: this, reader);
        }
        catch (Exception ex)
        {
            _logging?.LogError(exception: ex);
            throw;
        }
    }

    /// <summary>
    /// Executes a stored procedure and returns a list of JObjects representing the result set.
    /// </summary>
    /// <param name="spCallInfo">
    /// Information about the stored procedure call, including schema, procedure name, and parameters.
    /// </param>
    /// <param name="results">
    /// Output parameter containing a list of <see cref="Newtonsoft.Json.Linq.JObject"/>,
    /// where each JObject represents a row from the result set.
    /// </param>
    /// <returns>
    /// Current <see cref="PostgreHelper"/> instance for method chaining.
    /// </returns>
    /// <remarks>
    /// Executes a PostgreSQL stored procedure and retrieves the results as a list of <see cref="Newtonsoft.Json.Linq.JObject"/>.
    /// Each JObject represents a row, with properties corresponding to the column names and values.
    /// Supports method chaining and automatic resource disposal.
    ///
    /// Features:
    /// - Synchronous execution of stored procedures
    /// - Returns results as a list of <see cref="Newtonsoft.Json.Linq.JObject"/>
    /// - Method chaining support
    /// - Automatic resource disposal
    /// - Error logging
    ///
    /// Example Usage:
    /// <example>
    /// <code>
    /// var spInfo = new SPCallInfo
    /// {
    ///     Schema = "public",
    ///     SPName = "get_active_users",
    ///     Parameters = Array.Empty&lt;SPParameter&gt;()
    /// };
    /// db.ExecuteQuery(spInfo, out var users);
    /// foreach (var user in users)
    /// {
    ///     Console.WriteLine($"ID: {user["id"]}, Name: {user["name"]}");
    /// }
    /// </code>
    /// </example>
    ///
    /// > [!IMPORTANT]
    /// > Ensure proper disposal of the <see cref="PostgreHelper"/> instance after use.
    ///
    /// > [!NOTE]
    /// > For asynchronous execution, use <see cref="ExecuteQueryAsync(SPCallInfo, Action{List{JObject?}}?, CancellationToken)"/>.
    ///
    /// > [!TIP]
    /// > Use this method when you need to process the result set as JSON objects.
    /// </remarks>
    /// <exception cref="ObjectDisposedException">
    /// Thrown when accessing a disposed <see cref="PostgreHelper"/> instance.
    /// </exception>
    /// <exception cref="DataException">
    /// Thrown when the database connection is invalid.
    /// </exception>
    /// <exception cref="PostgresException">
    /// Thrown when a PostgreSQL-specific error occurs during execution.
    /// </exception>
    /// <exception cref="Exception">
    /// Rethrows any exceptions that occur during execution with full stack trace.
    /// </exception>
    /// <seealso cref="NpgsqlCommand.ExecuteReader"/>
    /// <seealso href="https://www.newtonsoft.com/json/help/html/T_Newtonsoft_Json_Linq_JObject.htm">JObject Documentation</seealso>
    /// <seealso href="https://www.npgsql.org/doc/api/Npgsql.NpgsqlCommand.html">Npgsql Documentation</seealso>
    public PostgreHelper ExecuteQuery(SPCallInfo spCallInfo, out List<JObject?>? results)
    {
        try
        {
            spCallInfo.EnsureModel();
            ThrowIfDisposed();

            results = [];
            using var command = CreateCommand(spCallInfo: spCallInfo);
            using var reader = command.ExecuteReader();

            while (reader.Read())
            {
                var row = new JObject();
                for (var i = 0; i < reader.FieldCount; i++)
                {
                    var name = reader.GetName(ordinal: i);
                    var value = reader.IsDBNull(ordinal: i) ? null : reader.GetValue(ordinal: i);
                    row[name] = value == null ? null : JToken.FromObject(o: value);
                }
                results.Add(item: row);
            }

            return this;
        }
        catch (Exception ex)
        {
            _logging?.LogError(exception: ex);
            throw;
        }
    }

    /// <summary>
    /// Executes a stored procedure asynchronously and returns a list of JObjects representing the result set.
    /// </summary>
    /// <param name="spCallInfo">
    /// Information about the stored procedure call, including schema, procedure name, and parameters.
    /// </param>
    /// <param name="callback">
    /// Optional <see cref="Action{T}"/> delegate to process the list of <see cref="Newtonsoft.Json.Linq.JObject"/>
    /// after execution.
    /// </param>
    /// <param name="cancellationToken">
    /// Optional <see cref="CancellationToken"/> to cancel the asynchronous operation. Defaults to <see cref="CancellationToken.None"/>.
    /// </param>
    /// <returns>
    /// A <see cref="Task{TResult}"/> containing a tuple with:
    /// - helper: The current <see cref="PostgreHelper"/> instance for method chaining
    /// - results: A list of <see cref="Newtonsoft.Json.Linq.JObject"/>, where each JObject represents a row from the result set.
    /// </returns>
    /// <remarks>
    /// Executes a PostgreSQL stored procedure asynchronously and retrieves the results as a list of <see cref="Newtonsoft.Json.Linq.JObject"/>.
    /// Each JObject represents a row, with properties corresponding to the column names and values.
    /// Supports cancellation, method chaining, and optional result processing via callback.
    ///
    /// Features:
    /// - Asynchronous execution of stored procedures
    /// - Returns results as a list of <see cref="Newtonsoft.Json.Linq.JObject"/>
    /// - Cancellation support
    /// - Method chaining support
    /// - Optional callback processing
    /// - Error logging
    ///
    /// Example Usage:
    /// <example>
    /// <code>
    /// var spInfo = new SPCallInfo
    /// {
    ///     Schema = "public",
    ///     SPName = "get_active_users",
    ///     Parameters = Array.Empty&lt;SPParameter&gt;()
    /// };
    /// var (db, users) = await db.ExecuteQueryAsync(
    ///     spInfo,
    ///     callback: users =>
    ///     {
    ///         foreach (var user in users)
    ///         {
    ///             Console.WriteLine($"ID: {user["id"]}, Name: {user["name"]}");
    ///         }
    ///     }
    /// );
    /// </code>
    /// </example>
    ///
    /// > [!IMPORTANT]
    /// > Ensure proper disposal of the <see cref="PostgreHelper"/> instance after use.
    ///
    /// > [!NOTE]
    /// > The callback is executed before the method returns, allowing for immediate result processing.
    ///
    /// > [!TIP]
    /// > Use this method when you need to process the result set as JSON objects asynchronously.
    /// </remarks>
    /// <exception cref="ObjectDisposedException">
    /// Thrown when accessing a disposed <see cref="PostgreHelper"/> instance.
    /// </exception>
    /// <exception cref="DataException">
    /// Thrown when the database connection is invalid.
    /// </exception>
    /// <exception cref="OperationCanceledException">
    /// Thrown when the operation is canceled via <paramref name="cancellationToken"/>.
    /// </exception>
    /// <exception cref="PostgresException">
    /// Thrown when a PostgreSQL-specific error occurs during execution.
    /// </exception>
    /// <exception cref="Exception">
    /// Rethrows any exceptions that occur during execution with full stack trace.
    /// </exception>
    /// <seealso cref="NpgsqlCommand.ExecuteReaderAsync"/>
    /// <seealso href="https://www.newtonsoft.com/json/help/html/T_Newtonsoft_Json_Linq_JObject.htm">JObject Documentation</seealso>
    /// <seealso href="https://www.npgsql.org/doc/api/Npgsql.NpgsqlCommand.html">Npgsql Documentation</seealso>
    public async Task<(PostgreHelper helper, List<JObject?>? results)> ExecuteQueryAsync(
        SPCallInfo spCallInfo,
        Action<List<JObject?>?>? callback = null,
        CancellationToken cancellationToken = default
    )
    {
        try
        {
            spCallInfo.EnsureModel();
            ThrowIfDisposed();

            var results = new List<JObject?>();
            using var command = CreateCommand(spCallInfo: spCallInfo);
            using var reader = await command.ExecuteReaderAsync(
                cancellationToken: cancellationToken
            );

            while (await reader.ReadAsync(cancellationToken: cancellationToken))
            {
                var row = new JObject();
                for (var i = 0; i < reader.FieldCount; i++)
                {
                    var name = reader.GetName(ordinal: i);
                    var value = await reader.IsDBNullAsync(
                        ordinal: i,
                        cancellationToken: cancellationToken
                    )
                        ? null
                        : reader.GetValue(ordinal: i);
                    row[name] = value == null ? null : JToken.FromObject(o: value);
                }
                results.Add(item: row);
            }

            callback?.Invoke(obj: results);

            return (this, results);
        }
        catch (Exception ex)
        {
            _logging?.LogError(exception: ex);
            throw;
        }
    }
    #endregion StoreProcedure Executor
}

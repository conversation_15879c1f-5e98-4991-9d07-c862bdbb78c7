using System.Text.RegularExpressions;

namespace IDC.Utilities;

/// <summary>
/// Provides a collection of commonly used regular expression patterns for various validation scenarios.
/// </summary>
/// <remarks>
/// This class contains pre-compiled regular expressions for validating:
/// - Email addresses
/// - Numbers (integers, decimals, currency, percentages)
/// - Dates and times
/// - Web-related strings (URLs, IP addresses, domains)
/// - HTML tags
/// - Security-related patterns (passwords, JWT tokens)
/// - Base64 strings
/// - File paths
///
/// Example usage:
/// <code>
/// // Validate email
/// bool isValidEmail = RegexPatternCollections.EmailValidation().IsMatch("<EMAIL>");
///
/// // Validate strong password
/// bool isStrongPassword = RegexPatternCollections.StrongPasswordValidation().IsMatch("P@ssw0rd123");
/// </code>
/// </remarks>
/// <seealso href="https://learn.microsoft.com/en-us/dotnet/api/system.text.regularexpressions.regex">Regex Class</seealso>
/// <seealso href="https://learn.microsoft.com/en-us/dotnet/standard/base-types/regular-expressions">Regular Expression Language</seealso>
public static partial class RegexPatternCollections
{
    #region Email Validation
    /// <summary>
    /// Validates email addresses using a compiled regular expression pattern.
    /// </summary>
    /// <remarks>
    /// The pattern validates email addresses with the following rules:
    /// - Local part can contain letters, numbers, and special characters (._%+-)
    /// - Domain part can contain letters, numbers, dots, and hyphens
    /// - TLD must be at least 2 characters long
    ///
    /// > [!NOTE]
    /// > This validation follows RFC 5322 standards but does not cover all edge cases.
    ///
    /// Example usage:
    /// <code>
    /// var regex = RegexPatternCollections.EmailValidation();
    ///
    /// // Valid emails
    /// bool isValid1 = regex.IsMatch("<EMAIL>");         // true
    /// bool isValid2 = regex.IsMatch("<EMAIL>");  // true
    ///
    /// // Invalid emails
    /// bool isValid3 = regex.IsMatch("invalid.email");            // false
    /// bool isValid4 = regex.IsMatch("user@domain");              // false
    /// </code>
    /// </remarks>
    /// <returns>A compiled <see href="https://learn.microsoft.com/en-us/dotnet/api/system.text.regularexpressions.regex">Regex</see> instance for email validation.</returns>
    [GeneratedRegex(
        pattern: @"^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$",
        options: RegexOptions.Compiled
    )]
    public static partial Regex EmailValidation();
    #endregion

    #region Number Validation
    /// <summary>
    /// Validates integer numbers using a compiled regular expression pattern.
    /// </summary>
    /// <remarks>
    /// Validates whole numbers (positive or negative) without decimal points.
    ///
    /// Example usage:
    /// <code>
    /// var regex = RegexPatternCollections.IntegerValidation();
    ///
    /// // Valid integers
    /// bool isValid1 = regex.IsMatch("123");     // true
    /// bool isValid2 = regex.IsMatch("-456");    // true
    ///
    /// // Invalid integers
    /// bool isValid3 = regex.IsMatch("12.34");   // false
    /// bool isValid4 = regex.IsMatch("abc");     // false
    /// </code>
    /// </remarks>
    /// <returns>A compiled <see href="https://learn.microsoft.com/en-us/dotnet/api/system.text.regularexpressions.regex">Regex</see> instance for integer validation.</returns>
    [GeneratedRegex(pattern: @"^-?\d+$", options: RegexOptions.Compiled)]
    public static partial Regex IntegerValidation();

    /// <summary>
    /// Validates decimal numbers using a compiled regular expression pattern.
    /// </summary>
    /// <remarks>
    /// Validates numbers that may include decimal points.
    ///
    /// Example usage:
    /// <code>
    /// var regex = RegexPatternCollections.DecimalValidation();
    ///
    /// // Valid decimals
    /// bool isValid1 = regex.IsMatch("123.45");    // true
    /// bool isValid2 = regex.IsMatch("-67.89");    // true
    /// bool isValid3 = regex.IsMatch("42");        // true
    ///
    /// // Invalid decimals
    /// bool isValid4 = regex.IsMatch("abc");       // false
    /// bool isValid5 = regex.IsMatch("12..34");    // false
    /// </code>
    /// </remarks>
    /// <returns>A compiled <see href="https://learn.microsoft.com/en-us/dotnet/api/system.text.regularexpressions.regex">Regex</see> instance for decimal validation.</returns>
    [GeneratedRegex(pattern: @"^-?\d*\.?\d+$", options: RegexOptions.Compiled)]
    public static partial Regex DecimalValidation();

    /// <summary>
    /// Validates currency format using a compiled regular expression pattern.
    /// </summary>
    /// <remarks>
    /// Validates currency values with optional dollar sign, thousands separators, and exactly two decimal places.
    ///
    /// > [!NOTE]
    /// > This validation follows US currency format standards.
    ///
    /// Example usage:
    /// <code>
    /// var regex = RegexPatternCollections.CurrencyValidation();
    ///
    /// // Valid currency
    /// bool isValid1 = regex.IsMatch("$1,234.56");    // true
    /// bool isValid2 = regex.IsMatch("1234.56");      // true
    /// bool isValid3 = regex.IsMatch("$100");         // true
    ///
    /// // Invalid currency
    /// bool isValid4 = regex.IsMatch("$1.234,56");    // false
    /// bool isValid5 = regex.IsMatch("1,234.567");    // false
    /// </code>
    /// </remarks>
    /// <returns>A compiled <see href="https://learn.microsoft.com/en-us/dotnet/api/system.text.regularexpressions.regex">Regex</see> instance for currency validation.</returns>
    [GeneratedRegex(pattern: @"^\$?\d{1,3}(,\d{3})*(\.\d{2})?$", options: RegexOptions.Compiled)]
    public static partial Regex CurrencyValidation();

    /// <summary>
    /// Validates percentage values using a compiled regular expression pattern.
    /// </summary>
    /// <remarks>
    /// Validates percentage values with the percent symbol (%). Accepts whole numbers and decimals.
    ///
    /// Example usage:
    /// <code>
    /// var regex = RegexPatternCollections.PercentageValidation();
    ///
    /// // Valid percentages
    /// bool isValid1 = regex.IsMatch("100%");      // true
    /// bool isValid2 = regex.IsMatch("12.34%");    // true
    /// bool isValid3 = regex.IsMatch("-25.5%");    // true
    ///
    /// // Invalid percentages
    /// bool isValid4 = regex.IsMatch("12.34");     // false
    /// bool isValid5 = regex.IsMatch("120%%");     // false
    /// </code>
    /// </remarks>
    /// <returns>A compiled <see href="https://learn.microsoft.com/en-us/dotnet/api/system.text.regularexpressions.regex">Regex</see> instance for percentage validation.</returns>
    [GeneratedRegex(pattern: @"^-?\d*\.?\d+%$", options: RegexOptions.Compiled)]
    public static partial Regex PercentageValidation();
    #endregion

    #region Date and Time Validation
    /// <summary>
    /// Validates dates in YYYY-MM-DD format using a compiled regular expression pattern.
    /// </summary>
    /// <remarks>
    /// Validates dates with the following rules:
    /// - Year must be 4 digits
    /// - Month must be 01-12
    /// - Day must be 01-31 (depending on month)
    ///
    /// > [!NOTE]
    /// > This validation checks format only, not valid calendar dates (e.g., Feb 31).
    ///
    /// Example usage:
    /// <code>
    /// var regex = RegexPatternCollections.DateValidationYMD();
    ///
    /// // Valid dates
    /// bool isValid1 = regex.IsMatch("2023-12-31");    // true
    /// bool isValid2 = regex.IsMatch("2024-01-01");    // true
    ///
    /// // Invalid dates
    /// bool isValid3 = regex.IsMatch("2023/12/31");    // false
    /// bool isValid4 = regex.IsMatch("23-12-31");      // false
    /// </code>
    /// </remarks>
    /// <returns>A compiled <see href="https://learn.microsoft.com/en-us/dotnet/api/system.text.regularexpressions.regex">Regex</see> instance for YMD date validation.</returns>
    [GeneratedRegex(
        pattern: @"^\d{4}-(0[1-9]|1[0-2])-(0[1-9]|[12]\d|3[01])$",
        options: RegexOptions.Compiled
    )]
    public static partial Regex DateValidationYMD();

    /// <summary>
    /// Validates dates in DD/MM/YYYY format using a compiled regular expression pattern.
    /// </summary>
    /// <remarks>
    /// Validates dates with the following rules:
    /// - Day must be 01-31 (depending on month)
    /// - Month must be 01-12
    /// - Year must be 4 digits
    ///
    /// > [!NOTE]
    /// > This validation checks format only, not valid calendar dates (e.g., 31/04/2023).
    ///
    /// Example usage:
    /// <code>
    /// var regex = RegexPatternCollections.DateValidationDMY();
    ///
    /// // Valid dates
    /// bool isValid1 = regex.IsMatch("31/12/2023");    // true
    /// bool isValid2 = regex.IsMatch("01/01/2024");    // true
    ///
    /// // Invalid dates
    /// bool isValid3 = regex.IsMatch("31-12-2023");    // false
    /// bool isValid4 = regex.IsMatch("31/12/23");      // false
    /// </code>
    /// </remarks>
    /// <returns>A compiled <see href="https://learn.microsoft.com/en-us/dotnet/api/system.text.regularexpressions.regex">Regex</see> instance for DMY date validation.</returns>
    [GeneratedRegex(
        pattern: @"^(0[1-9]|[12]\d|3[01])/(0[1-9]|1[0-2])/\d{4}$",
        options: RegexOptions.Compiled
    )]
    public static partial Regex DateValidationDMY();

    /// <summary>
    /// Validates time in 24-hour format (HH:MM:SS) using a compiled regular expression pattern.
    /// </summary>
    /// <remarks>
    /// Validates times with the following rules:
    /// - Hours must be 00-23
    /// - Minutes must be 00-59
    /// - Seconds must be 00-59
    ///
    /// Example usage:
    /// <code>
    /// var regex = RegexPatternCollections.TimeValidation24Hour();
    ///
    /// // Valid times
    /// bool isValid1 = regex.IsMatch("23:59:59");    // true
    /// bool isValid2 = regex.IsMatch("00:00:00");    // true
    ///
    /// // Invalid times
    /// bool isValid3 = regex.IsMatch("24:00:00");    // false
    /// bool isValid4 = regex.IsMatch("23:60:00");    // false
    /// </code>
    /// </remarks>
    /// <returns>A compiled <see href="https://learn.microsoft.com/en-us/dotnet/api/system.text.regularexpressions.regex">Regex</see> instance for 24-hour time validation.</returns>
    [GeneratedRegex(
        pattern: @"^([01]\d|2[0-3]):([0-5]\d):([0-5]\d)$",
        options: RegexOptions.Compiled
    )]
    public static partial Regex TimeValidation24Hour();

    /// <summary>
    /// Validates time in 12-hour format (HH:MM:SS AM/PM) using a compiled regular expression pattern.
    /// </summary>
    /// <remarks>
    /// Validates times with the following rules:
    /// - Hours must be 1-12
    /// - Minutes must be 00-59
    /// - Seconds must be 00-59
    /// - Must end with AM or PM (case sensitive)
    ///
    /// Example usage:
    /// <code>
    /// var regex = RegexPatternCollections.TimeValidation12Hour();
    ///
    /// // Valid times
    /// bool isValid1 = regex.IsMatch("11:59:59 PM");    // true
    /// bool isValid2 = regex.IsMatch("12:00:00 AM");    // true
    ///
    /// // Invalid times
    /// bool isValid3 = regex.IsMatch("13:00:00 PM");    // false
    /// bool isValid4 = regex.IsMatch("11:59:59 am");    // false
    /// </code>
    /// </remarks>
    /// <returns>A compiled <see href="https://learn.microsoft.com/en-us/dotnet/api/system.text.regularexpressions.regex">Regex</see> instance for 12-hour time validation.</returns>
    [GeneratedRegex(
        pattern: @"^(0?[1-9]|1[0-2]):([0-5]\d):([0-5]\d)\s?(AM|PM)$",
        options: RegexOptions.Compiled
    )]
    public static partial Regex TimeValidation12Hour();
    #endregion

    #region Web Validation
    /// <summary>
    /// Validates URLs using a compiled regular expression pattern.
    /// </summary>
    /// <remarks>
    /// Validates URLs with the following rules:
    /// - Optional protocol (http:// or https://)
    /// - Domain name with valid characters
    /// - Valid TLD (2-6 characters)
    /// - Optional path segments
    ///
    /// > [!IMPORTANT]
    /// > This validation is for basic URL format. For complete URL validation, consider using `System.Uri`.
    ///
    /// Example usage:
    /// <code>
    /// var regex = RegexPatternCollections.UrlValidation();
    ///
    /// // Valid URLs
    /// bool isValid1 = regex.IsMatch("https://www.example.com");           // true
    /// bool isValid2 = regex.IsMatch("example.com/path/to/resource");      // true
    ///
    /// // Invalid URLs
    /// bool isValid3 = regex.IsMatch("not-a-url");                        // false
    /// bool isValid4 = regex.IsMatch("http://invalid");                   // false
    /// </code>
    /// </remarks>
    /// <returns>A compiled <see href="https://learn.microsoft.com/en-us/dotnet/api/system.text.regularexpressions.regex">Regex</see> instance for URL validation.</returns>
    [GeneratedRegex(
        pattern: @"^(https?:\/\/)?([\da-z\.-]+)\.([a-z\.]{2,6})([\/\w \.-]*)*\/?$",
        options: RegexOptions.Compiled
    )]
    public static partial Regex UrlValidation();

    /// <summary>
    /// Validates IPv4 addresses using a compiled regular expression pattern.
    /// </summary>
    /// <remarks>
    /// Validates IPv4 addresses with the following rules:
    /// - Four octets separated by dots
    /// - Each octet must be between 0 and 255
    ///
    /// > [!NOTE]
    /// > For more comprehensive IP validation, consider using `System.Net.IPAddress.TryParse`.
    ///
    /// Example usage:
    /// <code>
    /// var regex = RegexPatternCollections.IPv4Validation();
    ///
    /// // Valid IPv4 addresses
    /// bool isValid1 = regex.IsMatch("***********");     // true
    /// bool isValid2 = regex.IsMatch("10.0.0.0");        // true
    ///
    /// // Invalid IPv4 addresses
    /// bool isValid3 = regex.IsMatch("256.1.2.3");       // false
    /// bool isValid4 = regex.IsMatch("1.2.3");           // false
    /// </code>
    /// </remarks>
    /// <returns>A compiled <see href="https://learn.microsoft.com/en-us/dotnet/api/system.text.regularexpressions.regex">Regex</see> instance for IPv4 validation.</returns>
    [GeneratedRegex(
        pattern: @"^(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$",
        options: RegexOptions.Compiled
    )]
    public static partial Regex IPv4Validation();

    /// <summary>
    /// Validates IPv6 addresses using a compiled regular expression pattern.
    /// </summary>
    /// <remarks>
    /// Validates all valid IPv6 address formats including:
    /// - Standard IPv6 addresses
    /// - Compressed IPv6 addresses (using ::)
    /// - IPv6 addresses with embedded IPv4
    /// - Link-local addresses
    ///
    /// > [!IMPORTANT]
    /// > For production use, prefer `System.Net.IPAddress.TryParse` for complete validation.
    ///
    /// Example usage:
    /// <code>
    /// var regex = RegexPatternCollections.IPv6Validation();
    ///
    /// // Valid IPv6 addresses
    /// bool isValid1 = regex.IsMatch("2001:0db8:85a3:0000:0000:8a2e:0370:7334");    // true
    /// bool isValid2 = regex.IsMatch("fe80::1ff:fe23:4567:890a");                    // true
    ///
    /// // Invalid IPv6 addresses
    /// bool isValid3 = regex.IsMatch("2001:0db8:85a3::8a2e::0370");                  // false
    /// bool isValid4 = regex.IsMatch("2001:0db8:85a3:0000:0000:8a2e:0370:733k");    // false
    /// </code>
    /// </remarks>
    /// <returns>A compiled <see href="https://learn.microsoft.com/en-us/dotnet/api/system.text.regularexpressions.regex">Regex</see> instance for IPv6 validation.</returns>
    [GeneratedRegex(
        pattern: @"^(([0-9a-fA-F]{1,4}:){7}[0-9a-fA-F]{1,4}|([0-9a-fA-F]{1,4}:){1,7}:|([0-9a-fA-F]{1,4}:){1,6}:[0-9a-fA-F]{1,4}|([0-9a-fA-F]{1,4}:){1,5}(:[0-9a-fA-F]{1,4}){1,2}|([0-9a-fA-F]{1,4}:){1,4}(:[0-9a-fA-F]{1,4}){1,3}|([0-9a-fA-F]{1,4}:){1,3}(:[0-9a-fA-F]{1,4}){1,4}|([0-9a-fA-F]{1,4}:){1,2}(:[0-9a-fA-F]{1,4}){1,5}|[0-9a-fA-F]{1,4}:((:[0-9a-fA-F]{1,4}){1,6})|:((:[0-9a-fA-F]{1,4}){1,7}|:)|fe80:(:[0-9a-fA-F]{0,4}){0,4}%[0-9a-zA-Z]+|::(ffff(:0{1,4})?:)?((25[0-5]|(2[0-4]|1?[0-9])?[0-9])\.){3}(25[0-5]|(2[0-4]|1?[0-9])?[0-9])|([0-9a-fA-F]{1,4}:){1,4}:((25[0-5]|(2[0-4]|1?[0-9])?[0-9])\.){3}(25[0-5]|(2[0-4]|1?[0-9])?[0-9]))$",
        options: RegexOptions.Compiled
    )]
    public static partial Regex IPv6Validation();

    /// <summary>
    /// Validates domain names using a compiled regular expression pattern.
    /// </summary>
    /// <remarks>
    /// Validates domain names with the following rules:
    /// - Contains only letters, numbers, hyphens, and dots
    /// - Segments separated by dots
    /// - TLD must be at least 2 characters
    /// - No consecutive dots or hyphens
    ///
    /// > [!NOTE]
    /// > This validation follows basic domain name rules but may not cover all edge cases.
    ///
    /// Example usage:
    /// <code>
    /// var regex = RegexPatternCollections.DomainValidation();
    ///
    /// // Valid domains
    /// bool isValid1 = regex.IsMatch("example.com");           // true
    /// bool isValid2 = regex.IsMatch("sub.example.co.uk");     // true
    ///
    /// // Invalid domains
    /// bool isValid3 = regex.IsMatch("example");               // false
    /// bool isValid4 = regex.IsMatch("example..com");          // false
    /// </code>
    /// </remarks>
    /// <returns>A compiled <see href="https://learn.microsoft.com/en-us/dotnet/api/system.text.regularexpressions.regex">Regex</see> instance for domain name validation.</returns>
    [GeneratedRegex(
        pattern: @"^([a-z0-9]+(-[a-z0-9]+)*\.)+[a-z]{2,}$",
        options: RegexOptions.Compiled
    )]
    public static partial Regex DomainValidation();
    #endregion

    /// <summary>
    /// Validates HTML tags using a compiled regular expression pattern.
    /// </summary>
    /// <remarks>
    /// Matches any HTML tag including:
    /// - Opening and closing tags
    /// - Self-closing tags
    /// - Tags with attributes
    ///
    /// > [!CAUTION]
    /// > This regex is for basic HTML tag detection. For proper HTML parsing, use a dedicated HTML parser like HtmlAgilityPack.
    ///
    /// Example usage:
    /// <code>
    /// var regex = RegexPatternCollections.HtmlTagValidation();
    ///
    /// // Valid HTML tags
    /// bool isValid1 = regex.IsMatch("<div>");                         // true
    /// bool isValid2 = regex.IsMatch("<img src='image.jpg' />");      // true
    ///
    /// // Invalid HTML tags
    /// bool isValid3 = regex.IsMatch("text without tags");            // false
    /// bool isValid4 = regex.IsMatch("<incomplete");                  // false
    /// </code>
    /// </remarks>
    /// <returns>A compiled <see href="https://learn.microsoft.com/en-us/dotnet/api/system.text.regularexpressions.regex">Regex</see> instance for HTML tag validation.</returns>
    [GeneratedRegex(pattern: @"<[^>]+>", options: RegexOptions.Compiled)]
    public static partial Regex HtmlTagValidation();

    #region Security Validation
    /// <summary>
    /// Validates password strength using a compiled regular expression pattern.
    /// </summary>
    /// <remarks>
    /// Enforces the following password requirements:
    /// - Minimum 8 characters
    /// - At least one uppercase letter
    /// - At least one lowercase letter
    /// - At least one number
    /// - At least one special character (@$!%*?&amp;)
    ///
    /// > [!IMPORTANT]
    /// > Consider using additional validation methods and password policies for production systems.
    ///
    /// Example usage:
    /// <code>
    /// var regex = RegexPatternCollections.StrongPasswordValidation();
    ///
    /// // Valid passwords
    /// bool isValid1 = regex.IsMatch("P@ssw0rd");           // true
    /// bool isValid2 = regex.IsMatch("Str0ng!Pass");        // true
    ///
    /// // Invalid passwords
    /// bool isValid3 = regex.IsMatch("weak");               // false
    /// bool isValid4 = regex.IsMatch("NoSpecialChar1");     // false
    /// </code>
    /// </remarks>
    /// <returns>A compiled <see href="https://learn.microsoft.com/en-us/dotnet/api/system.text.regularexpressions.regex">Regex</see> instance for strong password validation.</returns>
    [GeneratedRegex(
        pattern: @"^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]{8,}$",
        options: RegexOptions.Compiled
    )]
    public static partial Regex StrongPasswordValidation();

    /// <summary>
    /// Validates JWT (JSON Web Token) format using a compiled regular expression pattern.
    /// </summary>
    /// <remarks>
    /// Validates the basic structure of a JWT token:
    /// - Three parts separated by dots
    /// - Each part contains Base64Url encoded data
    /// - Optional padding
    ///
    /// > [!WARNING]
    /// > This only validates the token format. For proper JWT validation, use a dedicated JWT library.
    ///
    /// Example usage:
    /// <code>
    /// var regex = RegexPatternCollections.JwtValidation();
    ///
    /// // Valid JWT format
    /// bool isValid1 = regex.IsMatch("eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiIxMjM0NTY3ODkwIn0.dozjgNryP4J3jVmNHl0w5N_XgL0n3I9PlFUP0THsR8U");    // true
    ///
    /// // Invalid JWT format
    /// bool isValid2 = regex.IsMatch("not.a.valid.jwt");    // false
    /// bool isValid3 = regex.IsMatch("invalid-token");      // false
    /// </code>
    /// </remarks>
    /// <returns>A compiled <see href="https://learn.microsoft.com/en-us/dotnet/api/system.text.regularexpressions.regex">Regex</see> instance for JWT format validation.</returns>
    [GeneratedRegex(
        pattern: @"^[A-Za-z0-9-_=]+\.[A-Za-z0-9-_=]+\.?[A-Za-z0-9-_.+/=]*$",
        options: RegexOptions.Compiled
    )]
    public static partial Regex JwtValidation();
    #endregion

    #region Base64 Validation
    /// <summary>
    /// Validates Base64 encoded strings using a compiled regular expression pattern.
    /// </summary>
    /// <remarks>
    /// Validates strings that follow Base64 encoding rules:
    /// - Contains only alphanumeric characters, plus (+), and forward slash (/)
    /// - May end with up to two equals signs (=) for padding
    ///
    /// > [!NOTE]
    /// > This validation checks format only, not whether the string is actually valid Base64 encoded data.
    ///
    /// Example usage:
    /// <code>
    /// var regex = RegexPatternCollections.Base64Validation();
    ///
    /// // Valid Base64 format
    /// bool isValid1 = regex.IsMatch("SGVsbG8gV29ybGQ=");     // true
    /// bool isValid2 = regex.IsMatch("QUJDREVGMTIzNDU2");     // true
    ///
    /// // Invalid Base64 format
    /// bool isValid3 = regex.IsMatch("Invalid@Base64");        // false
    /// bool isValid4 = regex.IsMatch("===InvalidPadding");     // false
    /// </code>
    /// </remarks>
    /// <returns>A compiled <see href="https://learn.microsoft.com/en-us/dotnet/api/system.text.regularexpressions.regex">Regex</see> instance for Base64 format validation.</returns>
    [GeneratedRegex(pattern: @"^[A-Za-z0-9+/]*={0,2}$", options: RegexOptions.Compiled)]
    public static partial Regex Base64Validation();
    #endregion

    #region File and Path Validation
    /// <summary>
    /// Validates Windows file system paths using a compiled regular expression pattern.
    /// </summary>
    /// <remarks>
    /// Validates Windows paths with the following rules:
    /// - Must start with a drive letter followed by colon and backslash
    /// - Can contain multiple directory levels
    /// - Cannot contain invalid Windows filename characters (&lt;&gt;:"/\|?*)
    ///
    /// > [!TIP]
    /// > For cross-platform path validation, consider using <see cref="System.IO.Path"/> methods instead.
    ///
    /// Example usage:
    /// <code>
    /// var regex = RegexPatternCollections.WindowsPathValidation();
    ///
    /// // Valid Windows paths
    /// bool isValid1 = regex.IsMatch(@"C:\Users\<USER>\Data\Files\document.txt");   // true
    ///
    /// // Invalid Windows paths
    /// bool isValid3 = regex.IsMatch("/usr/local/bin");               // false
    /// bool isValid4 = regex.IsMatch(@"C:\Test|File.txt");            // false
    /// </code>
    /// </remarks>
    /// <returns>A compiled <see href="https://learn.microsoft.com/en-us/dotnet/api/system.text.regularexpressions.regex">Regex</see> instance for Windows path validation.</returns>
    [GeneratedRegex(
        pattern: @"^([a-zA-Z]:\\)((?:[^<>:""/\\|?*]*\\)*)[^<>:""/\\|?*]*$",
        options: RegexOptions.Compiled
    )]
    public static partial Regex WindowsPathValidation();

    /// <summary>
    /// Validates Unix-style file system paths using a compiled regular expression pattern.
    /// </summary>
    /// <remarks>
    /// Validates Unix paths with the following rules:
    /// - Must start with forward slash
    /// - Can contain multiple directory levels
    /// - Each segment must be non-empty
    ///
    /// > [!TIP]
    /// > For cross-platform path validation, consider using <see cref="System.IO.Path"/> methods instead.
    ///
    /// Example usage:
    /// <code>
    /// var regex = RegexPatternCollections.UnixPathValidation();
    ///
    /// // Valid Unix paths
    /// bool isValid1 = regex.IsMatch("/usr/local/bin");           // true
    /// bool isValid2 = regex.IsMatch("/home/<USER>/file.txt");      // true
    ///
    /// // Invalid Unix paths
    /// bool isValid3 = regex.IsMatch("C:\Windows");               // false
    /// bool isValid4 = regex.IsMatch("not/a/valid/path");         // false
    /// </code>
    /// </remarks>
    /// <returns>A compiled <see href="https://learn.microsoft.com/en-us/dotnet/api/system.text.regularexpressions.regex">Regex</see> instance for Unix path validation.</returns>
    [GeneratedRegex(pattern: @"^(/[^/]*)+$", options: RegexOptions.Compiled)]
    public static partial Regex UnixPathValidation();

    /// <summary>
    /// Validates file extensions using a compiled regular expression pattern.
    /// </summary>
    /// <remarks>
    /// Validates file extensions with the following rules:
    /// - Must start with a dot
    /// - Contains only lowercase letters and numbers
    ///
    /// > [!TIP]
    /// > For more robust file extension handling, consider using <see cref="System.IO.Path.GetExtension(string)"/>.
    ///
    /// Example usage:
    /// <code>
    /// var regex = RegexPatternCollections.FileExtensionValidation();
    ///
    /// // Valid file extensions
    /// bool isValid1 = regex.IsMatch(".txt");       // true
    /// bool isValid2 = regex.IsMatch(".jpg");       // true
    /// bool isValid3 = regex.IsMatch(".mp4");       // true
    ///
    /// // Invalid file extensions
    /// bool isValid4 = regex.IsMatch("txt");        // false
    /// bool isValid5 = regex.IsMatch(".TXT");       // false
    /// bool isValid6 = regex.IsMatch(".doc!");      // false
    /// </code>
    /// </remarks>
    /// <returns>A compiled <see href="https://learn.microsoft.com/en-us/dotnet/api/system.text.regularexpressions.regex">Regex</see> instance for file extension validation.</returns>
    [GeneratedRegex(pattern: @"\.[0-9a-z]+$", options: RegexOptions.Compiled)]
    public static partial Regex FileExtensionValidation();
    #endregion

    #region Identity Validation
    /// <summary>
    /// Validates credit card numbers using a compiled regular expression pattern.
    /// </summary>
    /// <remarks>
    /// Validates credit card numbers with the following format:
    /// - 16 digits total
    /// - Optional separators (space or hyphen) between each group of 4 digits
    ///
    /// > [!IMPORTANT]
    /// > This only validates the format. For proper credit card validation, use the Luhn algorithm and additional checks.
    ///
    /// Example usage:
    /// <code>
    /// var regex = RegexPatternCollections.CreditCardValidation();
    ///
    /// // Valid formats
    /// bool isValid1 = regex.IsMatch("****************");       // true
    /// bool isValid2 = regex.IsMatch("4111-1111-1111-1111");    // true
    /// bool isValid3 = regex.IsMatch("4111 1111 1111 1111");    // true
    ///
    /// // Invalid formats
    /// bool isValid4 = regex.IsMatch("411111111111");           // false
    /// bool isValid5 = regex.IsMatch("4111.1111.1111.1111");    // false
    /// </code>
    /// </remarks>
    /// <returns>A compiled <see href="https://learn.microsoft.com/en-us/dotnet/api/system.text.regularexpressions.regex">Regex</see> instance for credit card format validation.</returns>
    [GeneratedRegex(
        pattern: @"^\d{4}[- ]?\d{4}[- ]?\d{4}[- ]?\d{4}$",
        options: RegexOptions.Compiled
    )]
    public static partial Regex CreditCardValidation();

    /// <summary>
    /// Validates US Social Security Numbers (SSN) using a compiled regular expression pattern.
    /// </summary>
    /// <remarks>
    /// Validates SSN with the following format:
    /// - Three digits, followed by hyphen
    /// - Two digits, followed by hyphen
    /// - Four digits
    ///
    /// > [!IMPORTANT]
    /// > This only validates the format. For proper SSN validation, implement additional business rules.
    ///
    /// Example usage:
    /// <code>
    /// var regex = RegexPatternCollections.SsnValidation();
    ///
    /// // Valid format
    /// bool isValid1 = regex.IsMatch("***********");    // true
    ///
    /// // Invalid formats
    /// bool isValid2 = regex.IsMatch("*********");      // false
    /// bool isValid3 = regex.IsMatch("123-45-678");     // false
    /// </code>
    /// </remarks>
    /// <returns>A compiled <see href="https://learn.microsoft.com/en-us/dotnet/api/system.text.regularexpressions.regex">Regex</see> instance for SSN format validation.</returns>
    [GeneratedRegex(pattern: @"^\d{3}-\d{2}-\d{4}$", options: RegexOptions.Compiled)]
    public static partial Regex SsnValidation();

    /// <summary>
    /// Validates US ZIP codes using a compiled regular expression pattern.
    /// </summary>
    /// <remarks>
    /// Validates ZIP codes with the following formats:
    /// - Basic format: 5 digits
    /// - Extended format (ZIP+4): 5 digits, hyphen, 4 digits
    ///
    /// Example usage:
    /// <code>
    /// var regex = RegexPatternCollections.ZipCodeValidation();
    ///
    /// // Valid formats
    /// bool isValid1 = regex.IsMatch("12345");       // true
    /// bool isValid2 = regex.IsMatch("12345-6789");  // true
    ///
    /// // Invalid formats
    /// bool isValid3 = regex.IsMatch("1234");        // false
    /// bool isValid4 = regex.IsMatch("12345-678");   // false
    /// </code>
    /// </remarks>
    /// <returns>A compiled <see href="https://learn.microsoft.com/en-us/dotnet/api/system.text.regularexpressions.regex">Regex</see> instance for ZIP code format validation.</returns>
    [GeneratedRegex(pattern: @"^\d{5}(-\d{4})?$", options: RegexOptions.Compiled)]
    public static partial Regex ZipCodeValidation();
    #endregion

    #region String Format Validation
    /// <summary>
    /// Validates strings containing only letters using a compiled regular expression pattern.
    /// </summary>
    /// <remarks>
    /// Validates strings that:
    /// - Contain only uppercase and lowercase letters (A-Z, a-z)
    /// - Must not be empty
    ///
    /// Example usage:
    /// <code>
    /// var regex = RegexPatternCollections.LettersOnlyValidation();
    ///
    /// // Valid strings
    /// bool isValid1 = regex.IsMatch("Hello");       // true
    /// bool isValid2 = regex.IsMatch("AbCdEf");      // true
    ///
    /// // Invalid strings
    /// bool isValid3 = regex.IsMatch("Hello123");    // false
    /// bool isValid4 = regex.IsMatch("Hello!");      // false
    /// bool isValid5 = regex.IsMatch("");            // false
    /// </code>
    /// </remarks>
    /// <returns>A compiled <see href="https://learn.microsoft.com/en-us/dotnet/api/system.text.regularexpressions.regex">Regex</see> instance for letters-only validation.</returns>
    [GeneratedRegex(pattern: @"^[a-zA-Z]+$", options: RegexOptions.Compiled)]
    public static partial Regex LettersOnlyValidation();

    /// <summary>
    /// Validates strings containing only numbers using a compiled regular expression pattern.
    /// </summary>
    /// <remarks>
    /// Validates strings that:
    /// - Contain only digits (0-9)
    /// - Must not be empty
    ///
    /// > [!NOTE]
    /// > For numeric value validation, consider using <see cref="int.TryParse(string, out int)"/> or similar methods.
    ///
    /// Example usage:
    /// <code>
    /// var regex = RegexPatternCollections.NumbersOnlyValidation();
    ///
    /// // Valid strings
    /// bool isValid1 = regex.IsMatch("123");         // true
    /// bool isValid2 = regex.IsMatch("456789");      // true
    ///
    /// // Invalid strings
    /// bool isValid3 = regex.IsMatch("12.34");       // false
    /// bool isValid4 = regex.IsMatch("123a");        // false
    /// bool isValid5 = regex.IsMatch("");            // false
    /// </code>
    /// </remarks>
    /// <returns>A compiled <see href="https://learn.microsoft.com/en-us/dotnet/api/system.text.regularexpressions.regex">Regex</see> instance for numbers-only validation.</returns>
    [GeneratedRegex(pattern: @"^\d+$", options: RegexOptions.Compiled)]
    public static partial Regex NumbersOnlyValidation();

    /// <summary>
    /// Validates hexadecimal color codes using a compiled regular expression pattern.
    /// </summary>
    /// <remarks>
    /// Validates hex color codes in the following formats:
    /// - 6 characters (e.g., FF0000)
    /// - 3 characters shorthand (e.g., F00)
    /// - Optional # prefix
    ///
    /// > [!TIP]
    /// > For more comprehensive color handling, consider using a dedicated color parsing library.
    ///
    /// Example usage:
    /// <code>
    /// var regex = RegexPatternCollections.HexColorValidation();
    ///
    /// // Valid hex colors
    /// bool isValid1 = regex.IsMatch("#FF0000");     // true
    /// bool isValid2 = regex.IsMatch("F00");         // true
    /// bool isValid3 = regex.IsMatch("FFFFFF");      // true
    ///
    /// // Invalid hex colors
    /// bool isValid4 = regex.IsMatch("#GG0000");     // false
    /// bool isValid5 = regex.IsMatch("#FF00");       // false
    /// </code>
    /// </remarks>
    /// <returns>A compiled <see href="https://learn.microsoft.com/en-us/dotnet/api/system.text.regularexpressions.regex">Regex</see> instance for hex color validation.</returns>
    [GeneratedRegex(
        pattern: @"^#?([a-fA-F0-9]{6}|[a-fA-F0-9]{3})$",
        options: RegexOptions.Compiled
    )]
    public static partial Regex HexColorValidation();

    /// <summary>
    /// Validates ISBN (International Standard Book Number) using a compiled regular expression pattern.
    /// </summary>
    /// <remarks>
    /// Validates ISBN numbers in the following formats:
    /// - ISBN-10
    /// - ISBN-13
    /// - With or without hyphens/spaces
    /// - Optional "ISBN" prefix
    ///
    /// > [!IMPORTANT]
    /// > This only validates the format. For proper ISBN validation, implement checksum verification.
    ///
    /// Example usage:
    /// <code>
    /// var regex = RegexPatternCollections.IsbnValidation();
    ///
    /// // Valid ISBN formats
    /// bool isValid1 = regex.IsMatch("0-7475-3269-9");          // true (ISBN-10)
    /// bool isValid2 = regex.IsMatch("978-0-7475-3269-9");      // true (ISBN-13)
    /// bool isValid3 = regex.IsMatch("ISBN 0-7475-3269-9");     // true
    ///
    /// // Invalid ISBN formats
    /// bool isValid4 = regex.IsMatch("0-7475-3269");            // false
    /// bool isValid5 = regex.IsMatch("978-0-7475-3269-X");      // false
    /// </code>
    /// </remarks>
    /// <returns>A compiled <see href="https://learn.microsoft.com/en-us/dotnet/api/system.text.regularexpressions.regex">Regex</see> instance for ISBN format validation.</returns>
    [GeneratedRegex(
        pattern: @"^(?:ISBN(?:-1[03])?:? )?(?=[0-9X]{10}$|(?=(?:[0-9]+[- ]){3})[- 0-9X]{13}$|97[89][0-9]{10}$|(?=(?:[0-9]+[- ]){4})[- 0-9]{17}$)(?:97[89][- ]?)?[0-9]{1,5}[- ]?[0-9]+[- ]?[0-9]+[- ]?[0-9X]$",
        options: RegexOptions.Compiled
    )]
    public static partial Regex IsbnValidation();
    #endregion

    #region Alphanumeric Validation
    /// <summary>
    /// Validates strings containing only alphanumeric characters using a compiled regular expression pattern.
    /// </summary>
    /// <remarks>
    /// Validates strings that:
    /// - Contain only letters (A-Z, a-z) and numbers (0-9)
    /// - Can be empty
    ///
    /// Example usage:
    /// <code>
    /// var regex = RegexPatternCollections.AlphanumericValidation();
    ///
    /// // Valid strings
    /// bool isValid1 = regex.IsMatch("ABC123");      // true
    /// bool isValid2 = regex.IsMatch("123abc");      // true
    /// bool isValid3 = regex.IsMatch("");            // true
    ///
    /// // Invalid strings
    /// bool isValid4 = regex.IsMatch("ABC-123");     // false
    /// bool isValid5 = regex.IsMatch("ABC 123");     // false
    /// </code>
    /// </remarks>
    /// <returns>A compiled <see href="https://learn.microsoft.com/en-us/dotnet/api/system.text.regularexpressions.regex">Regex</see> instance for alphanumeric validation.</returns>
    [GeneratedRegex(pattern: "^[a-zA-Z0-9]*$", options: RegexOptions.Compiled)]
    public static partial Regex AlphanumericValidation();

    /// <summary>
    /// Extracts alphanumeric characters and specified special characters from a string.
    /// </summary>
    /// <remarks>
    /// Extracts characters that:
    /// - Are alphanumeric (A-Z, a-z, 0-9)
    /// - Match any of the specified special characters
    ///
    /// Example usage:
    /// <code>
    /// var regex = RegexPatternCollections.AlphanumericFinder(new[] { '@', '_', '-' });
    ///
    /// // Valid matches
    /// bool isValid1 = regex.IsMatch("user@domain");    // true
    /// bool isValid2 = regex.IsMatch("user_name");      // true
    /// bool isValid3 = regex.IsMatch("my-string");      // true
    ///
    /// // Invalid matches
    /// bool isValid4 = regex.IsMatch("user#name");      // false (# not in allowed chars)
    /// bool isValid5 = regex.IsMatch("string!");        // false (! not in allowed chars)
    /// </code>
    /// </remarks>
    /// <param name="allowedChars">Array of special characters to allow in addition to alphanumeric characters. If null, only alphanumeric characters are allowed.</param>
    /// <returns>A compiled <see href="https://learn.microsoft.com/en-us/dotnet/api/system.text.regularexpressions.regex">Regex</see> instance for extracting alphanumeric and specified special characters.</returns>
    public static Regex AlphanumericFinder(char[]? allowedChars = default)
    {
        string pattern =
            allowedChars is null || allowedChars.Length == 0
                ? "^[a-zA-Z0-9]+$"
                : $"^[a-zA-Z0-9{Regex.Escape(new string(allowedChars))}]+$";
        return new Regex(pattern, RegexOptions.Compiled);
    }

    /// <summary>
    /// Validates strings containing only non-alphanumeric characters using a compiled regular expression pattern.
    /// </summary>
    /// <remarks>
    /// Validates strings that:
    /// - Contain only special characters and symbols
    /// - Must not contain letters or numbers
    /// - Can be empty
    ///
    /// Example usage:
    /// <code>
    /// var regex = RegexPatternCollections.NonAlphanumericValidation();
    ///
    /// // Valid strings
    /// bool isValid1 = regex.IsMatch("!@#$%");      // true
    /// bool isValid2 = regex.IsMatch("&*()_+");     // true
    /// bool isValid3 = regex.IsMatch("");           // true
    ///
    /// // Invalid strings
    /// bool isValid4 = regex.IsMatch("abc123");     // false
    /// bool isValid5 = regex.IsMatch("!@#123");     // false
    /// bool isValid6 = regex.IsMatch("ABC!@#");     // false
    /// </code>
    /// </remarks>
    /// <returns>A compiled <see href="https://learn.microsoft.com/en-us/dotnet/api/system.text.regularexpressions.regex">Regex</see> instance for non-alphanumeric validation.</returns>
    [GeneratedRegex(pattern: "^[^a-zA-Z0-9]*$", options: RegexOptions.Compiled)]
    public static partial Regex NonAlphanumericValidation();
    #endregion

    #region Phone Number Validation
    /// <summary>
    /// Validates international phone numbers using a compiled regular expression pattern.
    /// </summary>
    /// <remarks>
    /// Validates phone numbers with:
    /// - Optional + prefix
    /// - Digits, dots, hyphens, and spaces
    /// - Optional parentheses groups
    ///
    /// > [!IMPORTANT]
    /// > This is a general-purpose phone validator. For country-specific validation, use dedicated rules.
    ///
    /// Example usage:
    /// <code>
    /// var regex = RegexPatternCollections.PhoneNumberValidation();
    ///
    /// // Valid formats
    /// bool isValid1 = regex.IsMatch("******-567-8900");        // true
    /// bool isValid2 = regex.IsMatch("(*************");         // true
    /// bool isValid3 = regex.IsMatch("************");           // true
    /// bool isValid4 = regex.IsMatch("+44 20 7123 4567");       // true
    ///
    /// // Invalid formats
    /// bool isValid5 = regex.IsMatch("123-ABC-4567");           // false
    /// bool isValid6 = regex.IsMatch("+******-567-8900");       // false
    /// </code>
    /// </remarks>
    /// <returns>A compiled <see href="https://learn.microsoft.com/en-us/dotnet/api/system.text.regularexpressions.regex">Regex</see> instance for phone number validation.</returns>
    [GeneratedRegex(
        pattern: @"^\+?(\d[\d-. ]+)?(\([\d-. ]+\))?[\d-. ]+\d$",
        options: RegexOptions.Compiled
    )]
    public static partial Regex PhoneNumberValidation();
    #endregion

    #region Log Parsing
    /// <summary>
    /// Splits log entries based on timestamp patterns using a compiled regular expression pattern.
    /// </summary>
    /// <remarks>
    /// Identifies log entries by matching timestamp format:
    /// - Format: [YYYY-MM-DD HH:MM:SS]
    ///
    /// Example usage:
    /// <code>
    /// var regex = RegexPatternCollections.LogEntrySplitter();
    /// var entries = regex.Split(logContent);
    ///
    /// // Sample log content
    /// string logContent = @"
    /// [2024-01-20 10:15:30] Entry 1
    /// [2024-01-20 10:15:31] Entry 2
    /// ";
    /// </code>
    /// </remarks>
    /// <returns>A compiled <see href="https://learn.microsoft.com/en-us/dotnet/api/system.text.regularexpressions.regex">Regex</see> instance for splitting log entries.</returns>
    [GeneratedRegex(
        pattern: @"(?=\[\d{4}-\d{2}-\d{2}\s+\d{2}:\d{2}:\d{2}\])",
        options: RegexOptions.Singleline
    )]
    public static partial Regex LogEntrySplitter();

    /// <summary>
    /// Parses simple log entries with timestamp, level, and message using a compiled regular expression pattern.
    /// </summary>
    /// <remarks>
    /// Extracts components from log entries in format:
    /// - [Timestamp] [Level] Message
    ///
    /// Example usage:
    /// <code>
    /// var regex = RegexPatternCollections.SimpleLogEntry();
    /// var match = regex.Match("[2024-01-20 10:15:30] [INFO] Application started");
    ///
    /// if (match.Success)
    /// {
    ///     string timestamp = match.Groups[1].Value;  // "2024-01-20 10:15:30"
    ///     string level = match.Groups[2].Value;      // "INFO"
    ///     string message = match.Groups[3].Value;    // "Application started"
    /// }
    /// </code>
    /// </remarks>
    /// <returns>A compiled <see href="https://learn.microsoft.com/en-us/dotnet/api/system.text.regularexpressions.regex">Regex</see> instance for parsing simple log entries.</returns>
    [GeneratedRegex(pattern: @"^\[(.*?)\] \[(.*?)\] (.+)$", options: RegexOptions.Singleline)]
    public static partial Regex SimpleLogEntry();

    /// <summary>
    /// Parses detailed log entries with exception information using a compiled regular expression pattern.
    /// </summary>
    /// <remarks>
    /// Extracts components from detailed log entries in format:
    /// - [Timestamp] [Level]
    /// - Type: ExceptionType
    /// - Message: ExceptionMessage
    /// - StackTrace: MultilineStackTrace
    ///
    /// > [!NOTE]
    /// > Stack trace lines should start with "   --> "
    ///
    /// Example usage:
    /// <code>
    /// var regex = RegexPatternCollections.DetailedLogEntry();
    /// var match = regex.Match(@"
    /// [2024-01-20 10:15:30] [ERROR] Type: System.Exception
    /// Message: Operation failed
    /// StackTrace:
    ///    --> at Program.Main() in Program.cs:line 10
    ///    --> at Program.&lt;Main&gt;() in Program.cs:line 5
    /// ");
    /// </code>
    /// </remarks>
    /// <returns>A compiled <see href="https://learn.microsoft.com/en-us/dotnet/api/system.text.regularexpressions.regex">Regex</see> instance for parsing detailed log entries.</returns>
    [GeneratedRegex(
        pattern: @"\[(.*?)\] \[(.*?)\] Type: (.*?)[\r\n]+Message: (.*?)[\r\n]+StackTrace:[\r\n]+((?:   --> .*(?:\r?\n|$))*)",
        options: RegexOptions.Singleline
    )]
    public static partial Regex DetailedLogEntry();
    #endregion

    #region Connection String Parsing
    /// <summary>
    /// Extracts host information from Oracle TNS connection strings.
    /// </summary>
    /// <remarks>
    /// Matches HOST parameter in TNS connection strings.
    ///
    /// Example usage:
    /// <code>
    /// var regex = RegexPatternCollections.OracleTnsHost();
    /// var match = regex.Match("(HOST=localhost)");
    /// string host = match.Groups[1].Value;  // "localhost"
    /// </code>
    /// </remarks>
    /// <returns>A <see href="https://learn.microsoft.com/en-us/dotnet/api/system.text.regularexpressions.regex">Regex</see> instance for extracting TNS host.</returns>
    [GeneratedRegex(pattern: @"HOST=([^)]+)")]
    public static partial Regex OracleTnsHost();

    /// <summary>
    /// Extracts port information from Oracle TNS connection strings.
    /// </summary>
    /// <remarks>
    /// Matches PORT parameter in TNS connection strings.
    ///
    /// Example usage:
    /// <code>
    /// var regex = RegexPatternCollections.OracleTnsPort();
    /// var match = regex.Match("(PORT=1521)");
    /// string port = match.Groups[1].Value;  // "1521"
    /// </code>
    /// </remarks>
    /// <returns>A <see href="https://learn.microsoft.com/en-us/dotnet/api/system.text.regularexpressions.regex">Regex</see> instance for extracting TNS port.</returns>
    [GeneratedRegex(pattern: @"PORT=(\d+)")]
    public static partial Regex OracleTnsPort();

    /// <summary>
    /// Extracts service name from Oracle TNS connection strings.
    /// </summary>
    /// <remarks>
    /// Matches SERVICE_NAME parameter in TNS connection strings.
    ///
    /// Example usage:
    /// <code>
    /// var regex = RegexPatternCollections.OracleTnsServiceName();
    /// var match = regex.Match("(SERVICE_NAME=ORCL)");
    /// string serviceName = match.Groups[1].Value;  // "ORCL"
    /// </code>
    /// </remarks>
    /// <returns>A <see href="https://learn.microsoft.com/en-us/dotnet/api/system.text.regularexpressions.regex">Regex</see> instance for extracting TNS service name.</returns>
    [GeneratedRegex(pattern: @"SERVICE_NAME=([^)]+)")]
    public static partial Regex OracleTnsServiceName();

    /// <summary>
    /// Extracts user ID from Oracle TNS connection strings.
    /// </summary>
    /// <remarks>
    /// Matches User Id parameter in TNS connection strings.
    ///
    /// > [!CAUTION]
    /// > Avoid storing sensitive information in plain text.
    ///
    /// Example usage:
    /// <code>
    /// var regex = RegexPatternCollections.OracleTnsUserId();
    /// var match = regex.Match("User Id=admin;");
    /// string userId = match.Groups[1].Value;  // "admin"
    /// </code>
    /// </remarks>
    /// <returns>A <see href="https://learn.microsoft.com/en-us/dotnet/api/system.text.regularexpressions.regex">Regex</see> instance for extracting TNS user ID.</returns>
    [GeneratedRegex(pattern: @"User Id=([^;]+)")]
    public static partial Regex OracleTnsUserId();

    /// <summary>
    /// Extracts password from Oracle TNS connection strings.
    /// </summary>
    /// <remarks>
    /// Matches Password parameter in TNS connection strings.
    ///
    /// > [!WARNING]
    /// > This should only be used for parsing. Never store or log passwords.
    ///
    /// Example usage:
    /// <code>
    /// var regex = RegexPatternCollections.OracleTnsPassword();
    /// var match = regex.Match("Password=****;");
    /// string password = match.Groups[1].Value;
    /// </code>
    /// </remarks>
    /// <returns>A <see href="https://learn.microsoft.com/en-us/dotnet/api/system.text.regularexpressions.regex">Regex</see> instance for extracting TNS password.</returns>
    [GeneratedRegex(pattern: @"Password=([^;]+)")]
    public static partial Regex OracleTnsPassword();
    #endregion

    #region String Manipulation
    /// <summary>
    /// Converts text to snake_case format using a compiled regular expression pattern.
    /// </summary>
    /// <remarks>
    /// Identifies camelCase or PascalCase boundaries and converts them to snake_case.
    ///
    /// Example usage:
    /// <code>
    /// var regex = RegexPatternCollections.SnakeCase();
    /// string result = regex.Replace("camelCase", "$1_$2").ToLower();     // "camel_case"
    /// string result2 = regex.Replace("PascalCase", "$1_$2").ToLower();   // "pascal_case"
    /// string result3 = regex.Replace("ABC", "$1_$2").ToLower();          // "a_b_c"
    /// </code>
    /// </remarks>
    /// <returns>A <see href="https://learn.microsoft.com/en-us/dotnet/api/system.text.regularexpressions.regex">Regex</see> instance for snake case conversion.</returns>
    [GeneratedRegex(pattern: "([a-z])([A-Z])")]
    public static partial Regex SnakeCase();

    /// <summary>
    /// Converts text to kebab-case format using a compiled regular expression pattern.
    /// </summary>
    /// <remarks>
    /// Identifies camelCase or PascalCase boundaries and converts them to kebab-case.
    ///
    /// Example usage:
    /// <code>
    /// var regex = RegexPatternCollections.KebabCase();
    /// string result = regex.Replace("camelCase", "$1-$2").ToLower();     // "camel-case"
    /// string result2 = regex.Replace("PascalCase", "$1-$2").ToLower();   // "pascal-case"
    /// string result3 = regex.Replace("ABC", "$1-$2").ToLower();          // "a-b-c"
    /// </code>
    /// </remarks>
    /// <returns>A <see href="https://learn.microsoft.com/en-us/dotnet/api/system.text.regularexpressions.regex">Regex</see> instance for kebab case conversion.</returns>
    [GeneratedRegex(pattern: "([a-z])([A-Z])")]
    public static partial Regex KebabCase();
    #endregion

    #region Common Patterns
    /// <summary>
    /// Validates usernames using a compiled regular expression pattern.
    /// </summary>
    /// <remarks>
    /// Validates usernames that:
    /// - Are 3-16 characters long
    /// - Contain only letters (A-Z, a-z), numbers (0-9), and underscores
    ///
    /// > [!NOTE]
    /// > This is a common pattern for usernames. Adjust the length and allowed characters based on your requirements.
    ///
    /// Example usage:
    /// <code>
    /// var regex = RegexPatternCollections.UsernameValidation();
    ///
    /// // Valid usernames
    /// bool isValid1 = regex.IsMatch("john_doe");        // true
    /// bool isValid2 = regex.IsMatch("user123");         // true
    /// bool isValid3 = regex.IsMatch("Admin_User_1");    // true
    ///
    /// // Invalid usernames
    /// bool isValid4 = regex.IsMatch("a");               // false (too short)
    /// bool isValid5 = regex.IsMatch("user@123");        // false (invalid character)
    /// bool isValid6 = regex.IsMatch("very_long_username_123"); // false (too long)
    /// </code>
    /// </remarks>
    /// <returns>A compiled <see href="https://learn.microsoft.com/en-us/dotnet/api/system.text.regularexpressions.regex">Regex</see> instance for username validation.</returns>
    [GeneratedRegex(pattern: @"^[a-zA-Z0-9_]{3,16}$", options: RegexOptions.Compiled)]
    public static partial Regex UsernameValidation();

    /// <summary>
    /// Validates URL slugs using a compiled regular expression pattern.
    /// </summary>
    /// <remarks>
    /// Validates slugs that:
    /// - Contain only lowercase letters, numbers, and hyphens
    /// - Cannot start or end with a hyphen
    /// - Cannot have consecutive hyphens
    ///
    /// > [!TIP]
    /// > Slugs are commonly used in SEO-friendly URLs.
    ///
    /// Example usage:
    /// <code>
    /// var regex = RegexPatternCollections.SlugValidation();
    ///
    /// // Valid slugs
    /// bool isValid1 = regex.IsMatch("my-blog-post");            // true
    /// bool isValid2 = regex.IsMatch("product123");              // true
    /// bool isValid3 = regex.IsMatch("2024-01-news");           // true
    ///
    /// // Invalid slugs
    /// bool isValid4 = regex.IsMatch("-invalid-slug");          // false (starts with hyphen)
    /// bool isValid5 = regex.IsMatch("invalid--slug");          // false (consecutive hyphens)
    /// bool isValid6 = regex.IsMatch("UPPERCASE-INVALID");      // false (uppercase letters)
    /// </code>
    /// </remarks>
    /// <returns>A compiled <see href="https://learn.microsoft.com/en-us/dotnet/api/system.text.regularexpressions.regex">Regex</see> instance for slug validation.</returns>
    [GeneratedRegex(pattern: @"^[a-z0-9]+(?:-[a-z0-9]+)*$", options: RegexOptions.Compiled)]
    public static partial Regex SlugValidation();

    /// <summary>
    /// Validates semantic version strings using a compiled regular expression pattern.
    /// </summary>
    /// <remarks>
    /// Validates versions that follow <see href="https://semver.org/">Semantic Versioning 2.0.0</see> specification:
    /// - Major.Minor.Patch format
    /// - Optional pre-release version
    /// - Optional build metadata
    ///
    /// > [!IMPORTANT]
    /// > This pattern strictly follows SemVer 2.0.0 specification.
    ///
    /// Example usage:
    /// <code>
    /// var regex = RegexPatternCollections.SemVerValidation();
    ///
    /// // Valid versions
    /// bool isValid1 = regex.IsMatch("1.0.0");                  // true
    /// bool isValid2 = regex.IsMatch("2.1.0-alpha.1");         // true
    /// bool isValid3 = regex.IsMatch("1.0.0-rc.1+build.123");  // true
    ///
    /// // Invalid versions
    /// bool isValid4 = regex.IsMatch("1.0");                   // false (missing patch)
    /// bool isValid5 = regex.IsMatch("1.0.0.0");              // false (extra segment)
    /// bool isValid6 = regex.IsMatch("v1.0.0");               // false (leading 'v')
    /// </code>
    /// </remarks>
    /// <returns>A compiled <see href="https://learn.microsoft.com/en-us/dotnet/api/system.text.regularexpressions.regex">Regex</see> instance for semantic version validation.</returns>
    [GeneratedRegex(
        pattern: @"^(0|[1-9]\d*)\.(0|[1-9]\d*)\.(0|[1-9]\d*)(?:-((?:0|[1-9]\d*|\d*[a-zA-Z-][0-9a-zA-Z-]*)(?:\.(?:0|[1-9]\d*|\d*[a-zA-Z-][0-9a-zA-Z-]*))*))?(?:\+([0-9a-zA-Z-]+(?:\.[0-9a-zA-Z-]+)*))?$",
        options: RegexOptions.Compiled
    )]
    public static partial Regex SemVerValidation();

    /// <summary>
    /// Validates latitude coordinates using a compiled regular expression pattern.
    /// </summary>
    /// <remarks>
    /// Validates latitude values that:
    /// - Range from -90 to 90 degrees
    /// - Allow decimal points
    /// - Accept optional plus/minus sign
    ///
    /// Example usage:
    /// <code>
    /// var regex = RegexPatternCollections.LatitudeValidation();
    ///
    /// // Valid latitudes
    /// bool isValid1 = regex.IsMatch("45.4215");      // true
    /// bool isValid2 = regex.IsMatch("-23.6261");     // true
    /// bool isValid3 = regex.IsMatch("+90.0");        // true
    ///
    /// // Invalid latitudes
    /// bool isValid4 = regex.IsMatch("91.0");         // false (out of range)
    /// bool isValid5 = regex.IsMatch("-90.1");        // false (out of range)
    /// bool isValid6 = regex.IsMatch("45,4215");      // false (invalid decimal separator)
    /// </code>
    /// </remarks>
    /// <returns>A compiled <see href="https://learn.microsoft.com/en-us/dotnet/api/system.text.regularexpressions.regex">Regex</see> instance for latitude validation.</returns>
    [GeneratedRegex(
        pattern: @"^[-+]?([1-8]?\d(\.\d+)?|90(\.0+)?)$",
        options: RegexOptions.Compiled
    )]
    public static partial Regex LatitudeValidation();

    /// <summary>
    /// Validates longitude coordinates using a compiled regular expression pattern.
    /// </summary>
    /// <remarks>
    /// Validates longitude values that:
    /// - Range from -180 to 180 degrees
    /// - Allow decimal points
    /// - Accept optional plus/minus sign
    ///
    /// Example usage:
    /// <code>
    /// var regex = RegexPatternCollections.LongitudeValidation();
    ///
    /// // Valid longitudes
    /// bool isValid1 = regex.IsMatch("123.4567");     // true
    /// bool isValid2 = regex.IsMatch("-45.6789");     // true
    /// bool isValid3 = regex.IsMatch("+180.0");       // true
    ///
    /// // Invalid longitudes
    /// bool isValid4 = regex.IsMatch("181.0");        // false (out of range)
    /// bool isValid5 = regex.IsMatch("-180.1");       // false (out of range)
    /// bool isValid6 = regex.IsMatch("45,6789");      // false (invalid decimal separator)
    /// </code>
    /// </remarks>
    /// <returns>A compiled <see href="https://learn.microsoft.com/en-us/dotnet/api/system.text.regularexpressions.regex">Regex</see> instance for longitude validation.</returns>
    [GeneratedRegex(
        pattern: @"^[-+]?(180(\.0+)?|((1[0-7]\d)|([1-9]?\d))(\.\d+)?)$",
        options: RegexOptions.Compiled
    )]
    public static partial Regex LongitudeValidation();

    /// <summary>
    /// Validates MAC addresses using a compiled regular expression pattern.
    /// </summary>
    /// <remarks>
    /// Validates MAC addresses that:
    /// - Consist of 6 pairs of hexadecimal digits
    /// - Are separated by colons (:) or hyphens (-)
    ///
    /// Example usage:
    /// <code>
    /// var regex = RegexPatternCollections.MacAddressValidation();
    ///
    /// // Valid MAC addresses
    /// bool isValid1 = regex.IsMatch("00:1A:2B:3C:4D:5E");     // true
    /// bool isValid2 = regex.IsMatch("00-1A-2B-3C-4D-5E");     // true
    /// bool isValid3 = regex.IsMatch("FF:FF:FF:FF:FF:FF");     // true
    ///
    /// // Invalid MAC addresses
    /// bool isValid4 = regex.IsMatch("00:1A:2B:3C:4D");        // false (too short)
    /// bool isValid5 = regex.IsMatch("00:1A:2B:3C:4D:5E:6F");  // false (too long)
    /// bool isValid6 = regex.IsMatch("00:1A:2B:3C:4D:5G");     // false (invalid hex)
    /// </code>
    /// </remarks>
    /// <returns>A compiled <see href="https://learn.microsoft.com/en-us/dotnet/api/system.text.regularexpressions.regex">Regex</see> instance for MAC address validation.</returns>
    [GeneratedRegex(
        pattern: @"^([0-9A-Fa-f]{2}[:-]){5}([0-9A-Fa-f]{2})$",
        options: RegexOptions.Compiled
    )]
    public static partial Regex MacAddressValidation();

    /// <summary>
    /// Validates GUID/UUID strings using a compiled regular expression pattern.
    /// </summary>
    /// <remarks>
    /// Validates GUIDs that follow the standard 8-4-4-4-12 format with hexadecimal digits.
    ///
    /// > [!NOTE]
    /// > This pattern validates the most common GUID format with hyphens.
    ///
    /// Example usage:
    /// <code>
    /// var regex = RegexPatternCollections.GuidValidation();
    ///
    /// // Valid GUIDs
    /// bool isValid1 = regex.IsMatch("550e8400-e29b-41d4-a716-************");  // true
    /// bool isValid2 = regex.IsMatch("123e4567-e89b-12d3-a456-************");  // true
    ///
    /// // Invalid GUIDs
    /// bool isValid3 = regex.IsMatch("550e8400e29b41d4a716************");      // false (no hyphens)
    /// bool isValid4 = regex.IsMatch("zzze8400-e29b-41d4-a716-************");  // false (invalid chars)
    /// </code>
    /// </remarks>
    /// <returns>A compiled <see href="https://learn.microsoft.com/en-us/dotnet/api/system.text.regularexpressions.regex">Regex</see> instance for GUID validation.</returns>
    [GeneratedRegex(
        pattern: @"^[0-9a-fA-F]{8}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{12}$",
        options: RegexOptions.Compiled
    )]
    public static partial Regex GuidValidation();

    /// <summary>
    /// Validates MongoDB ObjectId strings using a compiled regular expression pattern.
    /// </summary>
    /// <remarks>
    /// Validates 24-character hexadecimal strings that represent MongoDB ObjectIds.
    ///
    /// > [!IMPORTANT]
    /// > MongoDB ObjectIds are 12-byte values represented as 24-character hexadecimal strings.
    ///
    /// Example usage:
    /// <code>
    /// var regex = RegexPatternCollections.MongoObjectIdValidation();
    ///
    /// // Valid ObjectIds
    /// bool isValid1 = regex.IsMatch("507f1f77bcf86cd799439011");  // true
    /// bool isValid2 = regex.IsMatch("000000000000000000000000");  // true
    ///
    /// // Invalid ObjectIds
    /// bool isValid3 = regex.IsMatch("507f1f77bcf86cd7994390");    // false (too short)
    /// bool isValid4 = regex.IsMatch("507f1f77bcf86cd7994390111"); // false (too long)
    /// bool isValid5 = regex.IsMatch("507f1f77bcf86cd79943901g");  // false (invalid char)
    /// </code>
    /// </remarks>
    /// <returns>A compiled <see href="https://learn.microsoft.com/en-us/dotnet/api/system.text.regularexpressions.regex">Regex</see> instance for MongoDB ObjectId validation.</returns>
    [GeneratedRegex(pattern: @"^[0-9a-fA-F]{24}$", options: RegexOptions.Compiled)]
    public static partial Regex MongoObjectIdValidation();

    /// <summary>
    /// Validates time duration strings using a compiled regular expression pattern.
    /// </summary>
    /// <remarks>
    /// Validates duration strings in the format of days(d), hours(h), minutes(m), and seconds(s).
    /// Each component is optional but must appear in the correct order.
    ///
    /// > [!TIP]
    /// > This format is commonly used in configuration files and command-line interfaces.
    ///
    /// Example usage:
    /// <code>
    /// var regex = RegexPatternCollections.TimeDurationValidation();
    ///
    /// // Valid durations
    /// bool isValid1 = regex.IsMatch("1d2h30m");      // true
    /// bool isValid2 = regex.IsMatch("24h");          // true
    /// bool isValid3 = regex.IsMatch("90s");          // true
    /// bool isValid4 = regex.IsMatch("1d30m");        // true
    ///
    /// // Invalid durations
    /// bool isValid5 = regex.IsMatch("1h2d");         // false (wrong order)
    /// bool isValid6 = regex.IsMatch("1y");           // false (invalid unit)
    /// bool isValid7 = regex.IsMatch("1d2h30x");      // false (invalid unit)
    /// </code>
    /// </remarks>
    /// <returns>A compiled <see href="https://learn.microsoft.com/en-us/dotnet/api/system.text.regularexpressions.regex">Regex</see> instance for time duration validation.</returns>
    [GeneratedRegex(pattern: @"^(\d+d)?(\d+h)?(\d+m)?(\d+s)?$", options: RegexOptions.Compiled)]
    public static partial Regex TimeDurationValidation();

    /// <summary>
    /// Validates RGB color strings using a compiled regular expression pattern.
    /// </summary>
    /// <remarks>
    /// Validates RGB color values in the format rgb(r,g,b) where each component is 0-255.
    ///
    /// > [!NOTE]
    /// > Spaces around numbers are allowed for better readability.
    ///
    /// Example usage:
    /// <code>
    /// var regex = RegexPatternCollections.RgbColorValidation();
    ///
    /// // Valid RGB colors
    /// bool isValid1 = regex.IsMatch("rgb(255,0,0)");         // true (red)
    /// bool isValid2 = regex.IsMatch("rgb(0, 255, 0)");       // true (green)
    /// bool isValid3 = regex.IsMatch("rgb(0,0,255)");         // true (blue)
    ///
    /// // Invalid RGB colors
    /// bool isValid4 = regex.IsMatch("rgb(256,0,0)");         // false (value > 255)
    /// bool isValid5 = regex.IsMatch("rgb(0,0)");             // false (missing blue)
    /// bool isValid6 = regex.IsMatch("rgb(0,0,255,1)");       // false (extra value)
    /// </code>
    /// </remarks>
    /// <returns>A compiled <see href="https://learn.microsoft.com/en-us/dotnet/api/system.text.regularexpressions.regex">Regex</see> instance for RGB color validation.</returns>
    [GeneratedRegex(
        pattern: @"^rgb\(\s*(\d{1,3})\s*,\s*(\d{1,3})\s*,\s*(\d{1,3})\s*\)$",
        options: RegexOptions.Compiled
    )]
    public static partial Regex RgbColorValidation();

    /// <summary>
    /// Validates RGBA color strings using a compiled regular expression pattern.
    /// </summary>
    /// <remarks>
    /// Validates RGBA color values in the format rgba(r,g,b,a) where RGB components are 0-255 and alpha is 0-1.
    ///
    /// > [!NOTE]
    /// > The alpha value must be between 0 (fully transparent) and 1 (fully opaque).
    ///
    /// Example usage:
    /// <code>
    /// var regex = RegexPatternCollections.RgbaColorValidation();
    ///
    /// // Valid RGBA colors
    /// bool isValid1 = regex.IsMatch("rgba(255,0,0,1)");      // true (solid red)
    /// bool isValid2 = regex.IsMatch("rgba(0,0,255,0.5)");    // true (semi-transparent blue)
    /// bool isValid3 = regex.IsMatch("rgba(0, 255, 0, 0)");   // true (transparent green)
    ///
    /// // Invalid RGBA colors
    /// bool isValid4 = regex.IsMatch("rgba(255,0,0,1.1)");    // false (alpha > 1)
    /// bool isValid5 = regex.IsMatch("rgba(256,0,0,1)");      // false (RGB > 255)
    /// bool isValid6 = regex.IsMatch("rgba(0,0,255)");        // false (missing alpha)
    /// </code>
    /// </remarks>
    /// <returns>A compiled <see href="https://learn.microsoft.com/en-us/dotnet/api/system.text.regularexpressions.regex">Regex</see> instance for RGBA color validation.</returns>
    [GeneratedRegex(
        pattern: @"^rgba\(\s*(\d{1,3})\s*,\s*(\d{1,3})\s*,\s*(\d{1,3})\s*,\s*([01]|0?\.\d+)\s*\)$",
        options: RegexOptions.Compiled
    )]
    public static partial Regex RgbaColorValidation();

    /// <summary>
    /// Validates HSL color strings using a compiled regular expression pattern.
    /// </summary>
    /// <remarks>
    /// Validates HSL color values in the format hsl(h,s%,l%) where:
    /// - Hue (h) is 0-359 degrees
    /// - Saturation (s) is 0-100%
    /// - Lightness (l) is 0-100%
    ///
    /// > [!TIP]
    /// > HSL provides a more intuitive way to specify colors than RGB.
    ///
    /// Example usage:
    /// <code>
    /// var regex = RegexPatternCollections.HslColorValidation();
    ///
    /// // Valid HSL colors
    /// bool isValid1 = regex.IsMatch("hsl(0,100%,50%)");      // true (red)
    /// bool isValid2 = regex.IsMatch("hsl(120, 100%, 50%)");  // true (green)
    /// bool isValid3 = regex.IsMatch("hsl(240,100%,50%)");    // true (blue)
    ///
    /// // Invalid HSL colors
    /// bool isValid4 = regex.IsMatch("hsl(360,100%,50%)");    // false (hue >= 360)
    /// bool isValid5 = regex.IsMatch("hsl(0,101%,50%)");      // false (saturation > 100%)
    /// bool isValid6 = regex.IsMatch("hsl(0,50%,50%)");       // false (missing % in saturation)
    /// </code>
    /// </remarks>
    /// <returns>A compiled <see href="https://learn.microsoft.com/en-us/dotnet/api/system.text.regularexpressions.regex">Regex</see> instance for HSL color validation.</returns>
    [GeneratedRegex(
        pattern: @"^hsl\(\s*(\d{1,3})\s*,\s*(\d{1,3})%\s*,\s*(\d{1,3})%\s*\)$",
        options: RegexOptions.Compiled
    )]
    public static partial Regex HslColorValidation();

    /// <summary>
    /// Validates HSLA color strings using a compiled regular expression pattern.
    /// </summary>
    /// <remarks>
    /// Validates HSLA color values in the format hsla(h,s%,l%,a) where:
    /// - Hue (h) is 0-359 degrees
    /// - Saturation (s) is 0-100%
    /// - Lightness (l) is 0-100%
    /// - Alpha (a) is 0-1
    ///
    /// > [!TIP]
    /// > HSLA adds transparency support to the HSL color model.
    ///
    /// Example usage:
    /// <code>
    /// var regex = RegexPatternCollections.HslaColorValidation();
    ///
    /// // Valid HSLA colors
    /// bool isValid1 = regex.IsMatch("hsla(0,100%,50%,1)");       // true (solid red)
    /// bool isValid2 = regex.IsMatch("hsla(120,100%,50%,0.5)");   // true (semi-transparent green)
    /// bool isValid3 = regex.IsMatch("hsla(240, 100%, 50%, 0)");  // true (transparent blue)
    ///
    /// // Invalid HSLA colors
    /// bool isValid4 = regex.IsMatch("hsla(360,100%,50%,1)");     // false (hue >= 360)
    /// bool isValid5 = regex.IsMatch("hsla(0,100%,50%,1.1)");     // false (alpha > 1)
    /// bool isValid6 = regex.IsMatch("hsla(0,100%,50%)");         // false (missing alpha)
    /// </code>
    /// </remarks>
    /// <returns>A compiled <see href="https://learn.microsoft.com/en-us/dotnet/api/system.text.regularexpressions.regex">Regex</see> instance for HSLA color validation.</returns>
    [GeneratedRegex(
        pattern: @"^hsla\(\s*(\d{1,3})\s*,\s*(\d{1,3})%\s*,\s*(\d{1,3})%\s*,\s*([01]|0?\.\d+)\s*\)$",
        options: RegexOptions.Compiled
    )]
    public static partial Regex HslaColorValidation();

    /// <summary>
    /// Validates XML tags using a compiled regular expression pattern.
    /// </summary>
    /// <remarks>
    /// Validates XML tags including:
    /// - Opening and closing tags
    /// - Self-closing tags
    /// - Tags with attributes
    ///
    /// > [!CAUTION]
    /// > This is a basic XML validation. For complete XML validation, use an XML parser.
    ///
    /// Example usage:
    /// <code>
    /// var regex = RegexPatternCollections.XmlTagValidation();
    ///
    /// // Valid XML tags
    /// bool isValid1 = regex.IsMatch("<tag></tag>");                  // true
    /// bool isValid2 = regex.IsMatch("<img src='image.jpg' />");      // true
    /// bool isValid3 = regex.IsMatch("<div class='content'></div>");  // true
    ///
    /// // Invalid XML tags
    /// bool isValid4 = regex.IsMatch("<tag>");                        // false (no closing tag)
    /// bool isValid5 = regex.IsMatch("<tag/>");                       // false (invalid self-closing)
    /// bool isValid6 = regex.IsMatch("<123></123>");                  // false (invalid tag name)
    /// </code>
    /// </remarks>
    /// <returns>A compiled <see href="https://learn.microsoft.com/en-us/dotnet/api/system.text.regularexpressions.regex">Regex</see> instance for XML tag validation.</returns>
    [GeneratedRegex(
        pattern: @"<([a-z]+)([^<]+)*(?:>(.*)<\/\1>|\s+\/>)",
        options: RegexOptions.Compiled
    )]
    public static partial Regex XmlTagValidation();

    /// <summary>
    /// Validates Markdown link syntax using a compiled regular expression pattern.
    /// </summary>
    /// <remarks>
    /// Validates standard Markdown links in the format [text](url).
    ///
    /// > [!TIP]
    /// > This pattern matches both internal and external links.
    ///
    /// Example usage:
    /// <code>
    /// var regex = RegexPatternCollections.MarkdownLinkValidation();
    ///
    /// // Valid Markdown links
    /// bool isValid1 = regex.IsMatch("[Google](https://google.com)");           // true
    /// bool isValid2 = regex.IsMatch("[Local Page](/docs/page.md)");           // true
    /// bool isValid3 = regex.IsMatch("[Complex Title!](path/to/file.pdf)");    // true
    ///
    /// // Invalid Markdown links
    /// bool isValid4 = regex.IsMatch("[Broken Link](");                        // false
    /// bool isValid5 = regex.IsMatch("(https://example.com)");                 // false
    /// bool isValid6 = regex.IsMatch("[Missing Parentheses]");                 // false
    /// </code>
    /// </remarks>
    /// <returns>A compiled <see href="https://learn.microsoft.com/en-us/dotnet/api/system.text.regularexpressions.regex">Regex</see> instance for Markdown link validation.</returns>
    [GeneratedRegex(pattern: @"\[([^\]]+)\]\(([^)]+)\)", options: RegexOptions.Compiled)]
    public static partial Regex MarkdownLinkValidation();

    /// <summary>
    /// Validates Markdown image syntax using a compiled regular expression pattern.
    /// </summary>
    /// <remarks>
    /// Validates Markdown image syntax in the format ![alt text](image_url).
    ///
    /// > [!TIP]
    /// > This pattern matches both local and remote image references.
    ///
    /// Example usage:
    /// <code>
    /// var regex = RegexPatternCollections.MarkdownImageValidation();
    ///
    /// // Valid Markdown images
    /// bool isValid1 = regex.IsMatch("![Logo](/images/logo.png)");             // true
    /// bool isValid2 = regex.IsMatch("![Alt](https://site.com/img.jpg)");     // true
    /// bool isValid3 = regex.IsMatch("![Complex Alt!](./local/pic.gif)");     // true
    ///
    /// // Invalid Markdown images
    /// bool isValid4 = regex.IsMatch("![Broken Image](");                      // false
    /// bool isValid5 = regex.IsMatch("(image.jpg)");                          // false
    /// bool isValid6 = regex.IsMatch("![No URL]");                            // false
    /// </code>
    /// </remarks>
    /// <returns>A compiled <see href="https://learn.microsoft.com/en-us/dotnet/api/system.text.regularexpressions.regex">Regex</see> instance for Markdown image validation.</returns>
    [GeneratedRegex(pattern: @"!\[([^\]]+)\]\(([^)]+)\)", options: RegexOptions.Compiled)]
    public static partial Regex MarkdownImageValidation();

    /// <summary>
    /// Validates Twitter handle format using a compiled regular expression pattern.
    /// </summary>
    /// <remarks>
    /// Validates Twitter handles that:
    /// - Start with @
    /// - Contain only letters, numbers, and underscores
    /// - Are between 1 and 15 characters long (excluding @)
    ///
    /// > [!NOTE]
    /// > Twitter's maximum username length is 15 characters.
    ///
    /// Example usage:
    /// <code>
    /// var regex = RegexPatternCollections.TwitterHandleValidation();
    ///
    /// // Valid Twitter handles
    /// bool isValid1 = regex.IsMatch("@user123");                             // true
    /// bool isValid2 = regex.IsMatch("@Developer_");                          // true
    /// bool isValid3 = regex.IsMatch("@UPPERCASE");                           // true
    ///
    /// // Invalid Twitter handles
    /// bool isValid4 = regex.IsMatch("@toolong16characters");                 // false
    /// bool isValid5 = regex.IsMatch("@special-char");                        // false
    /// bool isValid6 = regex.IsMatch("user123");                             // false (missing @)
    /// </code>
    /// </remarks>
    /// <returns>A compiled <see href="https://learn.microsoft.com/en-us/dotnet/api/system.text.regularexpressions.regex">Regex</see> instance for Twitter handle validation.</returns>
    [GeneratedRegex(pattern: @"^@[A-Za-z0-9_]{1,15}$", options: RegexOptions.Compiled)]
    public static partial Regex TwitterHandleValidation();

    /// <summary>
    /// Validates Instagram handle format using a compiled regular expression pattern.
    /// </summary>
    /// <remarks>
    /// Validates Instagram handles that:
    /// - Start with @
    /// - Can contain letters, numbers, periods, and underscores
    /// - Cannot have consecutive periods
    /// - Cannot end with a period
    /// - Maximum 30 characters (excluding @)
    ///
    /// > [!NOTE]
    /// > Instagram usernames are case-insensitive but this validation preserves case.
    ///
    /// Example usage:
    /// <code>
    /// var regex = RegexPatternCollections.InstagramHandleValidation();
    ///
    /// // Valid Instagram handles
    /// bool isValid1 = regex.IsMatch("@user.name");                          // true
    /// bool isValid2 = regex.IsMatch("@user_name123");                       // true
    /// bool isValid3 = regex.IsMatch("@company.official");                   // true
    ///
    /// // Invalid Instagram handles
    /// bool isValid4 = regex.IsMatch("@user..name");                        // false (consecutive periods)
    /// bool isValid5 = regex.IsMatch("@username.");                         // false (ends with period)
    /// bool isValid6 = regex.IsMatch("@special-chars");                     // false (invalid character)
    /// </code>
    /// </remarks>
    /// <returns>A compiled <see href="https://learn.microsoft.com/en-us/dotnet/api/system.text.regularexpressions.regex">Regex</see> instance for Instagram handle validation.</returns>
    [GeneratedRegex(
        pattern: @"^@(?!.*\.\.)(?!.*\.$)[^\W][\w.]{0,29}$",
        options: RegexOptions.Compiled
    )]
    public static partial Regex InstagramHandleValidation();

    /// <summary>
    /// Validates YouTube video ID format using a compiled regular expression pattern.
    /// </summary>
    /// <remarks>
    /// Validates YouTube video IDs that:
    /// - Are exactly 11 characters long
    /// - Contain only letters, numbers, underscores, and hyphens
    ///
    /// > [!NOTE]
    /// > This validates the format only, not the existence of the video.
    ///
    /// Example usage:
    /// <code>
    /// var regex = RegexPatternCollections.YoutubeVideoIdValidation();
    ///
    /// // Valid YouTube video IDs
    /// bool isValid1 = regex.IsMatch("dQw4w9WgXcQ");                        // true
    /// bool isValid2 = regex.IsMatch("a1b2c3-d4e5");                        // true
    /// bool isValid3 = regex.IsMatch("AB_CD_EF_GH");                        // true
    ///
    /// // Invalid YouTube video IDs
    /// bool isValid4 = regex.IsMatch("tooshort");                           // false
    /// bool isValid5 = regex.IsMatch("toolong12345");                       // false
    /// bool isValid6 = regex.IsMatch("invalid#chars");                      // false
    /// </code>
    /// </remarks>
    /// <returns>A compiled <see href="https://learn.microsoft.com/en-us/dotnet/api/system.text.regularexpressions.regex">Regex</see> instance for YouTube video ID validation.</returns>
    [GeneratedRegex(pattern: @"^[a-zA-Z0-9_-]{11}$", options: RegexOptions.Compiled)]
    public static partial Regex YoutubeVideoIdValidation();

    /// <summary>
    /// Validates Bitcoin address format using a compiled regular expression pattern.
    /// </summary>
    /// <remarks>
    /// Validates Bitcoin addresses that:
    /// - Start with 1 or 3
    /// - Are 26-34 characters long
    /// - Contain base58 characters (excluding 0, O, I, l)
    ///
    /// > [!IMPORTANT]
    /// > This validates the format only, not the checksum or actual validity of the address.
    ///
    /// Example usage:
    /// <code>
    /// var regex = RegexPatternCollections.BitcoinAddressValidation();
    ///
    /// // Valid Bitcoin addresses
    /// bool isValid1 = regex.IsMatch("**********************************");   // true
    /// bool isValid2 = regex.IsMatch("**********************************");   // true
    ///
    /// // Invalid Bitcoin addresses
    /// bool isValid3 = regex.IsMatch("1InvalidAddress");                       // false
    /// bool isValid4 = regex.IsMatch("4ValidCharsButWrongPrefix");            // false
    /// </code>
    /// </remarks>
    /// <returns>A compiled <see href="https://learn.microsoft.com/en-us/dotnet/api/system.text.regularexpressions.regex">Regex</see> instance for Bitcoin address validation.</returns>
    [GeneratedRegex(pattern: @"^[13][a-km-zA-HJ-NP-Z1-9]{25,34}$", options: RegexOptions.Compiled)]
    public static partial Regex BitcoinAddressValidation();

    /// <summary>
    /// Validates Ethereum address format using a compiled regular expression pattern.
    /// </summary>
    /// <remarks>
    /// Validates Ethereum addresses that:
    /// - Start with "0x"
    /// - Followed by exactly 40 hexadecimal characters
    ///
    /// > [!IMPORTANT]
    /// > This validates the format only, not the checksum or actual validity of the address.
    ///
    /// Example usage:
    /// <code>
    /// var regex = RegexPatternCollections.EthereumAddressValidation();
    ///
    /// // Valid Ethereum addresses
    /// bool isValid1 = regex.IsMatch("******************************************");  // true
    /// bool isValid2 = regex.IsMatch("******************************************");  // true
    ///
    /// // Invalid Ethereum addresses
    /// bool isValid3 = regex.IsMatch("742d35Cc6634C0532925a3b844Bc454e4438f44e");    // false (no 0x)
    /// bool isValid4 = regex.IsMatch("0xInvalidAddress");                             // false
    /// </code>
    /// </remarks>
    /// <returns>A compiled <see href="https://learn.microsoft.com/en-us/dotnet/api/system.text.regularexpressions.regex">Regex</see> instance for Ethereum address validation.</returns>
    [GeneratedRegex(pattern: @"^0x[a-fA-F0-9]{40}$", options: RegexOptions.Compiled)]
    public static partial Regex EthereumAddressValidation();

    /// <summary>
    /// Validates International Bank Account Number (IBAN) format using a compiled regular expression pattern.
    /// </summary>
    /// <remarks>
    /// Validates IBAN numbers that:
    /// - Start with a 2-letter country code
    /// - Followed by 2 check digits
    /// - Contains 9-30 additional characters
    /// - Can have optional spaces or hyphens between groups
    ///
    /// > [!IMPORTANT]
    /// > This validates the format only, not the actual validity or checksum of the IBAN.
    ///
    /// Example usage:
    /// <code>
    /// var regex = RegexPatternCollections.IbanValidation();
    ///
    /// // Valid IBAN formats
    /// bool isValid1 = regex.IsMatch("GB82 WEST 1234 5698 7654 32");        // true
    /// bool isValid2 = regex.IsMatch("**********************");            // true
    /// bool isValid3 = regex.IsMatch("FR14-2004-1010-0505-0001-3M02-606"); // true
    ///
    /// // Invalid IBAN formats
    /// bool isValid4 = regex.IsMatch("GB82");                              // false (too short)
    /// bool isValid5 = regex.IsMatch("*********");                         // false (invalid format)
    /// </code>
    /// </remarks>
    /// <returns>A compiled <see href="https://learn.microsoft.com/en-us/dotnet/api/system.text.regularexpressions.regex">Regex</see> instance for IBAN validation.</returns>
    [GeneratedRegex(
        pattern: @"^([A-Z]{2}[ \-]?[0-9]{2})(?=(?:[ \-]?[A-Z0-9]){9,30}$)((?:[ \-]?[A-Z0-9]{3,5}){2,7})([ \-]?[A-Z0-9]{1,3})?$",
        options: RegexOptions.Compiled
    )]
    public static partial Regex IbanValidation();

    /// <summary>
    /// Validates SWIFT/BIC code format using a compiled regular expression pattern.
    /// </summary>
    /// <remarks>
    /// Validates SWIFT/BIC codes that:
    /// - Are 8 or 11 characters long
    /// - First 6 characters are letters (bank and country/location code)
    /// - Followed by 2 alphanumeric characters (branch location)
    /// - Optional 3 alphanumeric characters (branch code)
    ///
    /// > [!IMPORTANT]
    /// > This validates the format only, not the actual validity of the SWIFT/BIC code.
    ///
    /// Example usage:
    /// <code>
    /// var regex = RegexPatternCollections.SwiftCodeValidation();
    ///
    /// // Valid SWIFT/BIC codes
    /// bool isValid1 = regex.IsMatch("DEUTDEFF");                          // true (8 characters)
    /// bool isValid2 = regex.IsMatch("DEUTDEFF500");                      // true (11 characters)
    /// bool isValid3 = regex.IsMatch("UNCRIT2B912");                      // true (with branch code)
    ///
    /// // Invalid SWIFT/BIC codes
    /// bool isValid4 = regex.IsMatch("DEUT123F");                         // false (invalid format)
    /// bool isValid5 = regex.IsMatch("DEUTDEFF5");                        // false (9 characters)
    /// </code>
    /// </remarks>
    /// <returns>A compiled <see href="https://learn.microsoft.com/en-us/dotnet/api/system.text.regularexpressions.regex">Regex</see> instance for SWIFT/BIC code validation.</returns>
    [GeneratedRegex(
        pattern: @"^[A-Z]{6}[A-Z0-9]{2}([A-Z0-9]{3})?$",
        options: RegexOptions.Compiled
    )]
    public static partial Regex SwiftCodeValidation();
    #endregion

    #region Banking Validation
    /// <summary>
    /// Validates general bank account number format using a compiled regular expression pattern.
    /// </summary>
    /// <remarks>
    /// Validates bank account numbers that contain 10-20 digits.
    ///
    /// Example usage:
    /// <code>
    /// var regex = RegexPatternCollections.BankAccountNumberValidation();
    ///
    /// // Valid bank account numbers
    /// bool isValid1 = regex.IsMatch("*********0");       // true (10 digits)
    /// bool isValid2 = regex.IsMatch("***************67890"); // true (20 digits)
    ///
    /// // Invalid bank account numbers
    /// bool isValid3 = regex.IsMatch("*********");        // false (too short)
    /// bool isValid4 = regex.IsMatch("123abc456");        // false (contains letters)
    /// </code>
    /// </remarks>
    /// <returns>A compiled <see href="https://learn.microsoft.com/en-us/dotnet/api/system.text.regularexpressions.regex">Regex</see> instance for bank account number validation.</returns>
    [GeneratedRegex(pattern: @"^\d{10,20}$", options: RegexOptions.Compiled)]
    public static partial Regex BankAccountNumberValidation();

    /// <summary>
    /// Validates Indonesian bank account number format using a compiled regular expression pattern.
    /// </summary>
    /// <remarks>
    /// Validates Indonesian bank account numbers that contain 10-16 digits.
    ///
    /// > [!NOTE]
    /// > Most Indonesian banks use account numbers within this range.
    ///
    /// Example usage:
    /// <code>
    /// var regex = RegexPatternCollections.IndonesianBankAccountValidation();
    ///
    /// // Valid Indonesian bank account numbers
    /// bool isValid1 = regex.IsMatch("*********0");     // true (10 digits)
    /// bool isValid2 = regex.IsMatch("***************6"); // true (16 digits)
    ///
    /// // Invalid Indonesian bank account numbers
    /// bool isValid3 = regex.IsMatch("*********");      // false (too short)
    /// bool isValid4 = regex.IsMatch("***************67"); // false (too long)
    /// </code>
    /// </remarks>
    /// <returns>A compiled <see href="https://learn.microsoft.com/en-us/dotnet/api/system.text.regularexpressions.regex">Regex</see> instance for Indonesian bank account validation.</returns>
    [GeneratedRegex(pattern: @"^\d{10,16}$", options: RegexOptions.Compiled)]
    public static partial Regex IndonesianBankAccountValidation();

    /// <summary>
    /// Validates reference number format using a compiled regular expression pattern.
    /// </summary>
    /// <remarks>
    /// Validates reference numbers that:
    /// - Contain 6-20 characters
    /// - Allow uppercase letters and numbers only
    ///
    /// Example usage:
    /// <code>
    /// var regex = RegexPatternCollections.ReferenceNumberValidation();
    ///
    /// // Valid reference numbers
    /// bool isValid1 = regex.IsMatch("REF123");         // true
    /// bool isValid2 = regex.IsMatch("REFERENCE*********"); // true
    ///
    /// // Invalid reference numbers
    /// bool isValid3 = regex.IsMatch("REF12");          // false (too short)
    /// bool isValid4 = regex.IsMatch("ref123");         // false (lowercase)
    /// </code>
    /// </remarks>
    /// <returns>A compiled <see href="https://learn.microsoft.com/en-us/dotnet/api/system.text.regularexpressions.regex">Regex</see> instance for reference number validation.</returns>
    [GeneratedRegex(pattern: @"^[A-Z0-9]{6,20}$", options: RegexOptions.Compiled)]
    public static partial Regex ReferenceNumberValidation();

    /// <summary>
    /// Validates transaction ID format using a compiled regular expression pattern.
    /// </summary>
    /// <remarks>
    /// Validates transaction IDs that:
    /// - Contain 8-32 characters
    /// - Allow uppercase letters and numbers only
    ///
    /// Example usage:
    /// <code>
    /// var regex = RegexPatternCollections.TransactionIdValidation();
    ///
    /// // Valid transaction IDs
    /// bool isValid1 = regex.IsMatch("TRX12345");       // true
    /// bool isValid2 = regex.IsMatch("TRANSACTION*********ABCDEF"); // true
    ///
    /// // Invalid transaction IDs
    /// bool isValid3 = regex.IsMatch("TRX123");         // false (too short)
    /// bool isValid4 = regex.IsMatch("trx12345");       // false (lowercase)
    /// </code>
    /// </remarks>
    /// <returns>A compiled <see href="https://learn.microsoft.com/en-us/dotnet/api/system.text.regularexpressions.regex">Regex</see> instance for transaction ID validation.</returns>
    [GeneratedRegex(pattern: @"^[A-Z0-9]{8,32}$", options: RegexOptions.Compiled)]
    public static partial Regex TransactionIdValidation();

    /// <summary>
    /// Validates virtual account number format using a compiled regular expression pattern.
    /// </summary>
    /// <remarks>
    /// Validates virtual account numbers that:
    /// - Start with a 3-digit bank code
    /// - Followed by 8-16 digits
    ///
    /// Example usage:
    /// <code>
    /// var regex = RegexPatternCollections.VirtualAccountNumberValidation();
    ///
    /// // Valid virtual account numbers
    /// bool isValid1 = regex.IsMatch("**********8");    // true
    /// bool isValid2 = regex.IsMatch("002*********0123"); // true
    ///
    /// // Invalid virtual account numbers
    /// bool isValid3 = regex.IsMatch("**********");     // false (too short)
    /// bool isValid4 = regex.IsMatch("ABC12345678");    // false (invalid format)
    /// </code>
    /// </remarks>
    /// <returns>A compiled <see href="https://learn.microsoft.com/en-us/dotnet/api/system.text.regularexpressions.regex">Regex</see> instance for virtual account number validation.</returns>
    [GeneratedRegex(pattern: @"^\d{3}\d{8,16}$", options: RegexOptions.Compiled)]
    public static partial Regex VirtualAccountNumberValidation();

    /// <summary>
    /// Validates bank code format using a compiled regular expression pattern.
    /// </summary>
    /// <remarks>
    /// Validates 3-digit bank codes used in Indonesia.
    ///
    /// Example usage:
    /// <code>
    /// var regex = RegexPatternCollections.BankCodeValidation();
    ///
    /// // Valid bank codes
    /// bool isValid1 = regex.IsMatch("002");            // true (BRI)
    /// bool isValid2 = regex.IsMatch("014");            // true (BCA)
    ///
    /// // Invalid bank codes
    /// bool isValid3 = regex.IsMatch("02");             // false (too short)
    /// bool isValid4 = regex.IsMatch("ABC");            // false (not numeric)
    /// </code>
    /// </remarks>
    /// <returns>A compiled <see href="https://learn.microsoft.com/en-us/dotnet/api/system.text.regularexpressions.regex">Regex</see> instance for bank code validation.</returns>
    [GeneratedRegex(pattern: @"^\d{3}$", options: RegexOptions.Compiled)]
    public static partial Regex BankCodeValidation();

    /// <summary>
    /// Validates branch code format using a compiled regular expression pattern.
    /// </summary>
    /// <remarks>
    /// Validates 4-digit branch codes used by banks.
    ///
    /// Example usage:
    /// <code>
    /// var regex = RegexPatternCollections.BranchCodeValidation();
    ///
    /// // Valid branch codes
    /// bool isValid1 = regex.IsMatch("0001");           // true
    /// bool isValid2 = regex.IsMatch("9999");           // true
    ///
    /// // Invalid branch codes
    /// bool isValid3 = regex.IsMatch("001");            // false (too short)
    /// bool isValid4 = regex.IsMatch("ABCD");           // false (not numeric)
    /// </code>
    /// </remarks>
    /// <returns>A compiled <see href="https://learn.microsoft.com/en-us/dotnet/api/system.text.regularexpressions.regex">Regex</see> instance for branch code validation.</returns>
    [GeneratedRegex(pattern: @"^\d{4}$", options: RegexOptions.Compiled)]
    public static partial Regex BranchCodeValidation();

    /// <summary>
    /// Validates Indonesian currency format using a compiled regular expression pattern.
    /// </summary>
    /// <remarks>
    /// Validates Indonesian Rupiah amounts that:
    /// - Start with "Rp"
    /// - Optional space after "Rp"
    /// - Use dots as thousand separators
    /// - Optional 2 decimal places with comma separator
    ///
    /// Example usage:
    /// <code>
    /// var regex = RegexPatternCollections.IndonesianCurrencyValidation();
    ///
    /// // Valid Indonesian currency formats
    /// bool isValid1 = regex.IsMatch("Rp1.000");        // true
    /// bool isValid2 = regex.IsMatch("Rp 1.000.000,50"); // true
    ///
    /// // Invalid Indonesian currency formats
    /// bool isValid3 = regex.IsMatch("1000");           // false (no Rp)
    /// bool isValid4 = regex.IsMatch("Rp1,000.00");     // false (wrong separators)
    /// </code>
    /// </remarks>
    /// <returns>A compiled <see href="https://learn.microsoft.com/en-us/dotnet/api/system.text.regularexpressions.regex">Regex</see> instance for Indonesian currency validation.</returns>
    [GeneratedRegex(
        pattern: @"^Rp\s?\d{1,3}(?:\.\d{3})*(?:,\d{2})?$",
        options: RegexOptions.Compiled
    )]
    public static partial Regex IndonesianCurrencyValidation();

    /// <summary>
    /// Validates bill key format using a compiled regular expression pattern.
    /// </summary>
    /// <remarks>
    /// Validates bill keys that contain 6-20 digits.
    ///
    /// Example usage:
    /// <code>
    /// var regex = RegexPatternCollections.BillKeyValidation();
    ///
    /// // Valid bill keys
    /// bool isValid1 = regex.IsMatch("123456");         // true
    /// bool isValid2 = regex.IsMatch("***************67890"); // true
    ///
    /// // Invalid bill keys
    /// bool isValid3 = regex.IsMatch("12345");          // false (too short)
    /// bool isValid4 = regex.IsMatch("ABCDEF");         // false (not numeric)
    /// </code>
    /// </remarks>
    /// <returns>A compiled <see href="https://learn.microsoft.com/en-us/dotnet/api/system.text.regularexpressions.regex">Regex</see> instance for bill key validation.</returns>
    [GeneratedRegex(pattern: @"^\d{6,20}$", options: RegexOptions.Compiled)]
    public static partial Regex BillKeyValidation();

    /// <summary>
    /// Validates biller code format using a compiled regular expression pattern.
    /// </summary>
    /// <remarks>
    /// Validates biller codes that contain 3-10 digits.
    ///
    /// Example usage:
    /// <code>
    /// var regex = RegexPatternCollections.BillerCodeValidation();
    ///
    /// // Valid biller codes
    /// bool isValid1 = regex.IsMatch("123");            // true
    /// bool isValid2 = regex.IsMatch("*********0");     // true
    ///
    /// // Invalid biller codes
    /// bool isValid3 = regex.IsMatch("12");             // false (too short)
    /// bool isValid4 = regex.IsMatch("ABC");            // false (not numeric)
    /// </code>
    /// </remarks>
    /// <returns>A compiled <see href="https://learn.microsoft.com/en-us/dotnet/api/system.text.regularexpressions.regex">Regex</see> instance for biller code validation.</returns>
    [GeneratedRegex(pattern: @"^\d{3,10}$", options: RegexOptions.Compiled)]
    public static partial Regex BillerCodeValidation();

    /// <summary>
    /// Validates customer number format using a compiled regular expression pattern.
    /// </summary>
    /// <remarks>
    /// Validates customer numbers that contain 6-20 digits.
    ///
    /// Example usage:
    /// <code>
    /// var regex = RegexPatternCollections.CustomerNumberValidation();
    ///
    /// // Valid customer numbers
    /// bool isValid1 = regex.IsMatch("123456");         // true
    /// bool isValid2 = regex.IsMatch("***************67890"); // true
    ///
    /// // Invalid customer numbers
    /// bool isValid3 = regex.IsMatch("12345");          // false (too short)
    /// bool isValid4 = regex.IsMatch("ABCDEF");         // false (not numeric)
    /// </code>
    /// </remarks>
    /// <returns>A compiled <see href="https://learn.microsoft.com/en-us/dotnet/api/system.text.regularexpressions.regex">Regex</see> instance for customer number validation.</returns>
    [GeneratedRegex(pattern: @"^\d{6,20}$", options: RegexOptions.Compiled)]
    public static partial Regex CustomerNumberValidation();

    /// <summary>
    /// Validates Indonesian Tax ID (NPWP) format using a compiled regular expression pattern.
    /// </summary>
    /// <remarks>
    /// Validates NPWP numbers in the format: XX.XXX.XXX.X-XXX.XXX
    ///
    /// Example usage:
    /// <code>
    /// var regex = RegexPatternCollections.NpwpValidation();
    ///
    /// // Valid NPWP
    /// bool isValid1 = regex.IsMatch("12.345.678.9-012.345"); // true
    ///
    /// // Invalid NPWP
    /// bool isValid2 = regex.IsMatch("***************");      // false (wrong format)
    /// bool isValid3 = regex.IsMatch("12.345.678.9012.345");  // false (missing hyphen)
    /// </code>
    /// </remarks>
    /// <returns>A compiled <see href="https://learn.microsoft.com/en-us/dotnet/api/system.text.regularexpressions.regex">Regex</see> instance for NPWP validation.</returns>
    [GeneratedRegex(
        pattern: @"^\d{2}\.\d{3}\.\d{3}\.\d{1}-\d{3}\.\d{3}$",
        options: RegexOptions.Compiled
    )]
    public static partial Regex NpwpValidation();

    /// <summary>
    /// Validates Indonesian ID Card (KTP) number format using a compiled regular expression pattern.
    /// </summary>
    /// <remarks>
    /// Validates KTP numbers that contain exactly 16 digits.
    ///
    /// Example usage:
    /// <code>
    /// var regex = RegexPatternCollections.KtpValidation();
    ///
    /// // Valid KTP numbers
    /// bool isValid1 = regex.IsMatch("***************6"); // true
    ///
    /// // Invalid KTP numbers
    /// bool isValid2 = regex.IsMatch("***************");  // false (too short)
    /// bool isValid3 = regex.IsMatch("***************67"); // false (too long)
    /// </code>
    /// </remarks>
    /// <returns>A compiled <see href="https://learn.microsoft.com/en-us/dotnet/api/system.text.regularexpressions.regex">Regex</see> instance for KTP validation.</returns>
    [GeneratedRegex(pattern: @"^\d{16}$", options: RegexOptions.Compiled)]
    public static partial Regex KtpValidation();

    /// <summary>
    /// Validates passport number format using a compiled regular expression pattern.
    /// </summary>
    /// <remarks>
    /// Validates passport numbers that:
    /// - Start with 1-2 uppercase letters
    /// - Followed by 6-7 digits
    ///
    /// Example usage:
    /// <code>
    /// var regex = RegexPatternCollections.PassportNumberValidation();
    ///
    /// // Valid passport numbers
    /// bool isValid1 = regex.IsMatch("A123456");        // true
    /// bool isValid2 = regex.IsMatch("*********");      // true
    ///
    /// // Invalid passport numbers
    /// bool isValid3 = regex.IsMatch("123456");         // false (no letters)
    /// bool isValid4 = regex.IsMatch("ABC123456");      // false (too many letters)
    /// </code>
    /// </remarks>
    /// <returns>A compiled <see href="https://learn.microsoft.com/en-us/dotnet/api/system.text.regularexpressions.regex">Regex</see> instance for passport number validation.</returns>
    [GeneratedRegex(pattern: @"^[A-Z]{1,2}\d{6,7}$", options: RegexOptions.Compiled)]
    public static partial Regex PassportNumberValidation();

    /// <summary>
    /// Validates One-Time Password (OTP) format using a compiled regular expression pattern.
    /// </summary>
    /// <remarks>
    /// Validates 6-digit OTP codes.
    ///
    /// Example usage:
    /// <code>
    /// var regex = RegexPatternCollections.OtpValidation();
    ///
    /// // Valid OTP codes
    /// bool isValid1 = regex.IsMatch("123456");         // true
    ///
    /// // Invalid OTP codes
    /// bool isValid2 = regex.IsMatch("12345");          // false (too short)
    /// bool isValid3 = regex.IsMatch("1234567");        // false (too long)
    /// bool isValid4 = regex.IsMatch("12345!@#");       // false (contains non-numeric characters)
    /// </code>
    /// </remarks>
    /// <returns>A compiled <see href="https://learn.microsoft.com/en-us/dotnet/api/system.text.regularexpressions.regex">Regex</see> instance for OTP validation.</returns>
    [GeneratedRegex(pattern: @"^\d{6}$", options: RegexOptions.Compiled)]
    public static partial Regex OtpValidation();

    /// <summary>
    /// Validates mobile banking PIN format using a compiled regular expression pattern.
    /// </summary>
    /// <remarks>
    /// Validates 6-digit mobile banking PINs.
    ///
    /// Example usage:
    /// <code>
    /// var regex = RegexPatternCollections.MobileBankingPinValidation();
    ///
    /// // Valid mobile banking PINs
    /// bool isValid1 = regex.IsMatch("123456");         // true
    ///
    /// // Invalid mobile banking PINs
    /// bool isValid2 = regex.IsMatch("12345");          // false (too short)
    /// bool isValid3 = regex.IsMatch("1234567");        // false (too long)
    /// bool isValid4 = regex.IsMatch("123!@#");         // false (contains non-numeric characters)
    /// </code>
    /// </remarks>
    /// <returns>A compiled <see href="https://learn.microsoft.com/en-us/dotnet/api/system.text.regularexpressions.regex">Regex</see> instance for mobile banking PIN validation.</returns>
    [GeneratedRegex(pattern: @"^\d{6}$", options: RegexOptions.Compiled)]
    public static partial Regex MobileBankingPinValidation();

    /// <summary>
    /// Validates CVV (Card Verification Value) format using a compiled regular expression pattern.
    /// </summary>
    /// <remarks>
    /// Validates CVV numbers that:
    /// - Contain 3 or 4 digits
    ///
    /// Example usage:
    /// <code>
    /// var regex = RegexPatternCollections.CvvValidation();
    ///
    /// // Valid CVV numbers
    /// bool isValid1 = regex.IsMatch("123");            // true
    /// bool isValid2 = regex.IsMatch("1234");           // true
    ///
    /// // Invalid CVV numbers
    /// bool isValid3 = regex.IsMatch("12");             // false (too short)
    /// bool isValid4 = regex.IsMatch("123!@#");         // false (contains non-numeric characters)
    /// bool isValid5 = regex.IsMatch("12345");          // false (too long)
    /// </code>
    /// </remarks>
    /// <returns>A compiled <see href="https://learn.microsoft.com/en-us/dotnet/api/system.text.regularexpressions.regex">Regex</see> instance for CVV validation.</returns>
    [GeneratedRegex(pattern: @"^\d{3,4}$", options: RegexOptions.Compiled)]
    public static partial Regex CvvValidation();

    /// <summary>
    /// Validates credit/debit card expiry date format using a compiled regular expression pattern.
    /// </summary>
    /// <remarks>
    /// Validates expiry dates in MM/YY format where:
    /// - MM must be 01-12
    /// - YY must be 2 digits
    ///
    /// Example usage:
    /// <code>
    /// var regex = RegexPatternCollections.CardExpiryValidation();
    ///
    /// // Valid expiry dates
    /// bool isValid1 = regex.IsMatch("12/25");          // true
    /// bool isValid2 = regex.IsMatch("01/30");          // true
    ///
    /// // Invalid expiry dates
    /// bool isValid3 = regex.IsMatch("13/25");          // false (invalid month)
    /// bool isValid4 = regex.IsMatch("00/25");          // false (invalid month)
    /// bool isValid5 = regex.IsMatch("12/2025");        // false (year too long)
    /// </code>
    /// </remarks>
    /// <returns>A compiled <see href="https://learn.microsoft.com/en-us/dotnet/api/system.text.regularexpressions.regex">Regex</see> instance for card expiry validation.</returns>
    [GeneratedRegex(pattern: @"^(0[1-9]|1[0-2])\/([0-9]{2})$", options: RegexOptions.Compiled)]
    public static partial Regex CardExpiryValidation();

    /// <summary>
    /// Validates bank transfer code format using a compiled regular expression pattern.
    /// </summary>
    /// <remarks>
    /// Validates transfer codes in format: XXX/XXX/XXXXX where X are digits.
    ///
    /// Example usage:
    /// <code>
    /// var regex = RegexPatternCollections.BankTransferCodeValidation();
    ///
    /// // Valid transfer codes
    /// bool isValid1 = regex.IsMatch("123/456/78901");  // true
    ///
    /// // Invalid transfer codes
    /// bool isValid2 = regex.IsMatch("123456/78901");   // false (wrong format)
    /// bool isValid3 = regex.IsMatch("123/456/7890");   // false (last part too short)
    /// </code>
    /// </remarks>
    /// <returns>A compiled <see href="https://learn.microsoft.com/en-us/dotnet/api/system.text.regularexpressions.regex">Regex</see> instance for bank transfer code validation.</returns>
    [GeneratedRegex(pattern: @"^\d{3}\/\d{3}\/\d{5}$", options: RegexOptions.Compiled)]
    public static partial Regex BankTransferCodeValidation();

    /// <summary>
    /// Validates e-money card number format using a compiled regular expression pattern.
    /// </summary>
    /// <remarks>
    /// Validates 16-digit e-money card numbers.
    ///
    /// Example usage:
    /// <code>
    /// var regex = RegexPatternCollections.EmoneyCardNumberValidation();
    ///
    /// // Valid e-money card numbers
    /// bool isValid1 = regex.IsMatch("***************6");  // true
    ///
    /// // Invalid e-money card numbers
    /// bool isValid2 = regex.IsMatch("***************");   // false (too short)
    /// bool isValid3 = regex.IsMatch("***************67"); // false (too long)
    /// </code>
    /// </remarks>
    /// <returns>A compiled <see href="https://learn.microsoft.com/en-us/dotnet/api/system.text.regularexpressions.regex">Regex</see> instance for e-money card number validation.</returns>
    [GeneratedRegex(pattern: @"^\d{16}$", options: RegexOptions.Compiled)]
    public static partial Regex EmoneyCardNumberValidation();

    /// <summary>
    /// Validates QR payment code format using a compiled regular expression pattern.
    /// </summary>
    /// <remarks>
    /// Validates QR payment codes that:
    /// - Contain at least 8 alphanumeric characters (0-9, A-Z)
    /// - Only uppercase letters are allowed
    ///
    /// > [!NOTE]
    /// > The minimum length is 8 characters, but there is no maximum length restriction.
    ///
    /// Example usage:
    /// <code>
    /// var regex = RegexPatternCollections.QrPaymentCodeValidation();
    ///
    /// // Valid QR payment codes
    /// bool isValid1 = regex.IsMatch("12345678");         // true
    /// bool isValid2 = regex.IsMatch("ABCD1234");         // true
    /// bool isValid3 = regex.IsMatch("1234ABCD5678EFGH"); // true
    ///
    /// // Invalid QR payment codes
    /// bool isValid4 = regex.IsMatch("1234567");          // false (too short)
    /// bool isValid5 = regex.IsMatch("abcd1234");         // false (lowercase letters)
    /// bool isValid6 = regex.IsMatch("1234!@#$");         // false (special characters)
    /// </code>
    /// </remarks>
    /// <returns>A compiled <see href="https://learn.microsoft.com/en-us/dotnet/api/system.text.regularexpressions.regex">Regex</see> instance for QR payment code validation.</returns>
    [GeneratedRegex(pattern: @"^[0-9A-Z]{8,}$", options: RegexOptions.Compiled)]
    public static partial Regex QrPaymentCodeValidation();

    /// <summary>
    /// Validates QRIS (Quick Response Code Indonesian Standard) format using a compiled regular expression pattern.
    /// </summary>
    /// <remarks>
    /// Validates QRIS codes that:
    /// - Start with "00020101" (QRIS identifier)
    /// - End with 63 followed by 2 digits (checksum)
    ///
    /// > [!IMPORTANT]
    /// > This validation only checks the basic QRIS format structure. For complete QRIS validation,
    /// > additional checks should be performed according to the QRIS specification.
    ///
    /// Example usage:
    /// <code>
    /// var regex = RegexPatternCollections.QrisValidation();
    ///
    /// // Valid QRIS codes
    /// bool isValid1 = regex.IsMatch("00020101...6345");    // true
    ///
    /// // Invalid QRIS codes
    /// bool isValid2 = regex.IsMatch("00020102...6345");    // false (wrong header)
    /// bool isValid3 = regex.IsMatch("00020101...6445");    // false (wrong checksum identifier)
    /// </code>
    /// </remarks>
    /// <returns>A compiled <see href="https://learn.microsoft.com/en-us/dotnet/api/system.text.regularexpressions.regex">Regex</see> instance for QRIS validation.</returns>
    [GeneratedRegex(pattern: @"^00020101.*63[0-9]{2}$", options: RegexOptions.Compiled)]
    public static partial Regex QrisValidation();
    #endregion
}

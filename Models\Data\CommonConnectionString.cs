using System.Runtime.InteropServices;
using IDC.Utilities.Extensions;

namespace IDC.Utilities.Models.Data;

/// <summary>
/// Represents a common connection string format that can be converted to various database-specific formats.
/// </summary>
/// <remarks>
/// Provides a standardized way to handle connection strings across different database systems including MongoDB, Oracle, SQL Server, and PostgreSQL.
///
/// > [!NOTE]
/// > All sensitive information like ****words are stored as plain text. Ensure proper security measures when handling connection strings.
///
/// > [!IMPORTANT]
/// > Always validate connection parameters before using in production environments
///
/// Example connection strings for different databases:
/// ```json
/// // MongoDB
/// "******************************************************"
///
/// // Oracle
/// "(DESCRIPTION=(HOST=localhost)(PORT=1521)(SERVICE_NAME=orcl));User Id=system;Password=******"
///
/// // SQL Server
/// "Server=localhost,1433;Database=master;User ID=sa;Password=sqlserver;TrustServerCertificate=True"
///
/// // PostgreSQL
/// "Server=localhost;Port=5432;Database=********;User ID=********;Password=********"
/// ```
///
/// <example>
/// <code>
/// var connStr = new CommonConnectionString
/// {
///     Server = server: "localhost",
///     Port = port: 5432,
///     Database = database: "mydatabase",
///     Username = username: "myuser",
///     Password = ****word: "**********",
///     TrustServerCertificate = trustServerCertificate: true
/// };
/// </code>
/// </example>
/// </remarks>
/// <seealso href="https://www.mongodb.com/docs/manual/reference/connection-string"/>
/// <seealso href="https://www.******.com/database/technologies/net-connection-using-tns.html"/>
/// <seealso href="https://learn.microsoft.com/en-us/sql/connect/ado-net/connection-string-syntax"/>
/// <seealso href="https://www.********ql.org/docs/current/libpq-connect.html#LIBPQ-CONNSTRING"/>
public class CommonConnectionString
{
    /// <summary>
    /// Initializes a new instance of CommonConnectionString with default values.
    /// </summary>
    /// <remarks>
    /// Creates a connection string with secure default values suitable for development environments.
    ///
    /// > [!WARNING]
    /// > Default values are not recommended for production use
    ///
    /// Default Configuration:
    /// ```json
    /// {
    ///     "Server": "localhost",
    ///     "Port": 0,
    ///     "Database": ":memory:",
    ///     "Username": "",
    ///     "Password": "",
    ///     "ConnectionTimeout": 30,
    ///     "Pooling": true,
    ///     "MinPoolSize": 1,
    ///     "MaxPoolSize": 100,
    ///     "TrustServerCertificate": true,
    ///     "IntegratedSecurity": false,
    ///     "ApplicationName": null
    /// }
    /// ```
    ///
    /// <example>
    /// <code>
    /// // Create with defaults
    /// var defaultConn = new CommonConnectionString();
    ///
    /// // Create and customize
    /// var customConn = new CommonConnectionString
    /// {
    ///     Server = server: "prod-server",
    ///     Database = database: "production-db",
    ///     Username = username: "app-user",
    ///     Password = ****word: "secure-****word"
    /// };
    /// </code>
    /// </example>
    /// </remarks>
    public CommonConnectionString()
    {
        Server = "localhost";
        Port = 0;
        Database = ":memory:";
        Username = string.Empty;
        Password = string.Empty;
        ConnectionTimeout = 30;
        Pooling = true;
        MinPoolSize = 1;
        MaxPoolSize = 100;
        TrustServerCertificate = true;
        IntegratedSecurity = false;
        ApplicationName = null;
    }

    /// <summary>
    /// Gets or sets the server/host name for the database connection.
    /// </summary>
    /// <remarks>
    /// Specifies the server address where the database is hosted. Can be hostname, IP address, or instance name.
    ///
    /// <example>
    /// <code>
    /// var conn = new CommonConnectionString
    /// {
    ///     Server = server: "db.example.com"  // hostname
    ///     // or
    ///     Server = server: "*************"   // IP address
    ///     // or
    ///     Server = server: "MSSQLSERVER01"    // instance name
    /// };
    /// </code>
    /// </example>
    /// </remarks>
    /// <value>The server name or address.</value>
    public string? Server { get; set; }

    /// <summary>
    /// Gets or sets the database/catalog name to connect to.
    /// </summary>
    /// <remarks>
    /// Specifies the name of the database to use. For SQLite, this can be ":memory:" or a file path.
    ///
    /// <example>
    /// <code>
    /// var conn = new CommonConnectionString
    /// {
    ///     Database = database: "northwind",           // Regular database
    ///     // or
    ///     Database = database: ":memory:",           // SQLite in-memory
    ///     // or
    ///     Database = database: "C:/data/mydb.sqlite" // SQLite file
    /// };
    /// </code>
    /// </example>
    /// </remarks>
    /// <value>The database name or file path.</value>
    public string? Database { get; set; }

    /// <summary>
    /// Gets or sets the username for database authentication.
    /// </summary>
    /// <remarks>
    /// The username credential for database login. Leave empty for Windows Authentication.
    ///
    /// > [!IMPORTANT]
    /// > Ensure proper access rights are granted to this user in the database
    ///
    /// <example>
    /// <code>
    /// var conn = new CommonConnectionString
    /// {
    ///     Username = username: "db_user",
    ///     Password = ****word: "db_****word"
    /// };
    /// </code>
    /// </example>
    /// </remarks>
    /// <value>The username for authentication.</value>
    public string? Username { get; set; }

    /// <summary>
    /// Gets or sets the ****word for database authentication.
    /// </summary>
    /// <remarks>
    /// The ****word credential for database login. Store securely and never in plain text in production.
    ///
    /// > [!WARNING]
    /// > Never hard-code ****words in source code
    ///
    /// <example>
    /// <code>
    /// var conn = new CommonConnectionString
    /// {
    ///     Username = username: "db_user",
    ///     Password = ****word: Environment.GetEnvironmentVariable("DB_PASSWORD")
    /// };
    /// </code>
    /// </example>
    /// </remarks>
    /// <value>The ****word for authentication.</value>
    public string? Password { get; set; }

    /// <summary>
    /// Gets or sets the port number for the database connection.
    /// </summary>
    /// <remarks>
    /// Common default ports:
    /// - MongoDB: 27017
    /// - PostgreSQL: 5432
    /// - MySQL: 3306
    /// - SQL Server: 1433
    /// - Oracle: 1521
    ///
    /// <example>
    /// <code>
    /// var conn = new CommonConnectionString
    /// {
    ///     Server = server: "localhost",
    ///     Port = port: 5432  // PostgreSQL default
    /// };
    /// </code>
    /// </example>
    /// </remarks>
    /// <value>The port number to connect to.</value>
    public int Port { get; set; }

    /// <summary>
    /// Gets or sets whether to use Windows Authentication.
    /// </summary>
    /// <remarks>
    /// When true, uses Windows credentials instead of username/****word.
    /// Only applicable for SQL Server connections.
    ///
    /// <example>
    /// <code>
    /// var conn = new CommonConnectionString
    /// {
    ///     IntegratedSecurity = integratedSecurity: true,
    ///     // Username and Password not needed when using Windows Auth
    /// };
    /// </code>
    /// </example>
    /// </remarks>
    /// <value>True to use Windows Authentication, false to use SQL Authentication.</value>
    public bool IntegratedSecurity { get; set; }

    /// <summary>
    /// Gets or sets whether to trust the server certificate.
    /// </summary>
    /// <remarks>
    /// When true, by****es SSL certificate validation. Use with caution in production.
    ///
    /// > [!CAUTION]
    /// > Setting this to true in production may expose your connection to security risks
    ///
    /// <example>
    /// <code>
    /// var conn = new CommonConnectionString
    /// {
    ///     TrustServerCertificate = trustServerCertificate: true
    /// };
    /// </code>
    /// </example>
    /// </remarks>
    /// <value>True to trust server certificate, false to validate.</value>
    public bool TrustServerCertificate { get; set; }

    /// <summary>
    /// Gets or sets the connection timeout in seconds.
    /// </summary>
    /// <remarks>
    /// Maximum time to wait for a connection before throwing an exception.
    /// Default is 30 seconds.
    ///
    /// <example>
    /// <code>
    /// var conn = new CommonConnectionString
    /// {
    ///     ConnectionTimeout = connectionTimeout: 60  // 1 minute timeout
    /// };
    /// </code>
    /// </example>
    /// </remarks>
    /// <value>The timeout duration in seconds.</value>
    public int ConnectionTimeout { get; set; } = 30;

    /// <summary>
    /// Gets or sets the application name identifier.
    /// </summary>
    /// <remarks>
    /// Used to identify the application in database server logs and monitoring tools.
    ///
    /// <example>
    /// <code>
    /// var conn = new CommonConnectionString
    /// {
    ///     ApplicationName = applicationName: "MyApp.API.Production"
    /// };
    /// </code>
    /// </example>
    /// </remarks>
    /// <value>The application name identifier.</value>
    public string? ApplicationName { get; set; }

    /// <summary>
    /// Gets or sets whether connection pooling is enabled.
    /// </summary>
    /// <remarks>
    /// Enables connection reuse to improve performance. Default is true.
    ///
    /// > [!TIP]
    /// > Keep enabled for better performance in most scenarios
    ///
    /// <example>
    /// <code>
    /// var conn = new CommonConnectionString
    /// {
    ///     Pooling = pooling: true,
    ///     MinPoolSize = minPoolSize: 5,
    ///     MaxPoolSize = maxPoolSize: 100
    /// };
    /// </code>
    /// </example>
    /// </remarks>
    /// <value>True to enable connection pooling, false to disable.</value>
    public bool Pooling { get; set; } = true;

    /// <summary>
    /// Gets or sets the minimum connection pool size.
    /// </summary>
    /// <remarks>
    /// Minimum number of connections maintained in the pool. Default is 1.
    ///
    /// <example>
    /// <code>
    /// var conn = new CommonConnectionString
    /// {
    ///     MinPoolSize = minPoolSize: 5  // Keep minimum 5 connections
    /// };
    /// </code>
    /// </example>
    /// </remarks>
    /// <value>The minimum number of connections in the pool.</value>
    public int MinPoolSize { get; set; } = 1;

    /// <summary>
    /// Gets or sets the maximum connection pool size.
    /// </summary>
    /// <remarks>
    /// Maximum number of connections allowed in the pool. Default is 100.
    ///
    /// > [!NOTE]
    /// > Set based on your application's concurrent connection needs
    ///
    /// <example>
    /// <code>
    /// var conn = new CommonConnectionString
    /// {
    ///     MaxPoolSize = maxPoolSize: 200  // Allow up to 200 connections
    /// };
    /// </code>
    /// </example>
    /// </remarks>
    /// <value>The maximum number of connections in the pool.</value>
    public int MaxPoolSize { get; set; } = 100;

    /// <summary>
    /// Parses a MongoDB connection URL into a CommonConnectionString object.
    /// </summary>
    /// <param name="connectionString">MongoDB connection URL in standard format.</param>
    /// <returns>A CommonConnectionString instance populated with MongoDB connection details.</returns>
    /// <remarks>
    /// Supports standard MongoDB connection URL format with authentication and database name.
    /// Parses and validates all components of the connection string including:
    /// - Protocol (mongodb://)
    /// - Authentication credentials
    /// - Host(s) and port(s)
    /// - Database name
    /// - Connection options
    ///
    /// Connection string components:
    /// - Protocol: mongodb:// or mongodb+srv://
    /// - Credentials: username:****word@
    /// - Hosts: hostname1:port1,hostname2:port2
    /// - Database: /database_name
    /// - Options: ?option1=value1&amp;option2=value2
    ///
    /// > [!IMPORTANT]
    /// > Connection string must follow MongoDB URL format specifications
    ///
    /// > [!NOTE]
    /// > Special characters in username and ****word must be URL encoded
    ///
    /// > [!TIP]
    /// > Use URI encoding for special characters in credentials
    ///
    /// Example formats:
    /// <code>
    /// // Basic connection
    /// mongodb://localhost:27017/mydatabase
    ///
    /// // With authentication
    /// ******************************************************
    ///
    /// // With multiple hosts (replica set)
    /// **************************************************************
    ///
    /// // With query parameters
    /// ************************************************************************************
    ///
    /// // With connection pool settings
    /// mongodb://localhost:27017/mydatabase?maxPoolSize=50&amp;waitQueueTimeoutMS=2000
    ///
    /// // With replica set and read preference
    /// mongodb://host1:27017,host2:27017/mydatabase?replicaSet=mySet&amp;readPreference=secondary
    /// </code>
    /// </remarks>
    /// <exception cref="System.UriFormatException">Thrown when the connection string is not a valid URI format.</exception>
    /// <exception cref="System.ArgumentNullException">Thrown when the connection string is null or empty.</exception>
    /// <exception cref="System.ArgumentException">Thrown when required connection components are missing or invalid.</exception>
    /// <seealso href="https://www.mongodb.com/docs/manual/reference/connection-string/"/>
    /// <seealso href="https://www.mongodb.com/docs/manual/reference/connection-string/#connection-string-options"/>
    private static CommonConnectionString ParseMongoDbUrl(string connectionString)
    {
        var result = new CommonConnectionString();
        var uri = new Uri(uriString: connectionString);
        result.Server = uri.Host;
        result.Port = uri.Port;
        result.Database = uri.AbsolutePath.TrimStart(trimChars: '/');

        if (!string.IsNullOrEmpty(value: uri.UserInfo))
        {
            var credentials = uri.UserInfo.Split(separator: ':', options: StringSplitOptions.None);
            result.Username = credentials[0];
            if (credentials.Length > 1)
                result.Password = credentials[1];
        }

        return result;
    }

    /// <summary>
    /// Parses an Oracle TNS connection string into a CommonConnectionString object.
    /// </summary>
    /// <param name="connectionString" href="https://www.******.com/database/technologies/net-connection-using-tns.html">Oracle TNS format connection string</param>
    /// <returns>A <see cref="CommonConnectionString"/> instance populated with Oracle connection details</returns>
    /// <remarks>
    /// Supports Oracle TNS format with HOST, PORT, SERVICE_NAME parameters.
    ///
    /// > [!NOTE]
    /// > TNS format is commonly used for Oracle database connections
    ///
    /// > [!IMPORTANT]
    /// > Ensure all required TNS parameters are properly configured
    ///
    /// Example formats:
    /// ```json
    /// // Basic TNS format
    /// "(DESCRIPTION=(HOST=myhost)(PORT=1521)(SERVICE_NAME=myservice))"
    ///
    /// // With credentials
    /// "(DESCRIPTION=(HOST=myhost)(PORT=1521)(SERVICE_NAME=myservice));User Id=myuser;Password=**********"
    ///
    /// // With multiple hosts
    /// "(DESCRIPTION=(LOAD_BALANCE=on)
    ///   (ADDRESS_LIST=
    ///     (ADDRESS=(HOST=host1)(PORT=1521))
    ///     (ADDRESS=(HOST=host2)(PORT=1521)))
    ///   (SERVICE_NAME=myservice))"
    /// ```
    ///
    /// <example>
    /// <code>
    /// var tnsString = "(DESCRIPTION=(HOST=dbhost)(PORT=1521)(SERVICE_NAME=orcl));User Id=system;Password=******";
    /// var connStr = CommonConnectionString.ParseOracleTns(connectionString: tnsString);
    ///
    /// // Results in:
    /// // connStr.Server = "dbhost"
    /// // connStr.Port = 1521
    /// // connStr.Database = "orcl"
    /// // connStr.Username = "system"
    /// // connStr.Password = "******"
    /// </code>
    /// </example>
    /// </remarks>
    /// <exception cref="System.ArgumentException">Thrown when the TNS string format is invalid.</exception>
    /// <exception cref="System.ArgumentNullException">Thrown when the connection string is null or empty.</exception>
    /// <seealso href="https://docs.******.com/en/database/******/******-database/19/netag/configuring-naming-methods.html"/>
    private static CommonConnectionString ParseOracleTns(string connectionString)
    {
        var result = new CommonConnectionString();

        var hostMatch = RegexPatternCollections.OracleTnsHost().Match(connectionString);
        if (hostMatch.Success)
            result.Server = hostMatch.Groups[1].Value;

        var portMatch = RegexPatternCollections.OracleTnsPort().Match(connectionString);
        if (portMatch.Success)
            result.Port = int.Parse(portMatch.Groups[1].Value);

        var serviceMatch = RegexPatternCollections.OracleTnsServiceName().Match(connectionString);
        if (serviceMatch.Success)
            result.Database = serviceMatch.Groups[1].Value;

        var userMatch = RegexPatternCollections.OracleTnsUserId().Match(connectionString);
        if (userMatch.Success)
            result.Username = userMatch.Groups[1].Value;

        var ****wordMatch = RegexPatternCollections.OracleTnsPassword().Match(connectionString);
        if (****wordMatch.Success)
            result.Password = ****wordMatch.Groups[1].Value;

        return result;
    }

    /// <summary>
    /// Parses server value that may include port number.
    /// </summary>
    /// <param name="result" href="https://github.com/yourusername/IDC.Utilities/blob/main/Models/Data/CommonConnectionString.cs">CommonConnectionString instance to update</param>
    /// <param name="value">Server value string that may include port</param>
    /// <returns>void</returns>
    /// <remarks>
    /// Handles various server:port formats including IPv6 addresses.
    ///
    /// > [!NOTE]
    /// > For IPv6 addresses, the address should be enclosed in square brackets
    ///
    /// <example>
    /// <code>
    /// var connStr = new CommonConnectionString();
    ///
    /// // Standard hostname:port
    /// ParseServerValue(result: connStr, value: "localhost:5432");
    /// // connStr.Server = "localhost"
    /// // connStr.Port = 5432
    ///
    /// // IPv6 address
    /// ParseServerValue(result: connStr, value: "[2001:db8::1]:3306");
    /// // connStr.Server = "2001:db8::1"
    /// // connStr.Port = 3306
    ///
    /// // Comma-separated format
    /// ParseServerValue(result: connStr, value: "server1,1433");
    /// // connStr.Server = "server1"
    /// // connStr.Port = 1433
    /// </code>
    /// </example>
    /// </remarks>
    /// <exception cref="System.ArgumentNullException">Thrown when result parameter is null</exception>
    private static void ParseServerValue(CommonConnectionString result, string value)
    {
        var serverParts = value.Split(separator: ',')[0].Split(separator: ':');
        result.Server = serverParts[0].Trim().TrimEnd(trimChar: ']').TrimStart(trimChar: '[');

        if (serverParts.Length > 1)
            result.Port = serverParts[1].CastToInteger() ?? 0;
    }

    /// <summary>
    /// Parses timeout value that may be in seconds or milliseconds.
    /// </summary>
    /// <param name="value">Timeout string value</param>
    /// <returns>Timeout value in seconds</returns>
    /// <remarks>
    /// Converts milliseconds to seconds if "ms" suffix is present.
    /// Returns 30 seconds as default if parsing fails.
    ///
    /// > [!NOTE]
    /// > Default timeout is 30 seconds
    ///
    /// <example>
    /// <code>
    /// // Seconds format
    /// var timeout1 = ParseTimeout(value: "60");     // Returns 60
    ///
    /// // Milliseconds format
    /// var timeout2 = ParseTimeout(value: "5000ms"); // Returns 5
    ///
    /// // Invalid format
    /// var timeout3 = ParseTimeout(value: "invalid"); // Returns 30 (default)
    /// </code>
    /// </example>
    /// </remarks>
    private static int ParseTimeout(string value) =>
        value.EndsWith(value: "ms")
            ? value.TrimEnd(trimChars: ['m', 's']).CastToInteger() ?? 30
            : value.CastToInteger() ?? 30;

    /// <summary>
    /// Parses string value to boolean based on provided true values.
    /// </summary>
    /// <param name="value">String value to parse</param>
    /// <param name="trueValues">Array of strings that represent true value</param>
    /// <returns>True if value matches any trueValues (case-insensitive), false otherwise</returns>
    /// <remarks>
    /// Performs case-insensitive comparison against provided true values.
    ///
    /// > [!TIP]
    /// > Common true values include: "true", "yes", "1", "on"
    ///
    /// <example>
    /// <code>
    /// var result1 = ParseBooleanValue(value: "true", trueValues: ["true", "yes"]); // Returns true
    /// var result2 = ParseBooleanValue(value: "YES", trueValues: ["true", "yes"]);  // Returns true
    /// var result3 = ParseBooleanValue(value: "no", trueValues: ["true", "yes"]);   // Returns false
    /// </code>
    /// </example>
    /// </remarks>
    private static bool ParseBooleanValue(string value, params string[] trueValues) =>
        trueValues.Any(predicate: v =>
            value.Equals(value: v, comparisonType: StringComparison.OrdinalIgnoreCase)
        );

    /// <summary>
    /// Processes key-value pair from connection string and updates CommonConnectionString properties.
    /// </summary>
    /// <param name="result" href="https://github.com/yourusername/IDC.Utilities/blob/main/Models/Data/CommonConnectionString.cs">CommonConnectionString instance to update</param>
    /// <param name="key">Connection string parameter key (normalized to lowercase)</param>
    /// <param name="value">Connection string parameter value</param>
    /// <remarks>
    /// Handles various connection string parameter aliases across different database systems.
    ///
    /// > [!IMPORTANT]
    /// > All keys are normalized to lowercase before processing
    ///
    /// Supported parameter mappings:
    /// ```json
    /// {
    ///   "server": ["data source", "host", "contact points"],
    ///   "database": ["initial catalog", "default keyspace", "bucket_name", "databasename"],
    ///   "username": ["user id", "uid", "user"],
    ///   "****word": ["pwd"],
    ///   "integratedSecurity": ["trusted_connection"],
    ///   "timeout": ["connection timeout", "connect timeout", "connection_timeout"],
    ///   "poolSize": ["min pool size", "minimum pool size", "max pool size", "maximum pool size"]
    /// }
    /// ```
    ///
    /// <example>
    /// <code>
    /// var connStr = new CommonConnectionString();
    ///
    /// // Server variations
    /// ProcessKeyValuePair(result: connStr, key: "server", value: "localhost");
    /// ProcessKeyValuePair(result: connStr, key: "data source", value: "localhost");
    /// ProcessKeyValuePair(result: connStr, key: "host", value: "localhost");
    ///
    /// // Authentication variations
    /// ProcessKeyValuePair(result: connStr, key: "user id", value: "myuser");
    /// ProcessKeyValuePair(result: connStr, key: "****word", value: "my****");
    /// ProcessKeyValuePair(result: connStr, key: "integrated security", value: "true");
    ///
    /// // Timeout and pooling
    /// ProcessKeyValuePair(result: connStr, key: "connection timeout", value: "30");
    /// ProcessKeyValuePair(result: connStr, key: "min pool size", value: "1");
    /// ProcessKeyValuePair(result: connStr, key: "max pool size", value: "100");
    /// </code>
    /// </example>
    /// </remarks>
    /// <exception cref="System.ArgumentNullException">Thrown when result parameter is null</exception>
    /// <seealso href="https://learn.microsoft.com/en-us/dotnet/framework/data/adonet/connection-strings"/>
    private static void ProcessKeyValuePair(CommonConnectionString result, string key, string value)
    {
        switch (key)
        {
            case "server":
            case "data source":
            case "host":
            case "contact points":
                // Khusus untuk SQLite, data source adalah path database
                if (value == ":memory:")
                {
                    result.Database = value;
                }
                else if (
                    value.EndsWith(".db")
                    || value.EndsWith(".sqlite")
                    || value.EndsWith(".sqlite3")
                )
                {
                    result.Database = value;
                }
                else
                {
                    ParseServerValue(result: result, value: value);
                }
                break;

            case "database":
            case "initial catalog":
            case "default keyspace":
            case "bucket_name":
            case "databasename":
                result.Database = value;
                break;

            case "user id":
            case "uid":
            case "username":
            case "user":
                result.Username = value;
                break;

            case "****word":
            case "pwd":
                result.Password = value;
                break;

            case "port":
                result.Port = value.CastToInteger() ?? 0;
                break;

            case "integrated security":
            case "trusted_connection":
                result.IntegratedSecurity = ((string[])["true", "yes", "sspi"]).Any(predicate: v =>
                    value.Equals(value: v, comparisonType: StringComparison.OrdinalIgnoreCase)
                );
                break;

            case "trust server certificate":
            case "trustservercertificate":
                result.TrustServerCertificate = ParseBooleanValue(value: value, trueValues: "true");
                break;

            case "connection timeout":
            case "connect timeout":
            case "timeout":
            case "connection_timeout":
                result.ConnectionTimeout = ParseTimeout(value: value);
                break;

            case "application name":
            case "application":
                result.ApplicationName = value;
                break;

            case "pooling":
                result.Pooling = ParseBooleanValue(value: value, trueValues: "true");
                break;

            case "min pool size":
            case "minimum pool size":
                result.MinPoolSize = value.CastToInteger() ?? 1;
                break;

            case "max pool size":
            case "maximum pool size":
                result.MaxPoolSize = value.CastToInteger() ?? 100;
                break;
        }
    }

    /// <summary>
    /// Parses a connection string into a CommonConnectionString object.
    /// </summary>
    /// <param name="connectionString" href="https://learn.microsoft.com/en-us/dotnet/api/system.string">The connection string to parse.</param>
    /// <returns href="https://github.com/yourusername/IDC.Utilities/blob/main/Models/Data/CommonConnectionString.cs">A new CommonConnectionString instance populated with the parsed values.</returns>
    /// <remarks>
    /// Supports common connection string formats with various key aliases.
    ///
    /// Supported parameter mappings:
    /// ```json
    /// {
    ///   "server": ["data source", "host", "contact points"],
    ///   "database": ["initial catalog", "default keyspace"],
    ///   "username": ["user id", "uid", "user"],
    ///   "****word": ["pwd"],
    ///   "port": [],
    ///   "integratedSecurity": ["trusted_connection"],
    ///   "trustServerCertificate": ["trust server certificate"],
    ///   "timeout": ["connection timeout", "connect timeout"],
    ///   "applicationName": ["application"],
    ///   "pooling": [],
    ///   "minPoolSize": ["minimum pool size"],
    ///   "maxPoolSize": ["maximum pool size"]
    /// }
    /// ```
    ///
    /// > [!NOTE]
    /// > All keys are case-insensitive
    ///
    /// > [!TIP]
    /// > For MongoDB URLs, use the format: mongodb://[username:****word@]host[:port][/database]
    ///
    /// Example:
    /// <example>
    /// <code>
    /// var connStr = "Server=localhost;Database=mydb;User ID=user;Password=****;Port=1433;";
    /// var result = commonConnStr.FromConnectionString(connectionString: connStr);
    ///
    /// // MongoDB URL
    /// var mongoConnStr = "****************************************";
    /// var mongoResult = commonConnStr.FromConnectionString(connectionString: mongoConnStr);
    /// </code>
    /// </example>
    /// </remarks>
    /// <exception cref="System.ArgumentNullException">Thrown when connectionString is null or empty.</exception>
    /// <seealso href="https://learn.microsoft.com/en-us/dotnet/framework/data/adonet/connection-strings">Connection Strings in ADO.NET</seealso>
    /// <seealso href="https://www.mongodb.com/docs/manual/reference/connection-string/">MongoDB Connection String URI Format</seealso>
    public virtual CommonConnectionString FromConnectionString(string connectionString)
    {
        if (connectionString.StartsWith(value: "mongodb://"))
            return ParseMongoDbUrl(connectionString: connectionString);

        if (connectionString.Contains(value: "(DESCRIPTION="))
            return ParseOracleTns(connectionString: connectionString);

        var result = new CommonConnectionString();

        foreach (
            var part in connectionString.Split(
                separator: new char[] {';'},
                options: StringSplitOptions.RemoveEmptyEntries
            )
        )
        {
            var keyValue = part.Split(separator: new char[] { '=' }, count: 2, options: StringSplitOptions.None);
            if (keyValue.Length != 2)
                continue;

            ProcessKeyValuePair(
                result: result,
                key: keyValue[0].Trim().ToLower(),
                value: keyValue[1].Trim()
            );
        }

        return result;
    }

    /// <summary>
    /// Converts the current connection string to PostgreSQL format.
    /// </summary>
    /// <returns href="https://www.npgsql.org/doc/connection-string-parameters.html">A formatted PostgreSQL connection string.</returns>
    /// <remarks>
    /// This method generates a connection string compatible with PostgreSQL databases using the Npgsql provider.
    ///
    /// > [!NOTE]
    /// > The generated string includes SSL configuration and pool sizing parameters.
    ///
    /// > [!IMPORTANT]
    /// > Ensure that the Server and Database properties are set before calling this method.
    ///
    /// <example>
    /// <code>
    /// var connStr = new CommonConnectionString
    /// {
    ///     Server = server: "localhost",
    ///     Port = port: 5432,
    ///     Database = database: "mydb",
    ///     Username = username: "myuser",
    ///     Password = ****word: "**********"
    /// };
    /// string pgConnStr = connStr.ToPostgreSQL();
    /// // Result: "Host=localhost;Port=5432;Database=mydb;Username=myuser;Password=**********;Timeout=30;Application Name=CommonConnectionString;Pooling=True;Minimum Pool Size=1;Maximum Pool Size=100;Trust Server Certificate=true"
    /// </code>
    /// </example>
    /// </remarks>
    /// <seealso href="https://www.********ql.org/docs/current/libpq-connect.html#LIBPQ-CONNSTRING"/>
    public string ToPostgreSQL() =>
        string.Join<string>(
            separator: ";",
            values: new string[]
            {
                $"Host={Server}",
                Port > 0 ? $"Port={Port}" : string.Empty,
                $"Database={Database}",
                $"Username={Username}",
                $"Password={Password}",
                $"Timeout={ConnectionTimeout}",
                $"Application Name={ApplicationName ?? "CommonConnectionString"}",
                $"Pooling={Pooling}",
                $"Minimum Pool Size={MinPoolSize}",
                $"Maximum Pool Size={MaxPoolSize}",
                "Trust Server Certificate=true",
            }.Where(predicate: static x => !string.IsNullOrEmpty(value: x))
        );

    /// <summary>
    /// Converts the current connection string to SQLite format.
    /// </summary>
    /// <param name="cache" href="https://learn.microsoft.com/en-us/dotnet/standard/data/sqlite/connection-strings#cache">Cache mode (default: "Shared"). Available values: Private, Shared, Default</param>
    /// <param name="mode" href="https://learn.microsoft.com/en-us/dotnet/standard/data/sqlite/connection-strings#mode">Access mode (default: "ReadWrite"). Available values: ReadOnly, ReadWrite, Memory</param>
    /// <returns href="https://learn.microsoft.com/en-us/dotnet/standard/data/sqlite/connection-strings">A formatted SQLite connection string.</returns>
    /// <remarks>
    /// This method generates a connection string compatible with SQLite databases.
    ///
    /// > [!NOTE]
    /// > The generated string includes cache and mode parameters.
    ///
    /// > [!IMPORTANT]
    /// > Ensure that the Database property is set before calling this method.
    ///
    /// <example>
    /// <code>
    /// var connStr = new CommonConnectionString
    /// {
    ///     Database = database: "C:/data/mydb.sqlite"
    /// };
    /// string sqliteConnStr = connStr.ToSQLite(cache: "Shared", mode: "ReadWrite");
    /// // Result: "Data Source=C:/data/mydb.sqlite;Cache=Shared;Mode=ReadWrite"
    /// </code>
    /// </example>
    /// </remarks>
    /// <seealso href="https://www.sqlite.org/c3ref/open.html"/>
    public string ToSQLite(string cache = "Shared", string mode = "ReadWrite")
    {
        var dbPath = Database;
        if (!Path.IsPathRooted(path: dbPath))
        {
            dbPath = Path.Combine(
                path1: Directory.GetCurrentDirectory(),
                path2: dbPath ?? string.Empty
            );
        }
        dbPath = dbPath.Replace(oldChar: '\\', newChar: '/');

        return string.Join<string>(
            separator: ";",
            values: [$"Data Source={dbPath}", $"Cache={cache}", $"Mode={mode}"]
        );
    }

    /// <summary>
    /// Creates an in-memory SQLite connection string.
    /// </summary>
    /// <param name="cached" href="https://learn.microsoft.com/en-us/dotnet/standard/data/sqlite/connection-strings#cache">Whether to use shared cache (default: true).</param>
    /// <returns href="https://learn.microsoft.com/en-us/dotnet/standard/data/sqlite/connection-strings">A formatted in-memory SQLite connection string.</returns>
    /// <remarks>
    /// This method generates a connection string for an in-memory SQLite database.
    ///
    /// > [!NOTE]
    /// > In-memory databases are temporary and exist only for the duration of the connection.
    ///
    /// > [!TIP]
    /// > Use shared cache to allow multiple connections to access the same in-memory database.
    ///
    /// For more details about SQLite in-memory databases and cache modes, see:
    /// <see href="https://learn.microsoft.com/en-us/dotnet/standard/data/sqlite/in-memory-databases"/>
    /// <see href="https://learn.microsoft.com/en-us/dotnet/standard/data/sqlite/connection-strings#cache"/>
    /// <see href="https://learn.microsoft.com/en-us/dotnet/standard/data/sqlite/connection-strings#cache-size"/>
    /// <see href="https://learn.microsoft.com/en-us/dotnet/standard/data/sqlite/connection-strings#cache-shared"/>
    ///
    /// <example>
    /// <code>
    /// var connStr = new CommonConnectionString();
    ///
    /// // Create a shared in-memory database
    /// string sharedMemoryDb = connStr.ToSQLiteInMemory(cached: true);
    /// // Result: "Data Source=:memory:;Mode=Memory;Cache=Shared"
    ///
    /// // Create a private in-memory database
    /// string privateMemoryDb = connStr.ToSQLiteInMemory(cached: false);
    /// // Result: "Data Source=:memory:;Mode=Memory;Cache=Private"
    /// </code>
    /// </example>
    /// </remarks>
#pragma warning disable CA1822 // Mark members as static
    public string ToSQLiteInMemory(bool cached = true) =>
        string.Join<string>(
            separator: ";",
            values:
            [
                "Data Source=:memory:",
                "Mode=Memory",
                $"Cache={(cached ? "Shared" : "Private")}",
            ]
        );
#pragma warning restore CA1822 // Mark members as static

    /// <summary>
    /// Converts the current connection string to MySQL format.
    /// </summary>
    /// <returns href="https://dev.mysql.com/doc/connector-net/en/connector-net-connection-options.html">A formatted MySQL connection string.</returns>
    /// <remarks>
    /// This method generates a connection string compatible with MySQL databases using the MySQL Connector/NET.
    ///
    /// > [!NOTE]
    /// > The generated string includes SSL configuration and pool sizing parameters.
    ///
    /// > [!IMPORTANT]
    /// > Ensure that the Server and Database properties are set before calling this method.
    ///
    /// <example>
    /// <code>
    /// var connStr = new CommonConnectionString
    /// {
    ///     Server = server: "localhost",
    ///     Port = port: 3306,
    ///     Database = database: "mydb",
    ///     Username = username: "myuser",
    ///     Password = ****word: "**********"
    /// };
    /// string mysqlConnStr = connStr.ToMySQL();
    /// // Result: "Server=localhost;Port=3306;Database=mydb;User ID=myuser;Password=**********;Connection Timeout=30;Pooling=True;Min Pool Size=1;Max Pool Size=100;Allow User Variables=true;Convert Zero Datetime=true;SSL Mode=Required"
    /// </code>
    /// </example>
    /// </remarks>
    /// <seealso href="https://dev.mysql.com/doc/connector-net/en/connector-net-connection-options.html"/>
    public string ToMySQL() =>
        string.Join<string>(
            separator: ";",
            values: new string[]
            {
                $"Server={Server}",
                Port > 0 ? $"Port={Port}" : string.Empty,
                $"Database={Database}",
                $"User ID={Username}",
                $"Password={Password}",
                $"Connection Timeout={ConnectionTimeout}",
                $"Pooling={Pooling}",
                $"Min Pool Size={MinPoolSize}",
                $"Max Pool Size={MaxPoolSize}",
                "Allow User Variables=true",
                "Convert Zero Datetime=true",
                "SSL Mode=Required",
            }.Where(predicate: static x => !string.IsNullOrEmpty(value: x))
        );

    /// <summary>
    /// Converts the current connection string to SQL Server format.
    /// </summary>
    /// <returns href="https://learn.microsoft.com/en-us/dotnet/framework/data/adonet/connection-string-syntax#sql-server-connection-strings">A formatted SQL Server connection string.</returns>
    /// <remarks>
    /// This method generates a connection string compatible with SQL Server databases.
    ///
    /// > [!NOTE]
    /// > The generated string includes integrated security, SSL configuration, and pool sizing parameters.
    ///
    /// > [!IMPORTANT]
    /// > Ensure that the Server and Database properties are set before calling this method.
    ///
    /// <example>
    /// <code>
    /// var connStr = new CommonConnectionString
    /// {
    ///     Server = server: "localhost",
    ///     Port = port: 1433,
    ///     Database = database: "mydb",
    ///     Username = username: "myuser",
    ///     Password = ****word: "**********",
    ///     IntegratedSecurity = integratedSecurity: false,
    ///     TrustServerCertificate = trustServerCertificate: true
    /// };
    /// string sqlServerConnStr = connStr.ToSQLServer();
    /// // Result: "Server=localhost,1433;Database=mydb;User ID=myuser;Password=**********;Connection Timeout=30;Application Name=CommonConnectionString;TrustServerCertificate=True;Pooling=True;Min Pool Size=1;Max Pool Size=100;MultipleActiveResultSets=true"
    /// </code>
    /// </example>
    /// </remarks>
    /// <seealso href="https://learn.microsoft.com/en-us/sql/connect/ado-net/connection-string-syntax"/>
    public string ToSQLServer() =>
        string.Join<string>(
            separator: ";",
            values:
            [
                $"Server={Server}{(Port > 0 ? $",{Port}" : string.Empty)}",
                $"Database={Database}",
                IntegratedSecurity
                    ? RuntimeInformation.IsOSPlatform(osPlatform: OSPlatform.Windows)
                        ? "Integrated Security=SSPI"
                        : "Integrated Security=true"
                    : $"User ID={Username};Password={Password}",
                $"Connection Timeout={ConnectionTimeout}",
                $"Application Name={ApplicationName ?? "CommonConnectionString"}",
                $"TrustServerCertificate={TrustServerCertificate}",
                $"Pooling={Pooling}",
                $"Min Pool Size={MinPoolSize}",
                $"Max Pool Size={MaxPoolSize}",
                "MultipleActiveResultSets=true",
            ]
        );

    /// <summary>
    /// Converts the current connection string to MariaDB format.
    /// </summary>
    /// <returns href="https://mariadb.com/kb/en/about-mariadb-connector-net/#connection-strings">A formatted MariaDB connection string.</returns>
    /// <remarks>
    /// This method generates a connection string compatible with MariaDB databases using the MariaDB Connector/NET.
    ///
    /// > [!NOTE]
    /// > The generated string includes SSL configuration and pool sizing parameters.
    ///
    /// > [!IMPORTANT]
    /// > Ensure that the Server and Database properties are set before calling this method.
    ///
    /// <example>
    /// <code>
    /// var connStr = new CommonConnectionString
    /// {
    ///     Server = server: "localhost",
    ///     Port = port: 3306,
    ///     Database = database: "mydb",
    ///     Username = username: "myuser",
    ///     Password = ****word: "**********"
    /// };
    /// string mariaDbConnStr = connStr.ToMariaDB();
    /// // Result: "Server=localhost;Port=3306;Database=mydb;User ID=myuser;Password=**********;Connection Timeout=30;Pooling=True;Min Pool Size=1;Max Pool Size=100;SSL Mode=Required;Allow User Variables=true"
    /// </code>
    /// </example>
    /// </remarks>
    /// <seealso href="https://mariadb.com/kb/en/about-mariadb-connector-net/#connection-string-parameters"/>
    public string ToMariaDB() =>
        string.Join<string>(
            separator: ";",
            values: new string[]
            {
                $"Server={Server}",
                Port > 0 ? $"Port={Port}" : string.Empty,
                $"Database={Database}",
                $"User ID={Username}",
                $"Password={Password}",
                $"Connection Timeout={ConnectionTimeout}",
                $"Pooling={Pooling}",
                $"Min Pool Size={MinPoolSize}",
                $"Max Pool Size={MaxPoolSize}",
                "SSL Mode=Required",
                "Allow User Variables=true",
            }.Where(predicate: static x => !string.IsNullOrEmpty(value: x))
        );

    /// <summary>
    /// Converts the current connection string to Oracle format.
    /// </summary>
    /// <returns href="https://docs.******.com/en/database/******/******-database/19/odpnt/ConnectionString.html">A formatted Oracle connection string.</returns>
    /// <remarks>
    /// This method generates a connection string compatible with Oracle databases using Oracle Data Provider for .NET (ODP.NET).
    ///
    /// > [!NOTE]
    /// > The generated string includes Easy Connect Plus format and connection pooling parameters.
    ///
    /// > [!IMPORTANT]
    /// > Ensure that the Server, Port, and Database (Service Name) properties are set before calling this method.
    ///
    /// <example>
    /// <code>
    /// var connStr = new CommonConnectionString
    /// {
    ///     Server = server: "localhost",
    ///     Port = port: 1521,
    ///     Database = database: "ORCL",
    ///     Username = username: "system",
    ///     Password = ****word: "******"
    /// };
    /// string ******ConnStr = connStr.ToOracle();
    /// // Result: "Data Source=(DESCRIPTION=(ADDRESS=(PROTOCOL=TCP)(HOST=localhost)(PORT=1521))(CONNECT_DATA=(SERVICE_NAME=ORCL)));User Id=system;Password=******;Pooling=true;Statement Cache Size=20"
    /// </code>
    /// </example>
    /// </remarks>
    /// <seealso href="https://docs.******.com/en/database/******/******-database/19/netag/configuring-naming-methods.html#GUID-B0437826-43C1-49EC-A94D-B650B6A4A6EE"/>
    public string ToOracle() =>
        string.Join<string>(
            separator: ";",
            values:
            [
                string.Join(
                    string.Empty,
                    [
                        "Data Source=(DESCRIPTION=",
                        "(ADDRESS=",
                        "(PROTOCOL=TCP)",
                        $"(HOST={Server})",
                        $"(PORT={Port})",
                        ")",
                        "(CONNECT_DATA=",
                        $"(SERVICE_NAME={Database})",
                        ")",
                        ")",
                    ]
                ),
                $"User Id={Username}",
                $"Password={Password}",
                "Pooling=true",
                "Statement Cache Size=20",
            ]
        );

    /// <summary>
    /// Converts the current connection string to MongoDB format.
    /// </summary>
    /// <returns href="https://www.mongodb.com/docs/manual/reference/connection-string/">A formatted MongoDB connection string.</returns>
    /// <remarks>
    /// This method generates a connection string compatible with MongoDB databases.
    ///
    /// > [!NOTE]
    /// > The generated string includes retry writes, write concern, SSL, and connection timeout parameters.
    ///
    /// > [!IMPORTANT]
    /// > Ensure that the Server, Port, and Database properties are set before calling this method.
    ///
    /// <example>
    /// <code>
    /// var connStr = new CommonConnectionString
    /// {
    ///     Server = server: "localhost",
    ///     Port = port: 27017,
    ///     Database = database: "mydb",
    ///     Username = username: "myuser",
    ///     Password = ****word: "**********",
    ///     ConnectionTimeout = connectionTimeout: 30
    /// };
    /// string mongoDbConnStr = connStr.ToMongoDB();
    /// // Result: "************************************************************************************************************************"
    /// </code>
    /// </example>
    /// </remarks>
    /// <seealso href="https://www.mongodb.com/docs/manual/reference/connection-string-options/"/>
    public string ToMongoDB()
    {
        var auth = string.IsNullOrEmpty(value: Username)
            ? string.Empty
            : $"{Uri.EscapeDataString(stringToEscape: Username ?? string.Empty)}:{Uri.EscapeDataString(stringToEscape: Password ?? string.Empty)}@";
        List<string> options =
        [
            "retryWrites=true",
            "w=majority",
            "ssl=true",
            $"connectTimeoutMS={ConnectionTimeout * 1000}",
        ];

        return $"mongodb://{auth}{Server}:{Port}/{Database}?{string.Join(separator: "&", values: options)}";
    }

    /// <summary>
    /// Converts the current connection string to Cassandra format.
    /// </summary>
    /// <returns href="https://docs.datastax.com/en/developer/csharp-driver/3.0/features/connection-pooling/">A formatted Cassandra connection string.</returns>
    /// <remarks>
    /// This method generates a connection string compatible with Cassandra databases using the DataStax C# Driver.
    ///
    /// > [!NOTE]
    /// > The generated string includes SSL, connection timeout, and authentication parameters.
    ///
    /// > [!IMPORTANT]
    /// > Ensure that the Server, Port, and Database properties are set before calling this method.
    ///
    /// <example>
    /// <code>
    /// var connStr = new CommonConnectionString
    /// {
    ///     Server = server: "localhost",
    ///     Port = port: 9042,
    ///     Database = database: "mykeyspace",
    ///     Username = username: "myuser",
    ///     Password = ****word: "**********"
    /// };
    /// string cassandraConnStr = connStr.ToCassandra();
    /// // Result: "Contact Points=localhost;Port=9042;Default Keyspace=mykeyspace;User ID=myuser;Password=**********;SSL=true;Connection Timeout=10"
    /// </code>
    /// </example>
    /// </remarks>
    public string ToCassandra() =>
        string.Join<string>(
            separator: ";",
            values: new[]
            {
                $"Contact Points={Server}",
                $"Port={Port}",
                $"Default Keyspace={Database}",
                !string.IsNullOrEmpty(value: Username) ? $"User ID={Username}" : string.Empty,
                !string.IsNullOrEmpty(value: Username) ? $"Password={Password}" : string.Empty,
                "SSL=true",
                "Connection Timeout=10",
            }.Where(predicate: static x => !string.IsNullOrEmpty(value: x))
        );

    /// <summary>
    /// Converts the current connection string to Couchbase format.
    /// </summary>
    /// <returns href="https://docs.couchbase.com/dotnet-sdk/current/ref/connection-string.html">A formatted Couchbase connection string.</returns>
    /// <remarks>
    /// This method generates a connection string compatible with Couchbase databases using the Couchbase .NET SDK.
    ///
    /// > [!NOTE]
    /// > The generated string includes SSL, connection timeout, and authentication parameters.
    ///
    /// > [!IMPORTANT]
    /// > Ensure that the Server and Database properties are set before calling this method.
    ///
    /// <example>
    /// <code>
    /// var connStr = new CommonConnectionString
    /// {
    ///     Server = server: "localhost",
    ///     Port = port: 8091,
    ///     Database = database: "mybucket",
    ///     Username = username: "myuser",
    ///     Password = ****word: "**********"
    /// };
    /// string couchbaseConnStr = connStr.ToCouchbase();
    /// // Result: "couchbase://localhost?connection_timeout=30000&amp;bucket_name=mybucket&amp;ssl=true&amp;username=myuser&amp;****word=**********"
    /// </code>
    /// </example>
    /// </remarks>
    public string ToCouchbase()
    {
        List<string> options =
        [
            $"connection_timeout={ConnectionTimeout * 1000}",
            $"bucket_name={Database}",
            "ssl=true",
        ];

        if (!string.IsNullOrEmpty(Username))
        {
            options.Add($"username={Uri.EscapeDataString(stringToEscape: Username)}");
            options.Add(
                $"****word={Uri.EscapeDataString(stringToEscape: Password ?? string.Empty)}"
            );
        }

        return $"couchbase://{Server}?{string.Join(separator: "&", values: options)}";
    }

    public CommonConnectionString ChangePassword(string newPassword)
    {
        Password = newPassword;
        return this;
    }
}

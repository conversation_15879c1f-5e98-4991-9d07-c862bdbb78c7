using System.Security.Cryptography;
using System.Text;

namespace IDC.Utilities.Extensions;

public static partial class StringExtensions
{
    /// <summary>
    /// Performs a left rotation operation on a 32-bit unsigned integer.
    /// </summary>
    /// <remarks>
    /// This method rotates the bits of the input value to the left by the specified number of bits.
    /// The rotation is circular, meaning bits shifted off the left end are reintroduced on the right.
    ///
    /// > [!NOTE]
    /// > The method assumes a 32-bit unsigned integer input and performs modulo 32 on the bit count.
    ///
    /// <example>
    /// <code>
    /// uint result = LeftRotate(value: 0b11001100110011001100110011001100, bits: 4);
    /// // result will be 0b10011001100110011001100110011000
    /// </code>
    /// </example>
    /// </remarks>
    /// <param name="value">The 32-bit unsigned integer to rotate.</param>
    /// <param name="bits">The number of bits to rotate left (0-31).</param>
    /// <returns>The rotated 32-bit unsigned integer.</returns>
    private static uint LeftRotate(uint value, int bits) =>
        (value << bits) | (value >> (0b100000 - bits));

    /// <summary>
    /// Calculates a bitwise operation based on the threshold value.
    /// </summary>
    /// <remarks>
    /// This method performs different bitwise operations depending on the threshold value:
    /// - For thresholds less than 20 (0b10100): Uses (byte1 AND byte2) OR (NOT byte1 AND byte3)
    /// - For thresholds less than 40 (0b101000): Uses XOR operation between all three bytes
    /// - For thresholds less than 60 (0b111100): Uses (byte1 AND byte2) OR (byte1 AND byte3) OR (byte2 AND byte3)
    /// - For all other thresholds: Uses XOR operation between all three bytes
    ///
    /// > [!NOTE]
    /// > The threshold values are based on binary representations for consistency with the rest of the implementation.
    ///
    /// > [!IMPORTANT]
    /// > This method is critical for the SHA-0 hashing algorithm implementation and should not be modified without thorough testing.
    ///
    /// <example>
    /// <code>
    /// uint result = CalculateBitwiseOperation(
    ///     threshold: 15,
    ///     byte1: 0b11001100110011001100110011001100,
    ///     byte2: 0b10101010101010101010101010101010,
    ///     byte3: 0b11110000111100001111000011110000
    /// );
    /// </code>
    /// </example>
    /// </remarks>
    /// <param name="threshold">The threshold value determining which bitwise operation to perform.</param>
    /// <param name="byte1">First 32-bit unsigned integer input.</param>
    /// <param name="byte2">Second 32-bit unsigned integer input.</param>
    /// <param name="byte3">Third 32-bit unsigned integer input.</param>
    /// <returns>The result of the bitwise operation.</returns>
    private static uint CalculateBitwiseOperation(
        int threshold,
        uint byte1,
        uint byte2,
        uint byte3
    ) =>
        threshold switch
        {
            < 0b10100 => byte1 & byte2 | ~byte1 & byte3,
            < 0b101000 => byte1 ^ byte2 ^ byte3,
            < 0b111100 => byte1 & byte2 | byte1 & byte3 | byte2 & byte3,
            _ => byte1 ^ byte2 ^ byte3,
        };

    /// <summary>
    /// Retrieves the round constant for a given round in the SHA-0 hashing algorithm.
    /// </summary>
    /// <remarks>
    /// This method provides the round-specific constants used in the SHA-0 algorithm.
    /// The constants are binary values that vary based on the current round:
    /// - Rounds 0-19: 0b1011010100000100111100110011001
    /// - Rounds 20-39: 0b1101110110110011110101110100001
    /// - Rounds 40-59: 0b10001111000110111011110011011100
    /// - Rounds 60-79: 0b11001010011000101100000111010110
    ///
    /// > [!IMPORTANT]
    /// > These constants are critical for the SHA-0 algorithm's security properties.
    /// > Modifying them will break compatibility with standard SHA-0 implementations.
    ///
    /// > [!NOTE]
    /// > The binary representations are used for consistency with the rest of the implementation.
    ///
    /// <example>
    /// <code>
    /// uint constant = GetRoundConstant(round: 15);
    /// // Returns 0b1011010100000100111100110011001
    /// </code>
    /// </example>
    /// </remarks>
    /// <param name="round">The current round number (0-79).</param>
    /// <returns>The round-specific constant as a 32-bit unsigned integer.</returns>
    private static uint GetRoundConstant(int round) =>
        round switch
        {
            < 0b10100 => 0b1011010100000100111100110011001,
            < 0b101000 => 0b1101110110110011110101110100001,
            < 0b111100 => 0b10001111000110111011110011011100,
            _ => 0b11001010011000101100000111010110,
        };

    /// <summary>
    /// Computes the hash value for the specified byte array using the provided hash algorithm.
    /// </summary>
    /// <remarks>
    /// This method calculates a cryptographic hash of the input data using the specified algorithm.
    /// The resulting hash is converted to a lowercase hexadecimal string representation.
    ///
    /// > [!IMPORTANT]
    /// > The method returns null if the input byte array is empty (length = 0).
    ///
    /// > [!NOTE]
    /// > The hash computation is performed synchronously. For large inputs, consider using
    /// > asynchronous alternatives to avoid potential thread blocking.
    ///
    /// <example>
    /// <code>
    /// byte[] data = Encoding.UTF8.GetBytes("example");
    /// using var sha256 = SHA256.Create();
    /// string hash = HashAlgorithm(inputBytes: data, algorithm: sha256);
    /// // Returns "50d858e0985ecc7f60418aaf0cc5ab587f42c2570a884095a9e8ccacd0f6545c"
    /// </code>
    /// </example>
    /// </remarks>
    /// <param name="inputBytes">The input data to compute the hash for.</param>
    /// <param name="algorithm">The hash algorithm implementation to use.</param>
    /// <returns>
    /// A lowercase hexadecimal string representation of the computed hash, or null if input is empty.
    /// </returns>
    private static string? HashAlgorithm(byte[] inputBytes, HashAlgorithm algorithm) =>
        inputBytes.Length > 0
            ? BitConverter
                .ToString(value: algorithm.ComputeHash(buffer: inputBytes))
                .Replace(oldValue: "-", newValue: "")
                .ToLower(System.Globalization.CultureInfo.CurrentCulture)
            : null;

    /// <summary>
    /// Retrieves the byte array to be hashed, combining the input string with optional key and salt.
    /// </summary>
    /// <remarks>
    /// This method concatenates the input string with the key and salt (if provided)
    /// to create a byte array for hashing. The order of concatenation is input + key + salt.
    ///
    /// > [!NOTE]
    /// > If both key and salt are null or empty, the method returns the byte representation of the input string.
    ///
    /// > [!IMPORTANT]
    /// > Using a unique salt for each hash operation is crucial for security to prevent rainbow table attacks.
    ///
    /// <example>
    /// <code>
    /// byte[] hashBytes = GetHashBytes(input: "example", key: "myKey", salt: "mySalt");
    /// // Returns a byte array representing the combined input, key, and salt.
    /// </code>
    /// </example>
    /// </remarks>
    /// <param name="input">The input string to be hashed.</param>
    /// <param name="key">The optional key to be combined with the input string.</param>
    /// <param name="salt">The optional salt to be combined with the input string and key.</param>
    /// <returns>
    /// A byte array representing the combined input, key, and salt,
    /// or just the input if key and salt are not provided.
    /// </returns>
    private static byte[] GetHashBytes(string input, string? key, string? salt)
    {
        var inputBytes = Encoding.UTF8.GetBytes(s: input);
        byte[] bytesToHash;

        if (!string.IsNullOrWhiteSpace(value: key) || !string.IsNullOrWhiteSpace(value: salt))
        {
            var keyBytes = Encoding.UTF8.GetBytes(s: key ?? string.Empty);
            var saltBytes = Encoding.UTF8.GetBytes(s: salt ?? string.Empty);

            bytesToHash = new byte[inputBytes.Length + keyBytes.Length + saltBytes.Length];
            Array.Copy(
                sourceArray: inputBytes,
                destinationArray: bytesToHash,
                length: inputBytes.Length
            );
            Array.Copy(
                sourceArray: keyBytes,
                sourceIndex: 0,
                destinationArray: bytesToHash,
                destinationIndex: inputBytes.Length,
                length: keyBytes.Length
            );
            Array.Copy(
                sourceArray: saltBytes,
                sourceIndex: 0,
                destinationArray: bytesToHash,
                destinationIndex: inputBytes.Length + keyBytes.Length,
                length: saltBytes.Length
            );
        }
        else
        {
            bytesToHash = inputBytes;
        }

        return bytesToHash;
    }

    /// <summary>
    /// Encodes a string to its equivalent base64 string representation.
    /// </summary>
    /// <remarks>
    /// This method converts a plain text string into its base64 encoded equivalent.
    /// It is commonly used for encoding data for transmission across networks or
    /// storing binary data in text format.
    ///
    /// > [!NOTE]
    /// > The method returns null if the input string is null or empty.
    ///
    /// > [!CAUTION]
    /// > Ensure that the input string is properly encoded (e.g., UTF-8) to avoid
    /// > character corruption during the encoding process.
    ///
    /// <example>
    /// <code>
    /// string plainText = "example";
    /// string base64String = plainText.AsBase64Encode();
    /// // Returns "ZXhhbXBsZQ=="
    /// </code>
    /// </example>
    /// </remarks>
    /// <param name="plainText">The string to encode.</param>
    /// <returns>
    /// The base64 encoded string, or null if the input string is null or empty.
    /// </returns>
    public static string? AsBase64Encode(this string? plainText) =>
        !string.IsNullOrWhiteSpace(value: plainText)
            ? Convert.ToBase64String(inArray: Encoding.UTF8.GetBytes(s: plainText))
            : null;

    /// <summary>
    /// Decodes a base64 encoded string to its original string representation.
    /// </summary>
    /// <remarks>
    /// This method converts a base64 encoded string back into its original plain text form.
    /// It is the reverse operation of base64 encoding and is used to retrieve the
    /// original data from an encoded string.
    ///
    /// > [!NOTE]
    /// > The method returns null if the input string is null or empty.
    ///
    /// > [!CAUTION]
    /// > Ensure that the input string is a valid base64 encoded string to avoid
    /// > exceptions during the decoding process.
    ///
    /// <example>
    /// <code>
    /// string base64String = "ZXhhbXBsZQ==";
    /// string plainText = base64String.AsBase64Decode();
    /// // Returns "example"
    /// </code>
    /// </example>
    /// </remarks>
    /// <param name="base64EncodedData">The base64 encoded string to decode.</param>
    /// <returns>
    /// The decoded string, or null if the input string is null or empty.
    /// </returns>
    public static string? AsBase64Decode(this string? base64EncodedData) =>
        !string.IsNullOrWhiteSpace(value: base64EncodedData)
            ? Encoding.UTF8.GetString(bytes: Convert.FromBase64String(s: base64EncodedData))
            : null;

    /// <summary>
    /// Computes the SHA-0 hash of the input string, combined with an optional key.
    /// </summary>
    /// <remarks>
    /// This method calculates the SHA-0 hash of the input string. It concatenates the input string
    /// with the provided key before hashing. SHA-0 is an early version of the SHA family
    /// of hash functions and has known security weaknesses.
    ///
    /// > [!WARNING]
    /// > SHA-0 is considered cryptographically broken and should not be used for secure applications.
    /// > This method is provided for legacy support only.
    ///
    /// > [!NOTE]
    /// > The method returns null if the input string is null or empty.
    ///
    /// <example>
    /// <code>
    /// string data = "example";
    /// string key = "myKey";
    /// string hash = data.AsSHA0(key: key);
    /// // Returns "da39a3ee5e6b4b0d3255bfef95601890afd80709"
    /// </code>
    /// </example>
    /// </remarks>
    /// <param name="input">The input string to hash.</param>
    /// <param name="key">The optional key to combine with the input string.</param>
    /// <returns>
    /// The SHA-0 hash as a hexadecimal string, or null if the input string is null or empty.
    /// </returns>
    public static string? AsSHA0(this string? input, string? key)
    {
        if (string.IsNullOrWhiteSpace(value: input))
            return null;

        (byte[] inputBytes, byte[] keyBytes) = (
            Encoding.UTF8.GetBytes(s: input),
            Encoding.UTF8.GetBytes(s: key ?? string.Empty)
        );

        byte[] bytes = new byte[inputBytes.Length + keyBytes.Length];
        Array.Copy(sourceArray: inputBytes, destinationArray: bytes, length: inputBytes.Length);
        Array.Copy(
            sourceArray: keyBytes,
            sourceIndex: 0,
            destinationArray: bytes,
            destinationIndex: inputBytes.Length,
            length: keyBytes.Length
        );

        (uint h0, uint h1, uint h2, uint h3, uint h4) = (
            0b1100111010001010010001100000001,
            0b11101111110011011010101110001001,
            0b10011000101110101101110011111110,
            0b10000001100100101010001110110,
            0b11000011110100101110000111110000
        );

        int originalLength = bytes.Length;
        int paddedLength = ((originalLength + 0b1000) / 0b1000000 + 0b1) * 0b1000000;
        byte[] padded = new byte[paddedLength];
        Array.Copy(sourceArray: bytes, destinationArray: padded, length: originalLength);
        padded[originalLength] = 0b10000000;

        ulong bitLength = (ulong)originalLength * 0b1000;
        for (int i = 0b0; i < 0b1000; i++)
            padded[paddedLength - 0b1 - i] = (byte)(bitLength >> (i * 0b1000));

        uint[] bitWidth = new uint[0b1010000];
        for (int i = 0b0; i < paddedLength; i += 0b1000000)
        {
            for (int t = 0b0; t < 0b10000; t++)
                bitWidth[t] =
                    (uint)(padded[i + t * 0b100] << 0b11000)
                    | (uint)(padded[i + t * 0b100 + 0b1] << 0b10000)
                    | (uint)(padded[i + t * 0b100 + 0b10] << 0b1000)
                    | padded[i + t * 0b100 + 0b11];

            for (int t = 0b10000; t < 0b1010000; t++)
                bitWidth[t] = LeftRotate(
                    value: bitWidth[t - 0b11]
                        ^ bitWidth[t - 0b1000]
                        ^ bitWidth[t - 0b1110]
                        ^ bitWidth[t - 0b10000],
                    bits: 0b1
                );

            (uint a, uint b, uint c, uint d, uint e) = (h0, h1, h2, h3, h4);

            for (int t = 0b0; t < 0b1010000; t++)
            {
                (e, d, c, b) = (d, c, LeftRotate(value: b, bits: 0b11110), a);
                a =
                    LeftRotate(value: a, bits: 0b101)
                    + CalculateBitwiseOperation(threshold: t, byte1: b, byte2: c, byte3: d)
                    + e
                    + bitWidth[t]
                    + GetRoundConstant(round: t);
            }

            (h0, h1, h2, h3, h4) = (h0 + a, h1 + b, h2 + c, h3 + d, h4 + e);
        }

        return $"{h0:x8}{h1:x8}{h2:x8}{h3:x8}{h4:x8}";
    }

    /// <summary>
    /// Computes the SHA-1 hash of the input string, combined with an optional key and salt.
    /// </summary>
    /// <remarks>
    /// This method calculates the SHA-1 hash of the input string. It concatenates the input string
    /// with the provided key and salt before hashing. SHA-1 is a widely used hash function,
    /// though it has been superseded by stronger algorithms due to security concerns.
    ///
    /// > [!WARNING]
    /// > SHA-1 is considered cryptographically weak and should not be used for secure applications.
    /// > This method is provided for legacy support only.
    ///
    /// > [!NOTE]
    /// > The method returns null if the input string is null or empty.
    ///
    /// <example>
    /// <code>
    /// string data = "example";
    /// string key = "myKey";
    /// string salt = "mySalt";
    /// string hash = data.AsSHA1(key: key, salt: salt);
    /// // Returns "****************************************"
    /// </code>
    /// </example>
    /// </remarks>
    /// <param name="input">The input string to hash.</param>
    /// <param name="key">The optional key to combine with the input string.</param>
    /// <param name="salt">The optional salt to combine with the input string and key.</param>
    /// <returns>
    /// The SHA-1 hash as a hexadecimal string, or null if the input string is null or empty.
    /// </returns>
    public static string? AsSHA1(this string? input, string? key, string? salt = null)
    {
        if (string.IsNullOrWhiteSpace(value: input))
            return null;

        using var algorithm = SHA1.Create();
        return HashAlgorithm(
            inputBytes: GetHashBytes(input: input, key: key, salt: salt),
            algorithm: algorithm
        );
    }

    /// <summary>
    /// Computes the SHA-224 hash of the input string, combined with an optional key and salt.
    /// </summary>
    /// <remarks>
    /// This method calculates the SHA-224 hash of the input string. It concatenates the input
    /// string with the provided key and salt before hashing. SHA-224 is a member of the SHA-2
    /// family of hash functions and provides a 224-bit hash value.
    ///
    /// > [!NOTE]
    /// > The method returns null if the input string is null or empty.
    ///
    /// > [!WARNING]
    /// > While SHA-224 is more secure than SHA-1 and SHA-0, ensure it meets your application's
    /// > security requirements. Consider using stronger SHA-2 variants like SHA-256 or SHA-512
    /// > for sensitive applications.
    ///
    /// <example>
    /// <code>
    /// string data = "example";
    /// string key = "myKey";
    /// string salt = "mySalt";
    /// string hash = data.AsSHA224(key: key, salt: salt);
    /// // Returns "4d4ee783146b996789687e32528488c1dca43444334ca96bda644dd6"
    /// </code>
    /// </example>
    /// </remarks>
    /// <param name="input">The input string to hash.</param>
    /// <param name="key">The optional key to combine with the input string.</param>
    /// <param name="salt">The optional salt to combine with the input string and key.</param>
    /// <returns>
    /// The SHA-224 hash as a hexadecimal string, or null if the input string is null or empty.
    /// </returns>
    public static string? AsSHA224(this string? input, string? key, string? salt = null)
    {
        if (string.IsNullOrWhiteSpace(value: input))
            return null;

        using var algorithm = SHA256.Create();
        return HashAlgorithm(
            inputBytes: GetHashBytes(input: input, key: key, salt: salt),
            algorithm: algorithm
        );
    }

    /// <summary>
    /// Computes the SHA-256 hash of the input string, combined with an optional key and salt.
    /// </summary>
    /// <remarks>
    /// This method calculates the SHA-256 hash of the input string. It concatenates the input
    /// string with the provided key and salt before hashing. SHA-256 is a member of the SHA-2
    /// family of hash functions and is widely used for its strong security properties.
    ///
    /// > [!NOTE]
    /// > The method returns null if the input string is null or empty.
    ///
    /// > [!CAUTION]
    /// > Ensure that the key and salt are securely generated and stored to maintain the
    /// > integrity of the hash.
    ///
    /// <example>
    /// <code>
    /// string data = "example";
    /// string key = "myKey";
    /// string salt = "mySalt";
    /// string hash = data.AsSHA256(key: key, salt: salt);
    /// // Returns "2692bce46e55649aa942439ff586954532970aa9c6a34a143974ca9c7c962963"
    /// </code>
    /// </example>
    /// </remarks>
    /// <param name="input">The input string to hash.</param>
    /// <param name="key">The optional key to combine with the input string.</param>
    /// <param name="salt">The optional salt to combine with the input string and key.</param>
    /// <returns>
    /// The SHA-256 hash as a hexadecimal string, or null if the input string is null or empty.
    /// </returns>
    public static string? AsSHA256(this string? input, string? key, string? salt = null)
    {
        if (string.IsNullOrWhiteSpace(value: input))
            return null;

        using var algorithm = SHA256.Create();
        return HashAlgorithm(
            inputBytes: GetHashBytes(input: input, key: key, salt: salt),
            algorithm: algorithm
        );
    }

    /// <summary>
    /// Computes the SHA-384 hash of the input string, combined with an optional key and salt.
    /// </summary>
    /// <remarks>
    /// This method calculates the SHA-384 hash of the input string. It concatenates the input
    /// string with the provided key and salt before hashing. SHA-384 is a member of the SHA-2
    /// family of hash functions and provides a 384-bit hash value.
    ///
    /// > [!NOTE]
    /// > The method returns null if the input string is null or empty.
    ///
    /// > [!CAUTION]
    /// > Ensure that the key and salt are securely generated and stored to maintain the
    /// > integrity of the hash.
    ///
    /// <example>
    /// <code>
    /// string data = "example";
    /// string key = "myKey";
    /// string salt = "mySalt";
    /// string hash = data.AsSHA384(key: key, salt: salt);
    /// // Returns "5922fa9544c93b428941d999869891cb2aa91f152f5c90bca9e367e68466ca9c6c7a97f534ca1ded95b9991ba9e30993"
    /// </code>
    /// </example>
    /// </remarks>
    /// <param name="input">The input string to hash.</param>
    /// <param name="key">The optional key to combine with the input string.</param>
    /// <param name="salt">The optional salt to combine with the input string and key.</param>
    /// <returns>
    /// The SHA-384 hash as a hexadecimal string, or null if the input string is null or empty.
    /// </returns>
    public static string? AsSHA384(this string? input, string? key, string? salt = null)
    {
        if (string.IsNullOrWhiteSpace(value: input))
            return null;

        using var algorithm = SHA384.Create();
        return HashAlgorithm(
            inputBytes: GetHashBytes(input: input, key: key, salt: salt),
            algorithm: algorithm
        );
    }

    /// <summary>
    /// Computes the SHA-512 hash of the input string, combined with an optional key and salt.
    /// </summary>
    /// <remarks>
    /// This method calculates the SHA-512 hash of the input string. It concatenates the input
    /// string with the provided key and salt before hashing. SHA-512 is a member of the SHA-2
    /// family of hash functions and provides a 512-bit hash value, offering a high level of
    /// security.
    ///
    /// > [!NOTE]
    /// > The method returns null if the input string is null or empty.
    ///
    /// > [!CAUTION]
    /// > Ensure that the key and salt are securely generated and stored to maintain the
    /// > integrity of the hash.
    ///
    /// <example>
    /// <code>
    /// string data = "example";
    /// string key = "myKey";
    /// string salt = "mySalt";
    /// string hash = data.AsSHA512(key: key, salt: salt);
    /// // Returns "f764c9ff7099c9649948527993369a92dd260a869f936a4547b116b9054c3c447b9c3b8a4a99dca3c77efc5bd6ca49583946c9db53d9c9c1ca65c31ca963f1b6"
    /// </code>
    /// </example>
    /// </remarks>
    /// <param name="input">The input string to hash.</param>
    /// <param name="key">The optional key to combine with the input string.</param>
    /// <param name="salt">The optional salt to combine with the input string and key.</param>
    /// <returns>
    /// The SHA-512 hash as a hexadecimal string, or null if the input string is null or empty.
    /// </returns>
    public static string? AsSHA512(this string? input, string? key, string? salt = null)
    {
        if (string.IsNullOrWhiteSpace(value: input))
            return null;

        using var algorithm = SHA512.Create();
        return HashAlgorithm(
            inputBytes: GetHashBytes(input: input, key: key, salt: salt),
            algorithm: algorithm
        );
    }

    /// <summary>
    /// Encrypts a string using RSA encryption with the specified public key.
    /// </summary>
    /// <remarks>
    /// This method encrypts the input string using the RSA algorithm. The public key,
    /// provided in XML format, is used to encrypt the plain text. The encrypted data
    /// is then converted to a base64 string for easy storage or transmission.
    ///
    /// > [!NOTE]
    /// > The public key must be in XML format and compatible with the .NET RSA implementation.
    ///
    /// > [!CAUTION]
    /// > RSA encryption should be used judiciously, as it is computationally intensive.
    /// > Ensure the public key is securely managed to prevent unauthorized encryption.
    ///
    /// <example>
    /// <code>
    /// string publicKey = "&lt;RSAKeyValue&gt;&lt;Modulus&gt;...&lt;/Modulus&gt;&lt;Exponent&gt;AQAB&lt;/Exponent&gt;&lt;/RSAKeyValue&gt;";
    /// string plainText = "example";
    /// string cipherText = plainText.AsRSAEncrypt(publicKey: publicKey);
    /// // Returns "KTFmKRyc9jKa4mKy...""
    /// </code>
    /// </example>
    /// </remarks>
    /// <param name="plainText">The string to encrypt.</param>
    /// <param name="publicKey">The RSA public key in XML format.</param>
    /// <returns>
    /// The base64 encoded encrypted string, or null if the input string or public key is null or empty.
    /// </returns>
    /// <exception cref="System.Security.Cryptography.CryptographicException">
    /// Thrown when the encryption fails due to invalid key or other cryptographic errors.
    /// </exception>
    public static string? AsRSAEncrypt(this string? plainText, string publicKey)
    {
        if (
            string.IsNullOrWhiteSpace(value: plainText)
            || string.IsNullOrWhiteSpace(value: publicKey)
        )
            return null;

        using var rsa = RSA.Create();
        rsa.FromXmlString(xmlString: publicKey);

        return Convert.ToBase64String(
            inArray: rsa.Encrypt(
                data: Encoding.UTF8.GetBytes(s: plainText),
                padding: RSAEncryptionPadding.Pkcs1
            )
        );
    }

    /// <summary>
    /// Decrypts a base64 encoded string using RSA decryption with the specified private key.
    /// </summary>
    /// <remarks>
    /// This method decrypts a base64 encoded string using the RSA algorithm. The private key,
    /// provided in XML format, is used to decrypt the cipher text. The decrypted data
    /// is then converted to a UTF-8 string.
    ///
    /// > [!NOTE]
    /// > The private key must be in XML format and compatible with the .NET RSA implementation.
    ///
    /// > [!CAUTION]
    /// > RSA decryption should be used judiciously, as it is computationally intensive.
    /// > Ensure the private key is securely managed to prevent unauthorized decryption.
    ///
    /// <example>
    /// <code>
    /// string privateKey = "&lt;RSAKeyValue&gt;&lt;Modulus&gt;...&lt;/Modulus&gt;&lt;D&gt;...&lt;/D&gt;&lt;/RSAKeyValue&gt;";
    /// string cipherText = "KTFmKRyc9jKa4mKy...";
    /// string plainText = cipherText.AsRSADecrypt(privateKey: privateKey);
    /// // Returns "example"
    /// </code>
    /// </example>
    /// </remarks>
    /// <param name="cipherText">The base64 encoded string to decrypt.</param>
    /// <param name="privateKey">The RSA private key in XML format.</param>
    /// <returns>
    /// The decrypted string, or null if the input string or private key is null or empty.
    /// </returns>
    /// <exception cref="System.Security.Cryptography.CryptographicException">
    /// Thrown when the decryption fails due to invalid key or other cryptographic errors.
    /// </exception>
    public static string? AsRSADecrypt(this string? cipherText, string privateKey)
    {
        if (
            string.IsNullOrWhiteSpace(value: cipherText)
            || string.IsNullOrWhiteSpace(value: privateKey)
        )
            return null;

        using var rsa = RSA.Create();
        rsa.FromXmlString(xmlString: privateKey);

        return Encoding.UTF8.GetString(
            bytes: rsa.Decrypt(
                data: Convert.FromBase64String(s: cipherText),
                padding: RSAEncryptionPadding.Pkcs1
            )
        );
    }

    /// <summary>
    /// Encrypts a string using AES encryption with the specified key and initialization vector (IV).
    /// </summary>
    /// <remarks>
    /// This method encrypts the input string using the AES (Advanced Encryption Standard) algorithm.
    /// AES is a symmetric block cipher widely used for securing sensitive data.
    /// The method requires a key for encryption and an optional initialization vector (IV).
    ///
    /// > [!NOTE]
    /// > The key must be of appropriate length (16, 24, or 32 bytes for AES-128, AES-192, or AES-256, respectively).
    /// > If the IV is not provided, a default IV of 16 zero bytes is used.
    ///
    /// > [!CAUTION]
    /// > Ensure that the key is securely generated and stored. The same key must be used for decryption.
    /// > The IV should be unique for each encryption operation to enhance security.
    ///
    /// <example>
    /// <code>
    /// string plainText = "example";
    /// string key = "1234567890123456"; // 16-byte key for AES-128
    /// string iv = "abcdefghijklmnop"; // 16-byte IV
    /// string cipherText = plainText.AsAESEncrypt(key: key, iv: iv);
    /// // Returns "j+repF8qKds5wc4Bw7w5UA=="
    /// </code>
    /// </example>
    /// </remarks>
    /// <param name="plainText">The string to encrypt.</param>
    /// <param name="key">The encryption key.</param>
    /// <param name="iv">The optional initialization vector (IV). If null, a default IV is used.</param>
    /// <returns>
    /// The base64 encoded encrypted string, or null if the input string or key is null or empty.
    /// </returns>
    public static string? AsAESEncrypt(this string? plainText, string key, string? iv = null)
    {
        if (string.IsNullOrWhiteSpace(value: plainText) || string.IsNullOrWhiteSpace(value: key))
            return null;

        using var aes = Aes.Create();
        aes.Key = Encoding.UTF8.GetBytes(s: key);
        aes.IV = string.IsNullOrWhiteSpace(value: iv)
            ? new byte[16]
            : [.. Encoding.UTF8.GetBytes(s: iv).Take(count: 16)];

        using var encryptor = aes.CreateEncryptor();
        using var ms = new MemoryStream();
        using var cs = new CryptoStream(
            stream: ms,
            transform: encryptor,
            mode: CryptoStreamMode.Write
        );
        using (var sw = new StreamWriter(stream: cs))
        {
            sw.Write(value: plainText);
        }
        return Convert.ToBase64String(inArray: ms.ToArray());
    }

    /// <summary>
    /// Decrypts a string using AES decryption with the specified key and initialization vector (IV).
    /// </summary>
    /// <remarks>
    /// This method decrypts a string that was previously encrypted using the AES (Advanced Encryption Standard) algorithm.
    /// AES is a symmetric block cipher widely used for securing sensitive data.
    /// The method requires the same key that was used for encryption and an optional initialization vector (IV).
    ///
    /// > [!NOTE]
    /// > The key must be of appropriate length (16, 24, or 32 bytes for AES-128, AES-192, or AES-256, respectively),
    /// > and it must match the key used during encryption.
    /// > If the IV is not provided, a default IV of 16 zero bytes is used.
    ///
    /// > [!CAUTION]
    /// > Ensure that the key is securely stored and that the IV, if used, matches the one used during encryption.
    /// > Using the wrong key or IV will result in incorrect decryption.
    ///
    /// <example>
    /// <code>
    /// string cipherText = "j+repF8qKds5wc4Bw7w5UA==";
    /// string key = "1234567890123456"; // 16-byte key for AES-128
    /// string iv = "abcdefghijklmnop"; // 16-byte IV
    /// string plainText = cipherText.AsAESDecrypt(key: key, iv: iv);
    /// // Returns "example"
    /// </code>
    /// </example>
    /// </remarks>
    /// <param name="cipherText">The base64 encoded string to decrypt.</param>
    /// <param name="key">The decryption key.</param>
    /// <param name="iv">The optional initialization vector (IV). If null, a default IV is used.</param>
    /// <returns>
    /// The decrypted string, or null if the input string or key is null or empty.
    /// </returns>
    public static string? AsAESDecrypt(this string? cipherText, string key, string? iv = null)
    {
        if (string.IsNullOrWhiteSpace(value: cipherText) || string.IsNullOrWhiteSpace(value: key))
            return null;

        using var aes = Aes.Create();
        aes.Key = Encoding.UTF8.GetBytes(s: key);
        aes.IV = string.IsNullOrWhiteSpace(value: iv)
            ? new byte[16]
            : [.. Encoding.UTF8.GetBytes(s: iv).Take(count: 16)];

        using var decryptor = aes.CreateDecryptor();
        using var ms = new MemoryStream(Convert.FromBase64String(s: cipherText));
        using var cs = new CryptoStream(
            stream: ms,
            transform: decryptor,
            mode: CryptoStreamMode.Read
        );
        using var sr = new StreamReader(stream: cs);
        return sr.ReadToEnd();
    }

    /// <summary>
    /// Encrypts a string using DES encryption with the specified key and initialization vector (IV).
    /// </summary>
    /// <remarks>
    /// This method encrypts the input string using the DES (Data Encryption Standard) algorithm.
    /// DES is a symmetric block cipher that was widely used but is now considered less secure
    /// due to its short key length. The method requires a key for encryption and an optional
    /// initialization vector (IV).
    ///
    /// > [!NOTE]
    /// > The key must be exactly 8 bytes long. If the IV is not provided, a default IV of 8 zero bytes is used.
    ///
    /// > [!WARNING]
    /// > DES is considered cryptographically weak and should not be used for secure applications.
    /// > This method is provided for legacy support only.
    ///
    /// <example>
    /// <code>
    /// string plainText = "example";
    /// byte[] key = { 0x01, 0x23, 0x45, 0x67, 0x89, 0xab, 0xcd, 0xef }; // 8-byte key
    /// byte[] iv = { 0xfe, 0xdc, 0xba, 0x98, 0x76, 0x54, 0x32, 0x10 }; // 8-byte IV
    /// string cipherText = plainText.AsDESEncrypt(key: key, iv: iv);
    /// // Returns "j+repF8qKds5wc4Bw7w5UA=="
    /// </code>
    /// </example>
    /// </remarks>
    /// <param name="plainText">The string to encrypt.</param>
    /// <param name="key">The encryption key, which must be 8 bytes long.</param>
    /// <param name="iv">The optional initialization vector (IV). If null, a default IV is used.</param>
    /// <returns>
    /// The base64 encoded encrypted string, or null if the input string or key is null or empty
    /// or if the key length is not 8 bytes.
    /// </returns>
    public static string? AsDESEncrypt(this string? plainText, byte[] key, byte[]? iv = null)
    {
        if (string.IsNullOrEmpty(value: plainText) || key == null || key.Length != 8)
            return null;

        using var des = DES.Create();
        des.Key = key;
        des.IV = iv ?? new byte[8];

        using var encryptor = des.CreateEncryptor();
        using var ms = new MemoryStream();
        using var cs = new CryptoStream(
            stream: ms,
            transform: encryptor,
            mode: CryptoStreamMode.Write
        );
        using var sw = new StreamWriter(stream: cs);
        sw.Write(value: plainText);
        sw.Flush();
        cs.FlushFinalBlock();

        return Convert.ToBase64String(inArray: ms.ToArray());
    }

    /// <summary>
    /// Decrypts a string using DES decryption with the specified key and initialization vector (IV).
    /// </summary>
    /// <remarks>
    /// This method decrypts a string that was previously encrypted using the DES
    /// (Data Encryption Standard) algorithm.
    /// DES is a symmetric block cipher that was widely used but is now considered
    /// less secure due to its short key length.
    /// The method requires the same key that was used for encryption and an optional
    /// initialization vector (IV).
    ///
    /// > [!NOTE]
    /// > The key must be exactly 8 bytes long, and it must match the key used during encryption.
    /// > If the IV is not provided, a default IV of 8 zero bytes is used.
    ///
    /// > [!WARNING]
    /// > DES is considered cryptographically weak and should not be used for secure applications.
    /// > This method is provided for legacy support only.
    ///
    /// > [!CAUTION]
    /// > Ensure that the key is securely stored and that the IV, if used, matches the one
    /// > used during encryption.
    /// > Using the wrong key or IV will result in incorrect decryption.
    ///
    /// <example>
    /// <code>
    /// byte[] key = { 0x01, 0x23, 0x45, 0x67, 0x89, 0xab, 0xcd, 0xef }; // 8-byte key
    /// byte[] iv = { 0xfe, 0xdc, 0xba, 0x98, 0x76, 0x54, 0x32, 0x10 };   // 8-byte IV
    /// string cipherText = "j+repF8qKds5wc4Bw7w5UA==";
    /// string plainText = cipherText.AsDESDecrypt(key: key, iv: iv);
    /// // Returns "example"
    /// </code>
    /// </example>
    /// </remarks>
    /// <param name="cipherText">The base64 encoded string to decrypt.</param>
    /// <param name="key">The decryption key, which must be 8 bytes long.</param>
    /// <param name="iv">The optional initialization vector (IV). If null, a default IV is used.</param>
    /// <returns>
    /// The decrypted string, or null if the input string or key is null or empty,
    /// or if the key length is not 8 bytes or if decryption fails.
    /// </returns>
    public static string? AsDESDecrypt(this string? cipherText, byte[] key, byte[]? iv = null)
    {
        if (string.IsNullOrWhiteSpace(value: cipherText) || key == null || key.Length != 8)
            return null;

        using var des = DES.Create();
        des.Key = key;
        des.IV = iv ?? new byte[8];

        try
        {
            using var decryptor = des.CreateDecryptor();
            var buffer = Convert.FromBase64String(s: cipherText);
            using var ms = new MemoryStream(
                buffer: buffer,
                index: 0,
                count: buffer.Length,
                writable: false
            );
            using var cs = new CryptoStream(
                stream: ms,
                transform: decryptor,
                mode: CryptoStreamMode.Read
            );
            using var sr = new StreamReader(stream: cs);
            return sr.ReadToEnd();
        }
        catch (CryptographicException)
        {
            return null;
        }
    }

    /// <summary>
    /// Computes the MD5 hash of the input string, combined with an optional key.
    /// </summary>
    /// <remarks>
    /// This method calculates the MD5 hash of the input string. It concatenates the input
    /// string with the provided key before hashing. MD5 is a widely used hash function,
    /// but it is considered cryptographically weak and should not be used for secure
    /// applications.
    ///
    /// > [!NOTE]
    /// > The method returns null if the input string is null or empty.
    ///
    /// > [!CAUTION]
    /// > Ensure that the key is securely generated and stored to maintain the integrity
    /// > of the hash.
    ///
    /// <example>
    /// <code>
    /// string data = "example";
    /// string key = "myKey";
    /// string hash = data.AsMD5(key: key);
    /// // Returns "e4d909c290d0fb1ca068ffaddf22cbd0"
    /// </code>
    /// </example>
    /// </remarks>
    /// <param name="input">The input string to hash.</param>
    /// <param name="key">The optional key to combine with the input string.</param>
    /// <param name="salt">Unused parameter. Do not provide a value for this parameter.</param>
    /// <returns>
    /// The MD5 hash as a hexadecimal string, or null if the input string is null or empty.
    /// </returns>
    public static string? AsMD5(this string? input, string? key, string? salt = null)
    {
        if (string.IsNullOrWhiteSpace(value: input))
            return null;

        using var algorithm = MD5.Create();
        return HashAlgorithm(
            inputBytes: GetHashBytes(input: input, key: key, salt: salt),
            algorithm: algorithm
        );
    }

    private static Rfc2898DeriveBytes GetPdb(this string EncryptionKey)
    {
#pragma warning disable SYSLIB0041 // Type or member is obsolete
        return new(
            password: EncryptionKey,
            salt: [0x49, 0x76, 0x61, 0x6e, 0x20, 0x4d, 0x65, 0x64, 0x76, 0x65, 0x64, 0x65, 0x76]
        );
#pragma warning restore SYSLIB0041 // Type or member is obsolete
    }

    /// <summary>
    ///   Encrypts a string using a legacy encryption algorithm with the specified key.
    /// </summary>
    /// <remarks>
    ///   This method encrypts the input string using a combination of AES encryption
    ///   and a password-based key derivation function (Rfc2898DeriveBytes).
    ///   It is considered a legacy encryptor because it uses a fixed salt and a
    ///   specific number of iterations for key derivation.
    ///   The encrypted data is then converted to a base64 string for easy storage or
    ///   transmission.
    ///
    ///   > [!WARNING]
    ///   > This method is provided for legacy support only. It is recommended to use
    ///   > more secure encryption algorithms and key derivation functions for new
    ///   > applications.
    ///
    ///   > [!NOTE]
    ///   > The encryption key should be a strong, randomly generated string.
    ///
    ///   > [!CAUTION]
    ///   > Ensure that the key is securely stored and managed to prevent unauthorized
    ///   > decryption.
    ///   <example>
    ///     <code>
    ///     string plainText = "example";
    ///     string key = "idxp@rtn3rs";
    ///     string cipherText = plainText.LegacyEncryptor(key: key);
    ///     // Returns "KjCB+L5RgQxLs9Hox8GuJw=="
    ///     </code>
    ///   </example>
    /// </remarks>
    /// <param name="plainText">The string to encrypt.</param>
    /// <param name="key">The encryption key.</param>
    /// <returns>
    ///   The base64 encoded encrypted string.
    /// </returns>
    public static string LegacyEncryptor(this string plainText, string key)
    {
        //string EncryptionKey = "idxp@rtn3rs";
        byte[] clearBytes = Encoding.Unicode.GetBytes(s: plainText);

        using (var encryptor = Aes.Create())
        {
            using (var pdb = key.GetPdb())
            {
                encryptor.Key = pdb.GetBytes(cb: 32);
                encryptor.IV = pdb.GetBytes(cb: 16);
            }

            using var ms = new MemoryStream();
            using (
                var cs = new CryptoStream(
                    stream: ms,
                    transform: encryptor.CreateEncryptor(),
                    mode: CryptoStreamMode.Write
                )
            )
            {
                cs.Write(buffer: clearBytes, offset: 0, count: clearBytes.Length);
            }
            plainText = Convert.ToBase64String(inArray: ms.ToArray());
        }

        return plainText;
    }

    /// <summary>
    ///   Decrypts a string using a legacy encryption algorithm with the specified key.
    /// </summary>
    /// <remarks>
    ///   This method decrypts the input string using a combination of AES decryption
    ///   and a password-based key derivation function (Rfc2898DeriveBytes).
    ///   It is considered a legacy decryptor because it uses a fixed salt and a
    ///   specific number of iterations for key derivation.
    ///   The decrypted data is then converted to a UTF-16 string.
    ///
    ///   > [!WARNING]
    ///   > This method is provided for legacy support only. It is recommended to use
    ///   > more secure encryption algorithms and key derivation functions for new
    ///   > applications.
    ///
    ///   > [!NOTE]
    ///   > The encryption key should be the same key used to encrypt the string.
    ///
    ///   > [!CAUTION]
    ///   > Ensure that the key is securely stored and managed to prevent unauthorized
    ///   > decryption.
    ///   <example>
    ///     <code>
    ///     string cipherText = "KjCB+L5RgQxLs9Hox8GuJw==";
    ///     string key = "idxp@rtn3rs";
    ///     string plainText = cipherText.LegacyDecryptor(key: key);
    ///     // Returns "example"
    ///     </code>
    ///   </example>
    /// </remarks>
    /// <param name="cipherText">The string to decrypt.</param>
    /// <param name="key">The decryption key.</param>
    /// <returns>
    ///   The decrypted string.
    /// </returns>
    public static string LegacyDecryptor(this string cipherText, string key)
    {
        //string EncryptionKey = "0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZ";
        cipherText = cipherText.Replace(oldValue: " ", newValue: "+");
        byte[] cipherBytes = Convert.FromBase64String(s: cipherText);

        using (Aes encryptor = Aes.Create())
        {
            Rfc2898DeriveBytes pdb = key.GetPdb();

            encryptor.Key = pdb.GetBytes(cb: 32);
            encryptor.IV = pdb.GetBytes(cb: 16);

            using var ms = new MemoryStream();
            using (
                var cs = new CryptoStream(
                    stream: ms,
                    transform: encryptor.CreateDecryptor(),
                    mode: CryptoStreamMode.Write
                )
            )
            {
                cs.Write(buffer: cipherBytes, offset: 0, count: cipherBytes.Length);
                cs.FlushFinalBlock();
            }

            cipherText = Encoding.Unicode.GetString(bytes: ms.ToArray());
        }

        return cipherText;
    }
}
